"use client";

import { useState, useEffect } from "react";
import { useAuth } from "./useAuth";
import { getUserProfile, updateUserProfile, type UserProfile } from "@/Services/userService";

export function useUser() {
  const { user: authUser, loading: authLoading } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user profile when auth user changes
  useEffect(() => {
    async function fetchUserProfile() {
      if (!authUser) {
        setUserProfile(null);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const profile = await getUserProfile(authUser.uid);
        setUserProfile(profile);
      } catch (err) {
        console.error("Error fetching user profile:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch user profile");
      } finally {
        setLoading(false);
      }
    }

    if (!authLoading) {
      fetchUserProfile();
    }
  }, [authUser, authLoading]);

  // Update user profile
  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!authUser || !userProfile) return;

    try {
      setError(null);
      await updateUserProfile(authUser.uid, updates);
      setUserProfile(prev => prev ? { ...prev, ...updates } : null);
    } catch (err) {
      console.error("Error updating user profile:", err);
      setError(err instanceof Error ? err.message : "Failed to update profile");
      throw err;
    }
  };

  // Refresh user profile
  const refreshProfile = async () => {
    if (!authUser) return;

    try {
      setError(null);
      const profile = await getUserProfile(authUser.uid);
      setUserProfile(profile);
    } catch (err) {
      console.error("Error refreshing user profile:", err);
      setError(err instanceof Error ? err.message : "Failed to refresh profile");
    }
  };

  return {
    // User data
    user: userProfile,
    authUser,
    
    // Loading states
    loading: authLoading || loading,
    
    // Error state
    error,
    
    // Actions
    updateProfile,
    refreshProfile,
    
    // Computed properties
    isAuthenticated: !!authUser,
    hasProfile: !!userProfile,
  };
}
