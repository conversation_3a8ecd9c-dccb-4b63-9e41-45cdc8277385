"use client";
import { useEffect, useState } from "react";
import { onSnapshot, type DocumentData } from "firebase/firestore";
import { getDocRef } from "@/Services/firestoreService";

export function useFirestoreDoc<T = DocumentData>(path: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    setLoading(true);
    const ref = getDocRef<T>(path);
    const unsub = onSnapshot(
      ref,
      (snap) => {
        setData(snap.exists() ? (snap.data() as T) : null);
        setLoading(false);
      },
      (err) => {
        setError(err);
        setLoading(false);
      }
    );
    return () => unsub();
  }, [path]);

  return { data, loading, error } as const;
}


