"use client";

import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@/hooks/useUser";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { useEffect } from "react";
import { DtcSidebar } from "@/components/ui/navigation/DtcSidebar";
import { HeroSection } from "@/components/ui/HeroSection";
import { cn } from "@/lib/utils";
import { type DtcAssetKey } from "@/lib/assets";

interface DashboardLayoutProps {
  children: React.ReactNode;
  heroImage?: DtcAssetKey;
  heroTitle?: string;
  heroSubtitle?: string;
  heroContent?: React.ReactNode;
  className?: string;
  showHero?: boolean;
}

export function DashboardLayout({
  children,
  heroImage = "hero1",
  heroTitle,
  heroSubtitle,
  heroContent,
  className,
  showHero = true
}: DashboardLayoutProps) {
  const { user: authUser, loading: authLoading } = useAuth();
  const { user: userProfile, loading: userLoading } = useUser();
  const router = useRouter();
  const locale = useLocale();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !authUser) {
      router.push(`/${locale}/login`);
    }
  }, [authUser, authLoading, router, locale]);

  // Show loading state while checking authentication
  if (authLoading || userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[--color-muted]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-[--color-primary] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[--color-grey]">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (!authUser) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[--color-muted] flex">
      {/* Sidebar */}
      <DtcSidebar />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col lg:ml-0">
        {/* Hero Section */}
        {showHero && (
          <div className="p-6 pb-0">
            <HeroSection
              heroImage={heroImage}
              title={heroTitle}
              subtitle={heroSubtitle}
              overlayContent={heroContent}
              height="md"
            />
          </div>
        )}

        {/* Page Content */}
        <main className={cn(
          "flex-1 p-6",
          !showHero && "pt-20 lg:pt-6", // Add top padding when no hero (for mobile menu button)
          className
        )}>
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
