"use client";
import type { ButtonHTMLAttributes, ReactNode } from "react";

type Props = ButtonHTMLAttributes<HTMLButtonElement> & {
  variant?: "primary" | "outline" | "ghost";
  asChild?: boolean;
  children?: ReactNode;
};

export default function BrandButton({ variant = "primary", className = "", asChild = false, children, ...props }: Props) {
  const base = "inline-flex items-center justify-center rounded-xl transition-colors px-5 py-3 text-sm font-semibold";
  const styles = {
    primary: "bg-[--color-primary] text-white hover:bg-[--color-primary-deep]",
    outline: "border border-[--color-border] text-[--color-charcoal] hover:bg-[--color-muted]",
    ghost: "text-[--color-primary] hover:bg-[--color-muted]",
  }[variant];

  if (asChild && children) {
    return (
      <span className={`${base} ${styles} ${className}`} role="button">
        {children}
      </span>
    );
  }
  return (
    <button className={`${base} ${styles} ${className}`} {...props}>
      {children}
    </button>
  );
}


