"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { Home, Users, LogOut, Menu } from "lucide-react";
import { cn } from "@/lib/utils";
import { dtcAssets } from "@/lib/assets";
import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@/hooks/useUser";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useState } from "react";
import { toast } from "sonner";

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: string[]; // Roles that can access this item
}

interface DtcSidebarProps {
  className?: string;
}

export function DtcSidebar({ className }: DtcSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const { signOut } = useAuth();
  const { user } = useUser();
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // Navigation items based on user roles
  const navigationItems: NavigationItem[] = [
    {
      label: "Home",
      href: `/${locale}/dashboard`,
      icon: Home,
      roles: ["Admin", "DTC"], // Both roles can access
    },
    {
      label: "User Management",
      href: `/${locale}/dashboard/users`,
      icon: Users,
      roles: ["Admin"], // Only Admin can access
    },
  ];

  // Filter navigation items based on user role
  const filteredNavItems = navigationItems.filter(item =>
    user?.role && item.roles.includes(user.role)
  );

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
      router.push(`/${locale}/login`);
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Failed to sign out");
    }
  };

  const SidebarContent = () => (
    <div className="flex flex-col h-full bg-white border-r border-[--color-border]">
      {/* Logo Section */}
      <div className="flex items-center justify-center p-6 border-b border-[--color-border]">
        <Image
          src={dtcAssets.logoBlack}
          alt="DTC Logo"
          width={120}
          height={40}
          className="object-contain"
          priority
        />
      </div>

      {/* User Info Section */}
      <div className="p-4 border-b border-[--color-border]">
        <div className="flex items-center space-x-3">
          {user?.photoURL ? (
            <Image
              src={user.photoURL}
              alt="User Avatar"
              width={40}
              height={40}
              className="rounded-full"
            />
          ) : (
            <div className="w-10 h-10 bg-[--color-primary] rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {user?.displayName?.charAt(0) || user?.email?.charAt(0) || "U"}
              </span>
            </div>
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-[--color-charcoal] truncate">
              {user?.displayName || user?.email}
            </p>
            <p className="text-xs text-[--color-grey] capitalize">
              {user?.role || "User"}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Section */}
      <nav className="flex-1 p-4 space-y-2">
        {filteredNavItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => setIsMobileOpen(false)}
              className={cn(
                "flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200",
                "hover:bg-[--color-muted] group",
                isActive
                  ? "bg-[--color-primary] text-white shadow-sm"
                  : "text-[--color-charcoal] hover:text-[--color-primary]"
              )}
            >
              <Icon
                className={cn(
                  "w-5 h-5 transition-colors",
                  isActive
                    ? "text-white"
                    : "text-[--color-grey] group-hover:text-[--color-primary]"
                )}
              />
              <span className="font-medium text-sm">{item.label}</span>
            </Link>
          );
        })}
      </nav>

      {/* Sign Out Section */}
      <div className="p-4 border-t border-[--color-border]">
        <Button
          onClick={handleSignOut}
          variant="ghost"
          className="w-full justify-start space-x-3 px-4 py-3 text-[--color-charcoal] hover:text-red-600 hover:bg-red-50"
        >
          <LogOut className="w-5 h-5" />
          <span className="font-medium text-sm">Sign Out</span>
        </Button>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile Trigger */}
      <div className="lg:hidden">
        <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed top-4 left-4 z-50 bg-white shadow-md border border-[--color-border]"
            >
              <Menu className="w-5 h-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-80">
            <SidebarContent />
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Sidebar */}
      <div className={cn("hidden lg:flex lg:w-80 lg:flex-shrink-0", className)}>
        <SidebarContent />
      </div>
    </>
  );
}
