import type { ReactNode, ElementType, ComponentPropsWithoutRef } from "react";

type Props<T extends ElementType = "div"> = {
  children: ReactNode;
  className?: string;
  as?: T;
} & ComponentPropsWithoutRef<T>;

export default function Card<T extends ElementType = "div">({
  children,
  className = "",
  as,
  ...props
}: Props<T>) {
  const Tag = (as ?? ("div" as ElementType)) as ElementType;
  return (
    <Tag className={`bg-white border border-[--color-border] shadow-sm rounded-2xl ${className}`} {...props}>
      {children}
    </Tag>
  );
}


