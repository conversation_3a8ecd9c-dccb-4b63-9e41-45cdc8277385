"use client";

import Image from "next/image";
import { cn } from "@/lib/utils";
import { dtcAssets, type Dtc<PERSON>set<PERSON>ey } from "@/lib/assets";

interface HeroSectionProps {
  heroImage?: DtcAssetKey;
  title?: string;
  subtitle?: string;
  overlayContent?: React.ReactNode;
  className?: string;
  height?: "sm" | "md" | "lg" | "xl";
}

export function HeroSection({
  heroImage = "hero1",
  title,
  subtitle,
  overlayContent,
  className,
  height = "md"
}: HeroSectionProps) {
  const heightClasses = {
    sm: "h-48",
    md: "h-64",
    lg: "h-80",
    xl: "h-96"
  };

  return (
    <div className={cn(
      "relative overflow-hidden rounded-xl",
      heightClasses[height],
      className
    )}>
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src={dtcAssets[heroImage]}
          alt="Hero Background"
          fill
          className="object-cover"
          priority
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
        />
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-[--color-primary]/80 via-[--color-primary]/60 to-[--color-primary-deep]/70" />
      </div>

      {/* Content Overlay */}
      <div className="relative z-10 flex flex-col justify-center h-full p-8 text-white">
        {overlayContent ? (
          overlayContent
        ) : (
          <div className="max-w-2xl">
            {title && (
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 font-display">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-lg md:text-xl opacity-90 leading-relaxed">
                {subtitle}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
