"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import { dtcAssets } from "@/lib/assets";
import { cn } from "@/lib/utils";
import { LanguageSwitcher } from "./LanguageSwitcher";

interface AuthLayoutProps {
  children: React.ReactNode;
  heroImage?: keyof typeof dtcAssets;
  className?: string;
}

export function AuthLayout({
  children,
  heroImage = "hero1",
  className
}: AuthLayoutProps) {
  const t = useTranslations("app");

  return (
    <div className={cn("min-h-screen flex", className)}>
      {/* Left side - Hero Image (70%) */}
      <div className="hidden lg:flex lg:w-[70%] relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-primary-deep/20 z-10" />
        <Image
          src={dtcAssets[heroImage]}
          alt="DTC Hero"
          fill
          className="object-cover"
          priority
          sizes="70vw"
        />
        {/* Overlay content */}
        <div className="relative z-20 flex flex-col justify-end p-12 text-white">
          <div className="max-w-md">
            <h1 className="text-4xl font-bold mb-4 font-display">
              {t("title")}
            </h1>
            <p className="text-lg opacity-90">
              {t("heroSubtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Right side - Auth Form (30%) */}
      <div className="w-full lg:w-[30%] flex flex-col">
        {/* Language Switcher */}
        <div className="flex justify-end p-4">
          <LanguageSwitcher />
        </div>

        <div className="flex-1 flex items-center justify-center p-8 lg:p-12 pt-0">
          <div className="w-full max-w-sm">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
