"use client";

import { forwardRef } from "react";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";

interface LoginFieldProps extends React.ComponentProps<"input"> {
  label: string;
  error?: string;
  showPasswordToggle?: boolean;
}

const LoginField = forwardRef<HTMLInputElement, LoginFieldProps>(
  ({ label, error, showPasswordToggle = false, className, type, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const t = useTranslations();

    const inputType = showPasswordToggle ? (showPassword ? "text" : "password") : type;

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={props.id} 
          className="text-sm font-medium text-charcoal"
        >
          {label}
        </Label>
        <div className="relative">
          <Input
            ref={ref}
            type={inputType}
            className={cn(
              "h-12 px-4 border-2 transition-all duration-200",
              "border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20",
              "placeholder:text-grey",
              error && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
              showPasswordToggle && "pr-12",
              className
            )}
            aria-invalid={!!error}
            aria-describedby={error ? `${props.id}-error` : undefined}
            {...props}
          />
          {showPasswordToggle && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-grey hover:text-charcoal transition-colors"
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5" />
              ) : (
                <Eye className="h-5 w-5" />
              )}
            </button>
          )}
        </div>
        {error && (
          <p 
            id={`${props.id}-error`}
            className="text-sm text-red-600 animate-in slide-in-from-top-1 duration-200"
            role="alert"
          >
            {t(error)}
          </p>
        )}
      </div>
    );
  }
);

LoginField.displayName = "LoginField";

export { LoginField };
