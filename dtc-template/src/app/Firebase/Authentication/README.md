### Firebase Authentication Structure

- `init.ts`: Initializes Firebase App, Auth, and Firestore once; reuses instances.
- `Authentication/AuthProvider.tsx`: Client context that exposes `user`, `loading`, `signOut`.
- `Authentication/SetupGate.tsx`: Optional initial user creation/sign-in screen controlled by `NEXT_PUBLIC_ENABLE_SETUP`.
- Hooks: `src/hooks/useAuth.ts`, `src/hooks/useFirestoreDoc.ts`, `src/hooks/useFirestoreCollection.ts` provide a clean interface for consuming auth and Firestore without duplicating SDK calls across components.
- Services: `src/Services/authService.ts`, `src/Services/firestoreService.ts` centralize all direct SDK interactions.

Usage guidelines:
- Use hooks in components only; do not call Firebase SDK directly in UI.
- Use services for any data or auth operation; keep UI free from SDK details.
- Toggle setup screen by `.env.local` — set `NEXT_PUBLIC_ENABLE_SETUP=true` for first run, then disable.

