"use client";

import { useTranslations } from "next-intl";
import { DashboardLayout } from "@/components/layouts/DashboardLayout";
import { useUser } from "@/hooks/useUser";
import { Card } from "@/components/ui/Card";
import { Badge } from "@/components/ui/badge";
import { Users, Activity, TrendingUp, Clock } from "lucide-react";

export default function DashboardPage() {
  const t = useTranslations("app");
  const { user } = useUser();

  // Sample dashboard stats (in a real app, these would come from your API)
  const stats = [
    {
      title: "Total Users",
      value: "1,234",
      change: "+12%",
      changeType: "positive" as const,
      icon: Users,
    },
    {
      title: "Active Sessions",
      value: "89",
      change: "+5%",
      changeType: "positive" as const,
      icon: Activity,
    },
    {
      title: "Growth Rate",
      value: "23.5%",
      change: "+2.1%",
      changeType: "positive" as const,
      icon: TrendingUp,
    },
    {
      title: "Avg. Session",
      value: "4m 32s",
      change: "-0.5%",
      changeType: "negative" as const,
      icon: Clock,
    },
  ];

  const heroContent = (
    <div className="max-w-2xl">
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 font-display">
        Welcome back, {user?.firstName || user?.displayName || "User"}!
      </h1>
      <p className="text-lg md:text-xl opacity-90 leading-relaxed">
        Digital Transformation Center Dashboard
      </p>
      <div className="mt-6">
        <Badge variant="outline" className="bg-white/20 text-white border-white/30">
          {user?.role} Access
        </Badge>
      </div>
    </div>
  );

  return (
    <DashboardLayout
      heroImage="hero2"
      heroContent={heroContent}
    >
      <div className="space-y-8">
        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-[--color-grey] mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-[--color-charcoal]">
                      {stat.value}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-[--color-primary]/10 rounded-xl flex items-center justify-center">
                    <Icon className="w-6 h-6 text-[--color-primary]" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span
                    className={`text-sm font-medium ${
                      stat.changeType === "positive"
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {stat.change}
                  </span>
                  <span className="text-sm text-[--color-grey] ml-2">
                    from last month
                  </span>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-[--color-charcoal] mb-4">
              Recent Activity
            </h3>
            <div className="space-y-4">
              {[
                { action: "User login", user: "<EMAIL>", time: "2 minutes ago" },
                { action: "Profile updated", user: "<EMAIL>", time: "15 minutes ago" },
                { action: "New user registered", user: "<EMAIL>", time: "1 hour ago" },
                { action: "Password reset", user: "<EMAIL>", time: "2 hours ago" },
              ].map((activity, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <div>
                    <p className="text-sm font-medium text-[--color-charcoal]">
                      {activity.action}
                    </p>
                    <p className="text-xs text-[--color-grey]">{activity.user}</p>
                  </div>
                  <span className="text-xs text-[--color-grey]">{activity.time}</span>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold text-[--color-charcoal] mb-4">
              Quick Actions
            </h3>
            <div className="space-y-3">
              {user?.role === "Admin" && (
                <>
                  <button className="w-full text-left p-3 rounded-lg border border-[--color-border] hover:bg-[--color-muted] transition-colors">
                    <div className="font-medium text-[--color-charcoal]">Manage Users</div>
                    <div className="text-sm text-[--color-grey]">Add, edit, or remove user accounts</div>
                  </button>
                  <button className="w-full text-left p-3 rounded-lg border border-[--color-border] hover:bg-[--color-muted] transition-colors">
                    <div className="font-medium text-[--color-charcoal]">System Settings</div>
                    <div className="text-sm text-[--color-grey]">Configure system preferences</div>
                  </button>
                </>
              )}
              <button className="w-full text-left p-3 rounded-lg border border-[--color-border] hover:bg-[--color-muted] transition-colors">
                <div className="font-medium text-[--color-charcoal]">View Reports</div>
                <div className="text-sm text-[--color-grey]">Access analytics and reports</div>
              </button>
              <button className="w-full text-left p-3 rounded-lg border border-[--color-border] hover:bg-[--color-muted] transition-colors">
                <div className="font-medium text-[--color-charcoal]">Profile Settings</div>
                <div className="text-sm text-[--color-grey]">Update your profile information</div>
              </button>
            </div>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
