"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { DashboardLayout } from "@/components/layouts/DashboardLayout";
import { useUser } from "@/hooks/useUser";
import Card from "@/components/ui/Card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, MoreHorizontal, Mail, Calendar } from "lucide-react";
import Image from "next/image";

export default function UserManagementPage() {
  const { user } = useUser();
  const router = useRouter();
  const locale = useLocale();

  // Redirect non-admin users
  useEffect(() => {
    if (user && user.role !== "Admin") {
      router.push(`/${locale}/dashboard`);
    }
  }, [user, router, locale]);

  // Sample user data (in a real app, this would come from your API)
  const users = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Admin",
      status: "Active",
      lastLogin: "2024-01-15",
      avatar: null,
    },
    {
      id: "2",
      name: "Jane <PERSON>",
      email: "<EMAIL>",
      role: "DTC",
      status: "Active",
      lastLogin: "2024-01-14",
      avatar: null,
    },
    {
      id: "3",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "DTC",
      status: "Inactive",
      lastLogin: "2024-01-10",
      avatar: null,
    },
    {
      id: "4",
      name: "Sarah Jones",
      email: "<EMAIL>",
      role: "DTC",
      status: "Active",
      lastLogin: "2024-01-15",
      avatar: null,
    },
  ];

  const heroContent = (
    <div className="max-w-2xl">
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 font-display">
        User Management
      </h1>
      <p className="text-lg md:text-xl opacity-90 leading-relaxed">
        Manage user accounts, roles, and permissions
      </p>
    </div>
  );

  // Don't render if user is not admin
  if (user && user.role !== "Admin") {
    return null;
  }

  return (
    <DashboardLayout
      heroImage="hero3"
      heroContent={heroContent}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[--color-grey] w-4 h-4" />
            <Input
              placeholder="Search users..."
              className="pl-10"
            />
          </div>
          <Button className="bg-[--color-primary] hover:bg-[--color-primary-deep] text-white">
            <Plus className="w-4 h-4 mr-2" />
            Add User
          </Button>
        </div>

        {/* Users Table */}
        <Card className="overflow-hidden">
          <div className="p-6 border-b border-[--color-border]">
            <h3 className="text-lg font-semibold text-[--color-charcoal]">
              All Users ({users.length})
            </h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-[--color-muted]">
                <tr>
                  <th className="text-left p-4 font-medium text-[--color-charcoal]">User</th>
                  <th className="text-left p-4 font-medium text-[--color-charcoal]">Role</th>
                  <th className="text-left p-4 font-medium text-[--color-charcoal]">Status</th>
                  <th className="text-left p-4 font-medium text-[--color-charcoal]">Last Login</th>
                  <th className="text-left p-4 font-medium text-[--color-charcoal]">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user, index) => (
                  <tr key={user.id} className={index % 2 === 0 ? "bg-white" : "bg-[--color-muted]/30"}>
                    <td className="p-4">
                      <div className="flex items-center space-x-3">
                        {user.avatar ? (
                          <Image
                            src={user.avatar}
                            alt={user.name}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-[--color-primary] rounded-full flex items-center justify-center">
                            <span className="text-white font-semibold text-sm">
                              {user.name.charAt(0)}
                            </span>
                          </div>
                        )}
                        <div>
                          <p className="font-medium text-[--color-charcoal]">{user.name}</p>
                          <p className="text-sm text-[--color-grey] flex items-center">
                            <Mail className="w-3 h-3 mr-1" />
                            {user.email}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge
                        variant={user.role === "Admin" ? "default" : "outline"}
                        className={user.role === "Admin" ? "bg-[--color-primary] text-white" : ""}
                      >
                        {user.role}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <Badge
                        variant={user.status === "Active" ? "default" : "outline"}
                        className={
                          user.status === "Active"
                            ? "bg-green-100 text-green-800 border-green-200"
                            : "bg-red-100 text-red-800 border-red-200"
                        }
                      >
                        {user.status}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center text-sm text-[--color-grey]">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(user.lastLogin).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="p-4">
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>

        {/* User Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-[--color-charcoal] mb-1">
                {users.filter(u => u.status === "Active").length}
              </div>
              <div className="text-sm text-[--color-grey]">Active Users</div>
            </div>
          </Card>
          <Card className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-[--color-charcoal] mb-1">
                {users.filter(u => u.role === "Admin").length}
              </div>
              <div className="text-sm text-[--color-grey]">Administrators</div>
            </div>
          </Card>
          <Card className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-[--color-charcoal] mb-1">
                {users.filter(u => u.role === "DTC").length}
              </div>
              <div className="text-sm text-[--color-grey]">DTC Users</div>
            </div>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
