"use client";

import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { toast } from "sonner";
import { AuthLayout, LoginForm } from "@/components/ui/auth";
import type { LoginFormData } from "@/lib/validations/auth";
import { emailPasswordSignIn, googleSignIn } from "@/Services/authService";
import { createOrUpdateUserProfile } from "@/Services/userService";

export default function LoginPage() {
  const router = useRouter();
  const locale = useLocale();

  const handleLogin = async (data: LoginFormData) => {
    try {
      const user = await emailPasswordSignIn({
        email: data.email,
        password: data.password,
      });

      // Create or update user profile
      const userProfile = await createOrUpdateUserProfile(user, 'email');

      // Show success toast
      toast.success(`Signed in successfully as ${userProfile.displayName || userProfile.email}!`);

      // Redirect to dashboard
      router.push(`/${locale}/dashboard`);
    } catch (error) {
      console.error("Login error:", error);
      throw error; // Let the form handle the error display
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const result = await googleSignIn();

      // Create or update user profile
      const userProfile = await createOrUpdateUserProfile(result.user, 'google');

      // Show success toast
      toast.success(`Signed in successfully as ${userProfile.displayName || userProfile.email}!`);

      // Redirect to dashboard
      router.push(`/${locale}/dashboard`);
    } catch (error) {
      console.error("Google sign-in error:", error);
      throw error; // Let the form handle the error display
    }
  };

  return (
    <AuthLayout heroImage="hero1">
      <LoginForm onSubmit={handleLogin} onGoogleSignIn={handleGoogleSignIn} />
    </AuthLayout>
  );
}
