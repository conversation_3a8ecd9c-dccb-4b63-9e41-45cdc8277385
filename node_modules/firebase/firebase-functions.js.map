{"version": 3, "file": "firebase-functions.js", "sources": ["../util/dist/postinstall.mjs", "../util/src/crypt.ts", "../util/src/defaults.ts", "../util/src/global.ts", "../util/src/url.ts", "../util/src/emulator.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../functions/src/serializer.ts", "../functions/src/constants.ts", "../functions/src/error.ts", "../functions/src/context.ts", "../functions/src/service.ts", "../functions/src/api.ts", "../functions/src/config.ts", "../functions/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// This value is retrieved and hardcoded by the NPM postinstall script\nconst getDefaultsFromPostinstall = () => undefined;\n\nexport { getDefaultsFromPostinstall };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\nimport { getDefaultsFromPostinstall } from './postinstall';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromPostinstall() ||\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nexport function isCloudWorkstation(url: string): boolean {\n  // `isCloudWorkstation` is called without protocol in certain connect*Emulator functions\n  // In HTTP request builders, it's called with the protocol.\n  // If called with protocol prefix, it's a valid URL, so we extract the hostname\n  // If called without, we assume the string is the hostname.\n  try {\n    const host =\n      url.startsWith('http://') || url.startsWith('https://')\n        ? new URL(url).hostname\n        : url;\n    return host.endsWith('.cloudworkstations.dev');\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nexport async function pingServer(endpoint: string): Promise<boolean> {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\nimport { isCloudWorkstation } from './url';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n\ninterface EmulatorStatusMap {\n  [name: string]: boolean;\n}\nconst emulatorStatus: EmulatorStatusMap = {};\n\ninterface EmulatorSummary {\n  prod: string[];\n  emulator: string[];\n}\n\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary(): EmulatorSummary {\n  const summary: EmulatorSummary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\n\nfunction getOrCreateEl(id: string): { created: boolean; element: HTMLElement } {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return { created, element: parentDiv };\n}\n\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nexport function updateEmulatorBanner(\n  name: string,\n  isRunningEmulator: boolean\n): void {\n  if (\n    typeof window === 'undefined' ||\n    typeof document === 'undefined' ||\n    !isCloudWorkstation(window.location.host) ||\n    emulatorStatus[name] === isRunningEmulator ||\n    emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n    previouslyDismissed\n  ) {\n    return;\n  }\n\n  emulatorStatus[name] = isRunningEmulator;\n\n  function prefixedId(id: string): string {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n\n  function tearDown(): void {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n\n  function setupBannerStyles(bannerEl: HTMLElement): void {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n\n  function setupIconStyles(prependIcon: SVGElement, iconId: string): void {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n\n  function setupCloseBtn(): HTMLSpanElement {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n\n  function setupLinkStyles(\n    learnMoreLink: HTMLAnchorElement,\n    learnMoreId: string\n  ): void {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href =\n      'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n\n  function setupDom(): void {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText: HTMLSpanElement =\n      document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink: HTMLAnchorElement =\n      (document.getElementById(learnMoreId) as HTMLAnchorElement) ||\n      document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon: SVGElement =\n      (document.getElementById(\n        prependIconId\n      ) as HTMLOrSVGElement as SVGElement) ||\n      document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nexport class FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Additional details to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport { _isFirebaseServerApp, FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  private serverAppAppCheckToken: string | null = null;\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider?.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.serverAppAppCheckToken) {\n      return this.serverAppAppCheckToken;\n    }\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableStreamResult,\n  HttpsCallableOptions,\n  HttpsCallableStreamOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport {\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\n\nexport const DEFAULT_REGION = 'us-central1';\n\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch = (...args) => fetch(...args)\n  ) {\n    this.contextProvider = new ContextProvider(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  const useSsl = isCloudWorkstation(host);\n  functionsInstance.emulatorOrigin = `http${\n    useSsl ? 's' : ''\n  }://${host}:${port}`;\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(functionsInstance.emulatorOrigin);\n    updateEmulatorBanner('Functions', true);\n  }\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData, StreamData = unknown>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return call(functionsInstance, name, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return stream(functionsInstance, name, data, options);\n  };\n\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData,\n  ResponseData,\n  StreamData = unknown\n>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(\n  functionsInstance: FunctionsService,\n  options: HttpsCallableOptions\n): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options?: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n\n  let response: Response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver: (value: unknown) => void;\n  let resultRejecter: (reason: unknown) => void;\n  const resultPromise = new Promise<unknown>((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body!.getReader();\n  const rstream = createResponseStream(\n    reader,\n    resultResolver!,\n    resultRejecter!,\n    options?.signal\n  );\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const { value, done } = await rreader.read();\n            return { value: value as unknown, done };\n          },\n          async return() {\n            await rreader.cancel();\n            return { done: true, value: undefined };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  resultResolver: (value: unknown) => void,\n  resultRejecter: (reason: unknown) => void,\n  signal?: AbortSignal\n): ReadableStream<unknown> {\n  const processLine = (\n    line: string,\n    controller: ReadableStreamDefaultController\n  ): void => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump(): Promise<void> {\n        if (signal?.aborted) {\n          const error = new FunctionsError(\n            'cancelled',\n            'Request was cancelled'\n          );\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError(\n              'cancelled',\n              'Request was cancelled'\n            );\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, { stream: true });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError =\n            error instanceof FunctionsError\n              ? error\n              : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport { FunctionsError } from './error';\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallable<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallableFromURL<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(variant?: string): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\n\nexport * from './api';\nexport * from './public-types';\n\nregisterFunctions();\n"], "names": ["base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "this", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "Error", "init_", "byteToCharMap", "output", "i", "length", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte1", "outByte2", "outByte3", "outByte4", "push", "join", "encodeString", "btoa", "str", "out", "p", "c", "charCodeAt", "stringToByteArray", "decodeString", "bytes", "pos", "c1", "String", "fromCharCode", "c2", "u", "c3", "byteArrayToString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "constructor", "name", "getDefaultsFromGlobal", "getGlobal", "self", "window", "global", "__FIREBASE_DEFAULTS__", "getDefaultsFromCookie", "document", "match", "cookie", "e", "decoded", "console", "error", "base64Decode", "JSON", "parse", "getDefaults", "process", "env", "defaultsJsonString", "getDefaultsFromEnvVariable", "info", "getDefaultEmulatorHostnameAndPort", "productName", "host", "emulatorHosts", "getDefaultEmulatorHost", "separatorIndex", "lastIndexOf", "port", "parseInt", "substring", "isCloudWorkstation", "url", "startsWith", "URL", "hostname", "endsWith", "emulatorStatus", "previouslyDismissed", "updateEmulatorBanner", "isRunningEmulator", "location", "prefixedId", "id", "bannerId", "showError", "getEmulatorSummary", "summary", "prod", "emulator", "key", "Object", "keys", "setupCloseBtn", "closeBtn", "createElement", "style", "cursor", "marginLeft", "fontSize", "innerHTML", "onclick", "tearDown", "element", "getElementById", "remove", "setupDom", "banner", "getOrCreateEl", "parentDiv", "created", "setAttribute", "firebaseTextId", "firebaseText", "learnMoreId", "learnMoreLink", "prependIconId", "prependIcon", "createElementNS", "bannerEl", "setupBannerStyles", "display", "background", "position", "bottom", "left", "padding", "borderRadius", "alignItems", "setupLinkStyles", "innerText", "href", "paddingLeft", "textDecoration", "setupIconStyles", "iconId", "append", "body", "append<PERSON><PERSON><PERSON>", "readyState", "addEventListener", "FirebaseError", "code", "message", "customData", "super", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "value", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "mapValues", "o", "f", "result", "hasOwnProperty", "encode", "Number", "valueOf", "isFinite", "toString", "call", "Date", "toISOString", "map", "x", "decode", "json", "isNaN", "FUNCTIONS_TYPE", "errorCodeMap", "OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "FunctionsError", "details", "_errorForResponse", "status", "bodyJSON", "codeForHTTPStatus", "description", "errorJSON", "undefined", "ContextProvider", "app", "authProvider", "messagingProvider", "appCheckProvider", "auth", "messaging", "appCheck", "serverAppAppCheckToken", "_isFirebaseServerApp", "settings", "appCheckToken", "getImmediate", "optional", "get", "then", "getAuthToken", "token", "getToken", "accessToken", "getMessagingToken", "Notification", "permission", "getAppCheckToken", "limitedUseAppCheckTokens", "getLimitedUseToken", "getContext", "authToken", "messagingToken", "DEFAULT_REGION", "responseLineRE", "FunctionsService", "regionOrCustomDomain", "fetchImpl", "args", "fetch", "emulator<PERSON><PERSON><PERSON>", "contextProvider", "cancelAllRequests", "Promise", "resolve", "deleteService", "customDomain", "origin", "pathname", "region", "_delete", "_url", "projectId", "options", "connectFunctionsEmulator", "functionsInstance", "useSsl", "async", "pingServer", "endpoint", "credentials", "ok", "httpsCallable", "callable", "callAtURL", "stream", "streamAtURL", "postJSON", "headers", "response", "method", "stringify", "makeAuthHeaders", "context", "failAfterHandle", "failAfter", "millis", "timer", "promise", "reject", "setTimeout", "cancel", "clearTimeout", "timeout", "race", "responseData", "resultResolver", "resultRejecter", "signal", "Symbol", "asyncIterator", "next", "resultPromise", "rstream", "createResponseStream", "reader", "processLine", "line", "controller", "jsonData", "enqueue", "decoder", "TextDecoder", "ReadableStream", "start", "currentText", "pump", "aborted", "done", "read", "trim", "close", "lines", "split", "pop", "functionsError", "<PERSON><PERSON><PERSON><PERSON>", "rreader", "getFunctions", "getApp", "_get<PERSON><PERSON><PERSON>", "identifier", "_connectFunctionsEmulator", "_httpsCallable", "httpsCallableFromURL", "_httpsCallableFromURL", "registerFunctions", "variant", "_registerComponent", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "registerVersion", "version"], "mappings": "2HAiBA,MC0FaA,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKF,gBAAIC,GACF,OAAOC,KAAKF,kBAAoB,KACjC,EAKD,wBAAIG,GACF,OAAOD,KAAKF,kBAAoB,KACjC,EASDI,mBAAoC,mBAATC,KAW3B,eAAAC,CAAgBC,EAA8BC,GAC5C,IAAKC,MAAMC,QAAQH,GACjB,MAAMI,MAAM,iDAGdT,KAAKU,QAEL,MAAMC,EAAgBL,EAClBN,KAAKJ,sBACLI,KAAKN,eAEHkB,EAAS,GAEf,IAAK,IAAIC,EAAI,EAAGA,EAAIR,EAAMS,OAAQD,GAAK,EAAG,CACxC,MAAME,EAAQV,EAAMQ,GACdG,EAAYH,EAAI,EAAIR,EAAMS,OAC1BG,EAAQD,EAAYX,EAAMQ,EAAI,GAAK,EACnCK,EAAYL,EAAI,EAAIR,EAAMS,OAC1BK,EAAQD,EAAYb,EAAMQ,EAAI,GAAK,EAEnCO,EAAWL,GAAS,EACpBM,GAAqB,EAARN,IAAiB,EAAME,GAAS,EACnD,IAAIK,GAAqB,GAARL,IAAiB,EAAME,GAAS,EAC7CI,EAAmB,GAARJ,EAEVD,IACHK,EAAW,GAENP,IACHM,EAAW,KAIfV,EAAOY,KACLb,EAAcS,GACdT,EAAcU,GACdV,EAAcW,GACdX,EAAcY,GAEjB,CAED,OAAOX,EAAOa,KAAK,GACpB,EAUD,YAAAC,CAAarB,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBqB,KAAKtB,GAEPL,KAAKI,gBAlNU,SAAUwB,GAElC,MAAMC,EAAgB,GACtB,IAAIC,EAAI,EACR,IAAK,IAAIjB,EAAI,EAAGA,EAAIe,EAAId,OAAQD,IAAK,CACnC,IAAIkB,EAAIH,EAAII,WAAWnB,GACnBkB,EAAI,IACNF,EAAIC,KAAOC,EACFA,EAAI,MACbF,EAAIC,KAAQC,GAAK,EAAK,IACtBF,EAAIC,KAAY,GAAJC,EAAU,KAEL,QAAZ,MAAJA,IACDlB,EAAI,EAAIe,EAAId,QACyB,QAAZ,MAAxBc,EAAII,WAAWnB,EAAI,KAGpBkB,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBH,EAAII,aAAanB,IACvDgB,EAAIC,KAAQC,GAAK,GAAM,IACvBF,EAAIC,KAASC,GAAK,GAAM,GAAM,IAC9BF,EAAIC,KAASC,GAAK,EAAK,GAAM,IAC7BF,EAAIC,KAAY,GAAJC,EAAU,MAEtBF,EAAIC,KAAQC,GAAK,GAAM,IACvBF,EAAIC,KAASC,GAAK,EAAK,GAAM,IAC7BF,EAAIC,KAAY,GAAJC,EAAU,IAEzB,CACD,OAAOF,CACT,CAqLgCI,CAAkB5B,GAAQC,EACvD,EAUD,YAAA4B,CAAa7B,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBH,KAAKE,GA5LQ,SAAU8B,GAElC,MAAMN,EAAgB,GACtB,IAAIO,EAAM,EACRL,EAAI,EACN,KAAOK,EAAMD,EAAMrB,QAAQ,CACzB,MAAMuB,EAAKF,EAAMC,KACjB,GAAIC,EAAK,IACPR,EAAIE,KAAOO,OAAOC,aAAaF,QAC1B,GAAIA,EAAK,KAAOA,EAAK,IAAK,CAC/B,MAAMG,EAAKL,EAAMC,KACjBP,EAAIE,KAAOO,OAAOC,cAAoB,GAALF,IAAY,EAAW,GAALG,EACpD,MAAM,GAAIH,EAAK,KAAOA,EAAK,IAAK,CAE/B,MAGMI,IACI,EAALJ,IAAW,IAAa,GAJlBF,EAAMC,OAImB,IAAa,GAHtCD,EAAMC,OAGuC,EAAW,GAFxDD,EAAMC,MAGf,MACFP,EAAIE,KAAOO,OAAOC,aAAa,OAAUE,GAAK,KAC9CZ,EAAIE,KAAOO,OAAOC,aAAa,OAAc,KAAJE,GAC1C,KAAM,CACL,MAAMD,EAAKL,EAAMC,KACXM,EAAKP,EAAMC,KACjBP,EAAIE,KAAOO,OAAOC,cACT,GAALF,IAAY,IAAa,GAALG,IAAY,EAAW,GAALE,EAE3C,CACF,CACD,OAAOb,EAAIJ,KAAK,GAClB,CA+JWkB,CAAkB3C,KAAK4C,wBAAwBvC,EAAOC,GAC9D,EAiBD,uBAAAsC,CAAwBvC,EAAeC,GACrCN,KAAKU,QAEL,MAAMmC,EAAgBvC,EAClBN,KAAKH,sBACLG,KAAKL,eAEHiB,EAAmB,GAEzB,IAAK,IAAIC,EAAI,EAAGA,EAAIR,EAAMS,QAAU,CAClC,MAAMC,EAAQ8B,EAAcxC,EAAMyC,OAAOjC,MAGnCI,EADYJ,EAAIR,EAAMS,OACF+B,EAAcxC,EAAMyC,OAAOjC,IAAM,IACzDA,EAEF,MACMM,EADYN,EAAIR,EAAMS,OACF+B,EAAcxC,EAAMyC,OAAOjC,IAAM,KACzDA,EAEF,MACMkC,EADYlC,EAAIR,EAAMS,OACF+B,EAAcxC,EAAMyC,OAAOjC,IAAM,GAG3D,KAFEA,EAEW,MAATE,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAAT4B,EACrD,MAAM,IAAIC,wBAGZ,MAAM5B,EAAYL,GAAS,EAAME,GAAS,EAG1C,GAFAL,EAAOY,KAAKJ,GAEE,KAAVD,EAAc,CAChB,MAAME,EAAaJ,GAAS,EAAK,IAASE,GAAS,EAGnD,GAFAP,EAAOY,KAAKH,GAEE,KAAV0B,EAAc,CAChB,MAAMzB,EAAaH,GAAS,EAAK,IAAQ4B,EACzCnC,EAAOY,KAAKF,EACb,CACF,CACF,CAED,OAAOV,CACR,EAOD,KAAAF,GACE,IAAKV,KAAKN,eAAgB,CACxBM,KAAKN,eAAiB,GACtBM,KAAKL,eAAiB,GACtBK,KAAKJ,sBAAwB,GAC7BI,KAAKH,sBAAwB,GAG7B,IAAK,IAAIgB,EAAI,EAAGA,EAAIb,KAAKD,aAAae,OAAQD,IAC5Cb,KAAKN,eAAemB,GAAKb,KAAKD,aAAa+C,OAAOjC,GAClDb,KAAKL,eAAeK,KAAKN,eAAemB,IAAMA,EAC9Cb,KAAKJ,sBAAsBiB,GAAKb,KAAKC,qBAAqB6C,OAAOjC,GACjEb,KAAKH,sBAAsBG,KAAKJ,sBAAsBiB,IAAMA,EAGxDA,GAAKb,KAAKF,kBAAkBgB,SAC9Bd,KAAKL,eAAeK,KAAKC,qBAAqB6C,OAAOjC,IAAMA,EAC3Db,KAAKH,sBAAsBG,KAAKD,aAAa+C,OAAOjC,IAAMA,EAG/D,CACF,GAMG,MAAOmC,gCAAgCvC,MAA7C,WAAAwC,uBACWjD,KAAIkD,KAAG,yBACjB,EC7RD,MAAMC,sBAAwB,aClCdC,YACd,GAAoB,oBAATC,KACT,OAAOA,KAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,GAAsB,oBAAXC,OACT,OAAOA,OAET,MAAM,IAAI9C,MAAM,kCAClB,CDwBE2C,GAAYI,sBAoBRC,sBAAwB,KAC5B,GAAwB,oBAAbC,SACT,OAEF,IAAIC,EACJ,IACEA,EAAQD,SAASE,OAAOD,MAAM,gCAC/B,CAAC,MAAOE,GAGP,MACD,CACD,MAAMC,EAAUH,GDwRU,SAAU/B,GACpC,IACE,OAAOnC,EAAOyC,aAAaN,GAAK,EACjC,CAAC,MAAOiC,GACPE,QAAQC,MAAM,wBAAyBH,EACxC,CACD,OAAO,IACT,CC/R2BI,CAAaN,EAAM,IAC5C,OAAOG,GAAWI,KAAKC,MAAML,EAAQ,EAU1BM,YAAc,KACzB,IACE,OAEEjB,yBArC6B,MACjC,GAAuB,oBAAZkB,cAAkD,IAAhBA,QAAQC,IACnD,OAEF,MAAMC,EAAqBF,QAAQC,IAAId,sBACvC,OAAIe,EACKL,KAAKC,MAAMI,QADpB,CAEC,EA+BGC,IACAf,uBAEH,CAAC,MAAOI,GAQP,YADAE,QAAQU,KAAK,+CAA+CZ,IAE7D,GAmBUa,kCACXC,IAEA,MAAMC,EAb8B,CACpCD,GACuBP,eAAeS,gBAAgBF,GAWzCG,CAAuBH,GACpC,IAAKC,EACH,OAEF,MAAMG,EAAiBH,EAAKI,YAAY,KACxC,GAAID,GAAkB,GAAKA,EAAiB,IAAMH,EAAK9D,OACrD,MAAM,IAAIL,MAAM,gBAAgBmE,yCAGlC,MAAMK,EAAOC,SAASN,EAAKO,UAAUJ,EAAiB,GAAI,IAC1D,MAAgB,MAAZH,EAAK,GAEA,CAACA,EAAKO,UAAU,EAAGJ,EAAiB,GAAIE,GAExC,CAACL,EAAKO,UAAU,EAAGJ,GAAiBE,EAC5C,EErIG,SAAUG,mBAAmBC,GAKjC,IAKE,OAHEA,EAAIC,WAAW,YAAcD,EAAIC,WAAW,YACxC,IAAIC,IAAIF,GAAKG,SACbH,GACMI,SAAS,yBACtB,CAAC,MACA,OAAO,CACR,CACH,CCgHA,MAAMC,EAAoC,CAAA,EAkC1C,IAAIC,GAAsB,EAOV,SAAAC,qBACd1C,EACA2C,GAEA,GACoB,oBAAXvC,QACa,oBAAbI,WACN0B,mBAAmB9B,OAAOwC,SAASlB,OACpCc,EAAexC,KAAU2C,GACzBH,EAAexC,IACfyC,EAEA,OAKF,SAASI,WAAWC,GAClB,MAAO,uBAAuBA,GAC/B,CAJDN,EAAexC,GAAQ2C,EAKvB,MAAMI,EAAW,qBAEXC,EAvDR,SAASC,qBACP,MAAMC,EAA2B,CAC/BC,KAAM,GACNC,SAAU,IAEZ,IAAK,MAAMC,KAAOC,OAAOC,KAAKf,GACxBA,EAAea,GACjBH,EAAQE,SAAS9E,KAAK+E,GAEtBH,EAAQC,KAAK7E,KAAK+E,GAGtB,OAAOH,CACT,CAyCkBD,GACUE,KAAKvF,OAAS,EA6BxC,SAAS4F,gBACP,MAAMC,EAAWjD,SAASkD,cAAc,QASxC,OARAD,EAASE,MAAMC,OAAS,UACxBH,EAASE,MAAME,WAAa,OAC5BJ,EAASE,MAAMG,SAAW,OAC1BL,EAASM,UAAY,WACrBN,EAASO,QAAU,KACjBvB,GAAsB,EAlC1B,SAASwB,WACP,MAAMC,EAAU1D,SAAS2D,eAAepB,GACpCmB,GACFA,EAAQE,QAEX,CA8BGH,EAAU,EAELR,CACR,CAeD,SAASY,WACP,MAAMC,EAhGV,SAASC,cAAczB,GACrB,IAAI0B,EAAYhE,SAAS2D,eAAerB,GACpC2B,GAAU,EAMd,OALKD,IACHA,EAAYhE,SAASkD,cAAc,OACnCc,EAAUE,aAAa,KAAM5B,GAC7B2B,GAAU,GAEL,CAAEA,UAASP,QAASM,EAC7B,CAuFmBD,CAAcxB,GACvB4B,EAAiB9B,WAAW,QAC5B+B,EACJpE,SAAS2D,eAAeQ,IAAmBnE,SAASkD,cAAc,QAC9DmB,EAAchC,WAAW,aACzBiC,EACHtE,SAAS2D,eAAeU,IACzBrE,SAASkD,cAAc,KACnBqB,EAAgBlC,WAAW,gBAC3BmC,EACHxE,SAAS2D,eACRY,IAEFvE,SAASyE,gBAAgB,6BAA8B,OACzD,GAAIX,EAAOG,QAAS,CAElB,MAAMS,EAAWZ,EAAOJ,SA/D5B,SAASiB,kBAAkBD,GACzBA,EAASvB,MAAMyB,QAAU,OACzBF,EAASvB,MAAM0B,WAAa,UAC5BH,EAASvB,MAAM2B,SAAW,QAC1BJ,EAASvB,MAAM4B,OAAS,MACxBL,EAASvB,MAAM6B,KAAO,MACtBN,EAASvB,MAAM8B,QAAU,OACzBP,EAASvB,MAAM+B,aAAe,MAC9BR,EAASvB,MAAMgC,WAAa,QAC7B,CAuDGR,CAAkBD,GA/BtB,SAASU,gBACPd,EACAD,GAEAC,EAAcJ,aAAa,KAAMG,GACjCC,EAAce,UAAY,aAC1Bf,EAAcgB,KACZ,uEACFhB,EAAcJ,aAAa,SAAU,WACrCI,EAAcnB,MAAMoC,YAAc,MAClCjB,EAAcnB,MAAMqC,eAAiB,WACtC,CAqBGJ,CAAgBd,EAAeD,GAC/B,MAAMpB,EAAWD,iBAvDrB,SAASyC,gBAAgBjB,EAAyBkB,GAChDlB,EAAYN,aAAa,QAAS,MAClCM,EAAYN,aAAa,KAAMwB,GAC/BlB,EAAYN,aAAa,SAAU,MACnCM,EAAYN,aAAa,UAAW,aACpCM,EAAYN,aAAa,OAAQ,QACjCM,EAAYrB,MAAME,WAAa,MAChC,CAiDGoC,CAAgBjB,EAAaD,GAC7BG,EAASiB,OAAOnB,EAAaJ,EAAcE,EAAerB,GAC1DjD,SAAS4F,KAAKC,YAAYnB,EAC3B,CAEGlC,GACF4B,EAAaiB,UAAY,gCACzBb,EAAYjB,UAAY,wnBASxBiB,EAAYjB,UAAY,0hDAQxBa,EAAaiB,UAAY,8CAE3BjB,EAAaF,aAAa,KAAMC,EACjC,CAC2B,YAAxBnE,SAAS8F,WACXlG,OAAOmG,iBAAiB,mBAAoBlC,UAE5CA,UAEJ,CCtPM,MAAOmC,sBAAsBjJ,MAIjC,WAAAwC,CAEW0G,EACTC,EAEOC,GAEPC,MAAMF,GALG5J,KAAI2J,KAAJA,EAGF3J,KAAU6J,WAAVA,EAPA7J,KAAIkD,KAdI,gBA6BfsD,OAAOuD,eAAe/J,KAAM0J,cAAcM,WAItCvJ,MAAMwJ,mBACRxJ,MAAMwJ,kBAAkBjK,KAAMkK,aAAaF,UAAUG,OAExD,EAGU,MAAAD,aAIX,WAAAjH,CACmBmH,EACAC,EACAC,GAFAtK,KAAOoK,QAAPA,EACApK,KAAWqK,YAAXA,EACArK,KAAMsK,OAANA,CACf,CAEJ,MAAAH,CACER,KACGY,GAEH,MAAMV,EAAcU,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGxK,KAAKoK,WAAWT,IAC9Bc,EAAWzK,KAAKsK,OAAOX,GAEvBC,EAAUa,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGtE,KACnC,MAAMuE,EAAQP,EAAKhE,GACnB,OAAgB,MAATuE,EAAgBxI,OAAOwI,GAAS,IAAIvE,KAAO,GAEtD,CAf+BmE,CAAgBD,EAAUZ,GAAc,QAE7DkB,EAAc,GAAG/K,KAAKqK,gBAAgBT,MAAYY,MAIxD,OAFc,IAAId,cAAcc,EAAUO,EAAalB,EAGxD,EAUH,MAAMe,EAAU,gBClHV,SAAUI,mBACdZ,GAEA,OAAIA,GAAYA,EAA+Ba,UACrCb,EAA+Ba,UAEhCb,CAEX,CCDa,MAAAc,UAiBX,WAAAjI,CACWC,EACAiI,EACAC,GAFApL,KAAIkD,KAAJA,EACAlD,KAAemL,gBAAfA,EACAnL,KAAIoL,KAAJA,EAnBXpL,KAAiBqL,mBAAG,EAIpBrL,KAAYsL,aAAe,GAE3BtL,KAAAuL,kBAA2C,OAE3CvL,KAAiBwL,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADA1L,KAAKuL,kBAAoBG,EAClB1L,IACR,CAED,oBAAA2L,CAAqBN,GAEnB,OADArL,KAAKqL,kBAAoBA,EAClBrL,IACR,CAED,eAAA4L,CAAgBC,GAEd,OADA7L,KAAKsL,aAAeO,EACb7L,IACR,CAED,0BAAA8L,CAA2BC,GAEzB,OADA/L,KAAKwL,kBAAoBO,EAClB/L,IACR,EClDH,SAASgM,UAGPC,EACAC,GAEA,MAAMC,EAAqC,CAAA,EAC3C,IAAK,MAAM5F,KAAO0F,EACZA,EAAEG,eAAe7F,KACnB4F,EAAO5F,GAAO2F,EAAED,EAAE1F,KAGtB,OAAO4F,CACT,CAQM,SAAUE,OAAO9B,GACrB,GAAY,MAARA,EACF,OAAO,KAKT,GAHIA,aAAgB+B,SAClB/B,EAAOA,EAAKgC,WAEM,iBAAThC,GAAqBiC,SAASjC,GAGvC,OAAOA,EAET,IAAa,IAATA,IAA0B,IAATA,EACnB,OAAOA,EAET,GAA6C,oBAAzC/D,OAAOwD,UAAUyC,SAASC,KAAKnC,GACjC,OAAOA,EAET,GAAIA,aAAgBoC,KAClB,OAAOpC,EAAKqC,cAEd,GAAIrM,MAAMC,QAAQ+J,GAChB,OAAOA,EAAKsC,KAAIC,GAAKT,OAAOS,KAE9B,GAAoB,mBAATvC,GAAuC,iBAATA,EACvC,OAAOyB,UAAUzB,GAAOuC,GAAKT,OAAOS,KAGtC,MAAM,IAAIrM,MAAM,mCAAqC8J,EACvD,CAQM,SAAUwC,OAAOC,GACrB,GAAY,MAARA,EACF,OAAOA,EAET,GAAKA,EAAoC,SACvC,OAASA,EAAoC,UAC3C,IAnEY,iDAqEZ,IApEqB,kDAoEI,CAIvB,MAAMlC,EAAQwB,OAAQU,EAA2C,OACjE,GAAIC,MAAMnC,GACR,MAAM,IAAIrK,MAAM,qCAAuCuM,GAEzD,OAAOlC,CACR,CACD,QACE,MAAM,IAAIrK,MAAM,qCAAuCuM,GAI7D,OAAIzM,MAAMC,QAAQwM,GACTA,EAAKH,KAAIC,GAAKC,OAAOD,KAEV,mBAATE,GAAuC,iBAATA,EAChChB,UAAUgB,GAAOF,GAAKC,OAAOD,KAG/BE,CACT,CCxFO,MAAME,EAAiB,YCUxBC,EAAuD,CAC3DC,GAAI,KACJC,UAAW,YACXC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,UAAW,YACXC,eAAgB,iBAChBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,QAAS,UACTC,aAAc,eACdC,cAAe,gBACfC,SAAU,WACVC,YAAa,cACbC,UAAW,aAUP,MAAOC,uBAAuB3E,cAIlC,WAAAzG,CAKE0G,EACAC,EAIS0E,GAETxE,MAAM,GAAGoD,KAAkBvD,IAAQC,GAAW,IAFrC5J,KAAOsO,QAAPA,EAMT9H,OAAOuD,eAAe/J,KAAMqO,eAAerE,UAC5C,EAkDa,SAAAuE,kBACdC,EACAC,GAEA,IAKIH,EALA3E,EA3CN,SAAS+E,kBAAkBF,GAEzB,GAAIA,GAAU,KAAOA,EAAS,IAC5B,MAAO,KAET,OAAQA,GACN,KAAK,EAiBL,KAAK,IACH,MAAO,WAfT,KAAK,IACH,MAAO,mBACT,KAAK,IACH,MAAO,kBACT,KAAK,IACH,MAAO,oBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,UACT,KAAK,IACH,MAAO,qBACT,KAAK,IACH,MAAO,YAGT,KAAK,IACH,MAAO,gBACT,KAAK,IACH,MAAO,cACT,KAAK,IACH,MAAO,oBAGX,MAAO,SACT,CASaE,CAAkBF,GAGzBG,EAAsBhF,EAK1B,IACE,MAAMiF,EAAYH,GAAYA,EAASzK,MACvC,GAAI4K,EAAW,CACb,MAAMJ,EAASI,EAAUJ,OACzB,GAAsB,iBAAXA,EAAqB,CAC9B,IAAKrB,EAAaqB,GAEhB,OAAO,IAAIH,eAAe,WAAY,YAExC1E,EAAOwD,EAAaqB,GAIpBG,EAAcH,CACf,CAED,MAAM5E,EAAUgF,EAAUhF,QACH,iBAAZA,IACT+E,EAAc/E,GAGhB0E,EAAUM,EAAUN,aACJO,IAAZP,IACFA,EAAUvB,OAAOuB,GAEpB,CACF,CAAC,MAAOzK,GAER,CAED,MAAa,OAAT8F,EAIK,KAGF,IAAI0E,eAAe1E,EAAMgF,EAAaL,EAC/C,CCpIa,MAAAQ,gBAKX,WAAA7L,CACW8L,EACTC,EACAC,EACAC,GAHSlP,KAAG+O,IAAHA,EALH/O,KAAImP,KAAgC,KACpCnP,KAASoP,UAA6B,KACtCpP,KAAQqP,SAAoC,KAC5CrP,KAAsBsP,uBAAkB,KAO1CC,EAAqBR,IAAQA,EAAIS,SAASC,gBAC5CzP,KAAKsP,uBAAyBP,EAAIS,SAASC,eAE7CzP,KAAKmP,KAAOH,EAAaU,aAAa,CAAEC,UAAU,IAClD3P,KAAKoP,UAAYH,EAAkBS,aAAa,CAC9CC,UAAU,IAGP3P,KAAKmP,MACRH,EAAaY,MAAMC,MACjBV,GAASnP,KAAKmP,KAAOA,IACrB,SAMCnP,KAAKoP,WACRH,EAAkBW,MAAMC,MACtBT,GAAcpP,KAAKoP,UAAYA,IAC/B,SAMCpP,KAAKqP,UACRH,GAAkBU,MAAMC,MACtBR,GAAarP,KAAKqP,SAAWA,IAC7B,QAKL,CAED,kBAAMS,GACJ,GAAK9P,KAAKmP,KAIV,IACE,MAAMY,QAAc/P,KAAKmP,KAAKa,WAC9B,OAAOD,GAAOE,WACf,CAAC,MAAOpM,GAEP,MACD,CACF,CAED,uBAAMqM,GACJ,GACGlQ,KAAKoP,WACJ,iBAAkB/L,MACQ,YAA5B8M,aAAaC,WAKf,IACE,aAAapQ,KAAKoP,UAAUY,UAC7B,CAAC,MAAOnM,GAKP,MACD,CACF,CAED,sBAAMwM,CACJC,GAEA,GAAItQ,KAAKsP,uBACP,OAAOtP,KAAKsP,uBAEd,GAAItP,KAAKqP,SAAU,CACjB,MAAMlD,EAASmE,QACLtQ,KAAKqP,SAASkB,2BACdvQ,KAAKqP,SAASW,WACxB,OAAI7D,EAAOnI,MAIF,KAEFmI,EAAO4D,KACf,CACD,OAAO,IACR,CAED,gBAAMS,CAAWF,GAIf,MAAO,CAAEG,gBAHezQ,KAAK8P,eAGTY,qBAFS1Q,KAAKkQ,oBAEET,oBADRzP,KAAKqQ,iBAAiBC,GAEnD,ECnHI,MAAMK,EAAiB,cAExBC,EAAiB,uBA0DV,MAAAC,iBAYX,WAAA5N,CACW8L,EACTC,EACAC,EACAC,EACA4B,EAA+BH,EACtBI,EAA0B,IAAIC,IAASC,SAASD,IALhDhR,KAAG+O,IAAHA,EAKA/O,KAAS+Q,UAATA,EAhBX/Q,KAAckR,eAAkB,KAkB9BlR,KAAKmR,gBAAkB,IAAIrC,gBACzBC,EACAC,EACAC,EACAC,GAGFlP,KAAKoR,kBAAoB,IAAIC,SAAQC,IACnCtR,KAAKuR,cAAgB,IACZF,QAAQC,QAAQA,IACxB,IAIH,IACE,MAAMjM,EAAM,IAAIE,IAAIuL,GACpB9Q,KAAKwR,aACHnM,EAAIoM,QAA2B,MAAjBpM,EAAIqM,SAAmB,GAAKrM,EAAIqM,UAChD1R,KAAK2R,OAAShB,CACf,CAAC,MAAO9M,GACP7D,KAAKwR,aAAe,KACpBxR,KAAK2R,OAASb,CACf,CACF,CAED,OAAAc,GACE,OAAO5R,KAAKuR,eACb,CAOD,IAAAM,CAAK3O,GACH,MAAM4O,EAAY9R,KAAK+O,IAAIgD,QAAQD,UACnC,GAA4B,OAAxB9R,KAAKkR,eAAyB,CAEhC,MAAO,GADQlR,KAAKkR,kBACAY,KAAa9R,KAAK2R,UAAUzO,GACjD,CAED,OAA0B,OAAtBlD,KAAKwR,aACA,GAAGxR,KAAKwR,gBAAgBtO,IAG1B,WAAWlD,KAAK2R,UAAUG,wBAAgC5O,GAClE,EAYa8O,SAAAA,2BACdC,EACArN,EACAK,GAEA,MAAMiN,EAAS9M,mBAAmBR,GAClCqN,EAAkBf,eAAiB,OACjCgB,EAAS,IAAM,QACXtN,KAAQK,IAEViN,KThJCC,eAAeC,WAAWC,GAI/B,aAHqBpB,MAAMoB,EAAU,CACnCC,YAAa,aAEDC,EAChB,CS4ISH,CAAWH,EAAkBf,gBAClCtL,qBAAqB,aAAa,GAEtC,CAOgB4M,SAAAA,gBACdP,EACA/O,EACA6O,GAEA,MAAMU,SACJlI,GAuHJ,SAASmC,KACPuF,EACA/O,EACAqH,EACAwH,GAEA,MAAM1M,EAAM4M,EAAkBJ,KAAK3O,GACnC,OAAOwP,UAAUT,EAAmB5M,EAAKkF,EAAMwH,EACjD,CA7HWrF,CAAKuF,EAAmB/O,EAAMqH,EAAMwH,GAAW,CAAA,GAUxD,OAPAU,SAASE,OAAS,CAChBpI,EACAwH,IAkMJ,SAASY,OACPV,EACA/O,EACAqH,EACAwH,GAEA,MAAM1M,EAAM4M,EAAkBJ,KAAK3O,GACnC,OAAO0P,YAAYX,EAAmB5M,EAAKkF,EAAMwH,GAAW,CAAA,EAC9D,CAxMWY,CAAOV,EAAmB/O,EAAMqH,EAAMwH,GAGxCU,QACT,CAsCAN,eAAeU,SACbxN,EACAiE,EACAwJ,EACA/B,GAIA,IAAIgC,EAFJD,EAAQ,gBAAkB,mBAG1B,IACEC,QAAiBhC,EAAU1L,EAAK,CAC9B2N,OAAQ,OACR1J,KAAMpF,KAAK+O,UAAU3J,GACrBwJ,WAEH,CAAC,MAAOjP,GAKP,MAAO,CACL2K,OAAQ,EACRxB,KAAM,KAET,CACD,IAAIA,EAAgC,KACpC,IACEA,QAAa+F,EAAS/F,MACvB,CAAC,MAAOnJ,GAER,CACD,MAAO,CACL2K,OAAQuE,EAASvE,OACjBxB,OAEJ,CAQAmF,eAAee,gBACbjB,EACAF,GAEA,MAAMe,EAAkC,CAAA,EAClCK,QAAgBlB,EAAkBd,gBAAgBX,WACtDuB,EAAQzB,0BAWV,OATI6C,EAAQ1C,YACVqC,EAAuB,cAAI,UAAYK,EAAQ1C,WAE7C0C,EAAQzC,iBACVoC,EAAQ,8BAAgCK,EAAQzC,gBAEpB,OAA1ByC,EAAQ1D,gBACVqD,EAAQ,uBAAyBK,EAAQ1D,eAEpCqD,CACT,CAsBAX,eAAeO,UACbT,EACA5M,EACAkF,EACAwH,GAIA,MAAMzI,EAAO,CAAEiB,KADfA,EAAO8B,OAAO9B,IAIRuI,QAAgBI,gBAAgBjB,EAAmBF,GAKnDqB,EAtRR,SAASC,UAAUC,GAIjB,IAAIC,EAAoB,KACxB,MAAO,CACLC,QAAS,IAAInC,SAAQ,CAACxG,EAAG4I,KACvBF,EAAQG,YAAW,KACjBD,EAAO,IAAIpF,eAAe,oBAAqB,qBAAqB,GACnEiF,EAAO,IAEZK,OAAQ,KACFJ,GACFK,aAAaL,EACd,EAGP,CAqQ0BF,CAFRtB,EAAQ8B,SAAW,KAG7Bd,QAAiB1B,QAAQyC,KAAK,CAClCjB,SAASxN,EAAKiE,EAAMwJ,EAASb,EAAkBlB,WAC/CqC,EAAgBI,QAChBvB,EAAkBb,oBAOpB,GAHAgC,EAAgBO,UAGXZ,EACH,MAAM,IAAI1E,eACR,YACA,4CAKJ,MAAMrK,EAAQuK,kBAAkBwE,EAASvE,OAAQuE,EAAS/F,MAC1D,GAAIhJ,EACF,MAAMA,EAGR,IAAK+O,EAAS/F,KACZ,MAAM,IAAIqB,eAAe,WAAY,sCAGvC,IAAI0F,EAAehB,EAAS/F,KAAKzC,KAMjC,QAH4B,IAAjBwJ,IACTA,EAAehB,EAAS/F,KAAKb,aAEH,IAAjB4H,EAET,MAAM,IAAI1F,eAAe,WAAY,mCAMvC,MAAO,CAAE9D,KAFWwC,OAAOgH,GAG7B,CAwBA5B,eAAeS,YACbX,EACA5M,EACAkF,EACAwH,GAIA,MAAMzI,EAAO,CAAEiB,KADfA,EAAO8B,OAAO9B,IAIRuI,QAAgBI,gBAAgBjB,EAAmBF,GAIzD,IAAIgB,EA2CAiB,EACAC,EA/CJnB,EAAQ,gBAAkB,mBAC1BA,EAAgB,OAAI,oBAGpB,IACEC,QAAiBd,EAAkBlB,UAAU1L,EAAK,CAChD2N,OAAQ,OACR1J,KAAMpF,KAAK+O,UAAU3J,GACrBwJ,UACAoB,OAAQnC,GAASmC,QAEpB,CAAC,MAAOrQ,GACP,GAAIA,aAAapD,OAAoB,eAAXoD,EAAEX,KAAuB,CACjD,MAAMc,EAAQ,IAAIqK,eAAe,YAAa,0BAC9C,MAAO,CACL9D,KAAM8G,QAAQoC,OAAOzP,GACrB2O,OAAQ,CACN,CAACwB,OAAOC,eAAc,KACb,CACLC,KAAI,IACKhD,QAAQoC,OAAOzP,MAMjC,CAKD,MAAMA,EAAQuK,kBAAkB,EAAG,MACnC,MAAO,CACLhE,KAAM8G,QAAQoC,OAAOzP,GAErB2O,OAAQ,CACN,CAACwB,OAAOC,eAAc,KACb,CACLC,KAAI,IACKhD,QAAQoC,OAAOzP,MAMjC,CAGD,MAAMsQ,EAAgB,IAAIjD,SAAiB,CAACC,EAASmC,KACnDO,EAAiB1C,EACjB2C,EAAiBR,CAAM,IAEzB1B,GAASmC,QAAQzK,iBAAiB,SAAS,KACzC,MAAMzF,EAAQ,IAAIqK,eAAe,YAAa,0BAC9C4F,EAAejQ,EAAM,IAEvB,MACMuQ,EAyCR,SAASC,qBACPC,EACAT,EACAC,EACAC,GAEA,MAAMQ,YAAc,CAClBC,EACAC,KAEA,MAAMjR,EAAQgR,EAAKhR,MAAMiN,GAEzB,IAAKjN,EACH,OAEF,MAAM4G,EAAO5G,EAAM,GACnB,IACE,MAAMkR,EAAW3Q,KAAKC,MAAMoG,GAC5B,GAAI,WAAYsK,EAEd,YADAb,EAAejH,OAAO8H,EAAS1I,SAGjC,GAAI,YAAa0I,EAEf,YADAD,EAAWE,QAAQ/H,OAAO8H,EAASjL,UAGrC,GAAI,UAAWiL,EAAU,CACvB,MAAM7Q,EAAQuK,kBAAkB,EAAGsG,GAGnC,OAFAD,EAAW5Q,MAAMA,QACjBiQ,EAAejQ,EAEhB,CACF,CAAC,MAAOA,GACP,GAAIA,aAAiBqK,eAGnB,OAFAuG,EAAW5Q,MAAMA,QACjBiQ,EAAejQ,EAIlB,GAGG+Q,EAAU,IAAIC,YACpB,OAAO,IAAIC,eAAe,CACxB,KAAAC,CAAMN,GACJ,IAAIO,EAAc,GAClB,OAAOC,OACPjD,eAAeiD,OACb,GAAIlB,GAAQmB,QAAS,CACnB,MAAMrR,EAAQ,IAAIqK,eAChB,YACA,yBAIF,OAFAuG,EAAW5Q,MAAMA,GACjBiQ,EAAejQ,GACRqN,QAAQC,SAChB,CACD,IACE,MAAMxG,MAAEA,EAAKwK,KAAEA,SAAeb,EAAOc,OACrC,GAAID,EAKF,OAJIH,EAAYK,QACdd,YAAYS,EAAYK,OAAQZ,QAElCA,EAAWa,QAGb,GAAIvB,GAAQmB,QAAS,CACnB,MAAMrR,EAAQ,IAAIqK,eAChB,YACA,yBAKF,OAHAuG,EAAW5Q,MAAMA,GACjBiQ,EAAejQ,cACTyQ,EAAOd,QAEd,CACDwB,GAAeJ,EAAQhI,OAAOjC,EAAO,CAAE6H,QAAQ,IAC/C,MAAM+C,EAAQP,EAAYQ,MAAM,MAChCR,EAAcO,EAAME,OAAS,GAC7B,IAAK,MAAMjB,KAAQe,EACbf,EAAKa,QACPd,YAAYC,EAAKa,OAAQZ,GAG7B,OAAOQ,MACR,CAAC,MAAOpR,GACP,MAAM6R,EACJ7R,aAAiBqK,eACbrK,EACAuK,kBAAkB,EAAG,MAC3BqG,EAAW5Q,MAAM6R,GACjB5B,EAAe4B,EAChB,CACF,CACF,EACDlC,OAAM,IACGc,EAAOd,UAGpB,CA5IkBa,CADDzB,EAASzJ,KAAMwM,YAG5B9B,EACAC,EACAlC,GAASmC,QAEX,MAAO,CACLvB,OAAQ,CACN,CAACwB,OAAOC,iBACN,MAAM2B,EAAUxB,EAAQuB,YACxB,MAAO,CACL,UAAMzB,GACJ,MAAMvJ,MAAEA,EAAKwK,KAAEA,SAAeS,EAAQR,OACtC,MAAO,CAAEzK,MAAOA,EAAkBwK,OACnC,EACDnD,OAAY,gBACJ4D,EAAQpC,SACP,CAAE2B,MAAM,EAAMxK,WAAO+D,IAGjC,GAEHtE,KAAM+J,EAEV,0CCpdM,SAAU0B,aACdjH,EAAmBkH,IACnBnF,EAA+BH,GAG/B,MAIMsB,EAJ2CiE,aAC/ClL,mBAAmB+D,GACnB7B,GAE0CwC,aAAa,CACvDyG,WAAYrF,IAERxK,EAAW5B,kCAAkC,aAInD,OAHI4B,GACF0L,yBAAyBC,KAAsB3L,GAE1C2L,CACT,CAWgB,SAAAD,yBACdC,EACArN,EACAK,GAEAmR,2BACEpL,mBAAqCiH,GACrCrN,EACAK,EAEJ,CAOgB,SAAAuN,cAKdP,EACA/O,EACA6O,GAEA,OAAOsE,gBACLrL,mBAAqCiH,GACrC/O,EACA6O,EAEJ,CAOgB,SAAAuE,qBAKdrE,EACA5M,EACA0M,GAEA,ODuGcuE,SAAAA,uBAKdrE,EACA5M,EACA0M,GAEA,MAAMU,SACJlI,GAEOmI,UAAUT,EAAmB5M,EAAKkF,EAAMwH,GAAW,CAAA,GAS5D,OANAU,SAASE,OAAS,CAChBpI,EACAwH,IAEOa,YAAYX,EAAmB5M,EAAKkF,EAAMwH,GAAW,CAAA,GAEvDU,QACT,CC7HS8D,CACLvL,mBAAqCiH,GACrC5M,EACA0M,EAEJ,ECxFM,SAAUyE,kBAAkBC,GAqBhCC,EACE,IAAIxL,UACFgC,GAtB0C,CAC5CyJ,GACEC,mBAAoB9F,MAGtB,MAAM/B,EAAM4H,EAAUE,YAAY,OAAOnH,eACnCV,EAAe2H,EAAUE,YAbkB,iBAc3C5H,EAAoB0H,EAAUE,YAVtC,sBAWQ3H,EAAmByH,EAAUE,YAbrC,sBAgBE,OAAO,IAAIhG,iBACT9B,EACAC,EACAC,EACAC,EACA4B,EACD,GAQA,UAACnF,sBAAqB,IAGzBmL,EAAgB5T,EAAM6T,EAASN,GAE/BK,EAAgB5T,EAAM6T,EAAS,UACjC,CC1CAP", "preExistingComment": "firebase-functions.js.map"}