((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(nt,it){try{!(function(){function L(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let M=L(nt),B=function(t){var r=[];let s=0;for(let n=0;n<t.length;n++){let e=t.charCodeAt(n);e<128?r[s++]=e:(e<2048?r[s++]=e>>6|192:(55296==(64512&e)&&n+1<t.length&&56320==(64512&t.charCodeAt(n+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++n)),r[s++]=e>>18|240,r[s++]=e>>12&63|128):r[s++]=e>>12|224,r[s++]=e>>6&63|128),r[s++]=63&e|128)}return r},F={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var s=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,n=[];for(let u=0;u<r.length;u+=3){var i=r[u],a=u+1<r.length,o=a?r[u+1]:0,h=u+2<r.length,l=h?r[u+2]:0;let e=(15&o)<<2|l>>6,t=63&l;h||(t=64,a)||(e=64),n.push(s[i>>2],s[(3&i)<<4|o>>4],s[e],s[t])}return n.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(B(e),t)},decodeString(r,s){if(this.HAS_NATIVE_SUPPORT&&!s)return atob(r);{var n=this.decodeStringToByteArray(r,s);var i=[];let e=0,t=0;for(;e<n.length;){var a,o,h,l=n[e++];l<128?i[t++]=String.fromCharCode(l):191<l&&l<224?(a=n[e++],i[t++]=String.fromCharCode((31&l)<<6|63&a)):239<l&&l<365?(a=((7&l)<<18|(63&n[e++])<<12|(63&n[e++])<<6|63&n[e++])-65536,i[t++]=String.fromCharCode(55296+(a>>10)),i[t++]=String.fromCharCode(56320+(1023&a))):(o=n[e++],h=n[e++],i[t++]=String.fromCharCode((15&l)<<12|(63&o)<<6|63&h))}return i.join("");return}},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_,s=[];for(let h=0;h<e.length;){var n=r[e.charAt(h++)],i=h<e.length?r[e.charAt(h)]:0,a=++h<e.length?r[e.charAt(h)]:64,o=++h<e.length?r[e.charAt(h)]:64;if(++h,null==n||null==i||null==a||null==o)throw new H;s.push(n<<2|i>>4),64!==a&&(s.push(i<<4&240|a>>2),64!==o)&&s.push(a<<6&192|o)}return s},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class H extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let q=function(e){var t=B(e);return F.encodeByteArray(t,!0)},V=function(e){return q(e).replace(/\./g,"")};function a(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch{return!1}}let s={};let W=!1;function z(e,t){if("undefined"!=typeof window&&"undefined"!=typeof document&&a(window.location.host)&&s[e]!==t&&!s[e]&&!W){s[e]=t;let l="__firebase__banner";let u=0<(()=>{var e,t={prod:[],emulator:[]};for(e of Object.keys(s))(s[e]?t.emulator:t.prod).push(e);return t})().prod.length;function c(e){return"__firebase__banner__"+e}function d(){var e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{var e;W=!0,(e=document.getElementById(l))&&e.remove()},e}function r(){var e,t,r=(e=>{let t=document.getElementById(e),r=!1;return t||((t=document.createElement("div")).setAttribute("id",e),r=!0),{created:r,element:t}})(l),s=c("text"),n=document.getElementById(s)||document.createElement("span"),i=c("learnmore"),a=document.getElementById(i)||document.createElement("a"),o=c("preprendIcon"),h=document.getElementById(o)||document.createElementNS("http://www.w3.org/2000/svg","svg");r.created&&(r=r.element,(t=r).style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center",(t=a).setAttribute("id",i),t.innerText="Learn more",t.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",t.setAttribute("target","__blank"),t.style.paddingLeft="5px",t.style.textDecoration="underline",i=d(),t=o,(e=h).setAttribute("width","24"),e.setAttribute("id",t),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px",r.append(h,n,a,i),document.body.appendChild(r)),u?(n.innerText="Preview backend disconnected.",h.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(h.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,n.innerText="Preview backend running in this workspace."),n.setAttribute("id",s)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",r):r()}}class o extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,o.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,j.prototype.create)}}class j{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var s,r=t[0]||{},n=this.service+"/"+e,i=this.errors[e],i=i?(s=r,i.replace(G,(e,t)=>{var r=s[t];return null!=r?String(r):`<${t}?>`})):"Error",i=this.serviceName+`: ${i} (${n}).`;return new o(n,i,r)}}let G=/\{\$([^}]+)}/g;function n(e){return e&&e._delegate?e._delegate:e}class X{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let Z="firebasestorage.googleapis.com",$="storageBucket";class p extends o{constructor(e,t,r=0){super(h(e),`Firebase Storage: ${t} (${h(e)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,p.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return h(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=this._baseMessage+`
`+this.customData.serverResponse:this.message=this._baseMessage}}var f,i,e,K;function h(e){return"storage/"+e}function l(){return new p(f.UNKNOWN,"An unknown error occurred, please check the error payload for server response.")}function J(){return new p(f.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function Y(){return new p(f.CANCELED,"User canceled the upload/download.")}function Q(){return new p(f.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.")}function u(e){return new p(f.INVALID_ARGUMENT,e)}function ee(){return new p(f.APP_DELETED,"The Firebase app was deleted.")}function te(e){return new p(f.INVALID_ROOT_OPERATION,"The operation '"+e+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}function c(e,t){return new p(f.INVALID_FORMAT,"String does not match format '"+e+"': "+t)}function d(e){throw new p(f.INTERNAL_ERROR,"Internal error: "+e)}(e=f=f||{}).UNKNOWN="unknown",e.OBJECT_NOT_FOUND="object-not-found",e.BUCKET_NOT_FOUND="bucket-not-found",e.PROJECT_NOT_FOUND="project-not-found",e.QUOTA_EXCEEDED="quota-exceeded",e.UNAUTHENTICATED="unauthenticated",e.UNAUTHORIZED="unauthorized",e.UNAUTHORIZED_APP="unauthorized-app",e.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",e.INVALID_CHECKSUM="invalid-checksum",e.CANCELED="canceled",e.INVALID_EVENT_NAME="invalid-event-name",e.INVALID_URL="invalid-url",e.INVALID_DEFAULT_BUCKET="invalid-default-bucket",e.NO_DEFAULT_BUCKET="no-default-bucket",e.CANNOT_SLICE_BLOB="cannot-slice-blob",e.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",e.NO_DOWNLOAD_URL="no-download-url",e.INVALID_ARGUMENT="invalid-argument",e.INVALID_ARGUMENT_COUNT="invalid-argument-count",e.APP_DELETED="app-deleted",e.INVALID_ROOT_OPERATION="invalid-root-operation",e.INVALID_FORMAT="invalid-format",e.INTERNAL_ERROR="internal-error",e.UNSUPPORTED_ENVIRONMENT="unsupported-environment";class _{constructor(e,t){this.bucket=e,this.path_=t}get path(){return this.path_}get isRoot(){return 0===this.path.length}fullServerUrl(){var e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(t,e){let r;try{r=_.makeFromUrl(t,e)}catch(e){return new _(t,"")}if(""===r.path)return r;throw e=t,new p(f.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+e+"'.")}static makeFromUrl(e,t){let r=null;var s="([A-Za-z0-9.\\-_]+)";var n=new RegExp("^gs://"+s+"(/(.*))?$","i");function i(e){e.path_=decodeURIComponent(e.path)}var a=t.replace(/[.]/g,"\\."),a=new RegExp(`^https?://${a}/v[A-Za-z0-9_]+/b/${s}/o(/([^?#]*).*)?$`,"i"),o=t===Z?"(?:storage.googleapis.com|storage.cloud.google.com)":t,h=[{regex:n,indices:{bucket:1,path:3},postModify:function(e){"/"===e.path.charAt(e.path.length-1)&&(e.path_=e.path_.slice(0,-1))}},{regex:a,indices:{bucket:1,path:3},postModify:i},{regex:new RegExp(`^https?://${o}/${s}/([^?#]*)`,"i"),indices:{bucket:1,path:2},postModify:i}];for(let d=0;d<h.length;d++){var l=h[d],u=l.regex.exec(e);if(u){var c=u[l.indices.bucket];let e=u[l.indices.path];e=e||"",r=new _(c,e),l.postModify(r);break}}if(null==r)throw t=e,new p(f.INVALID_URL,"Invalid URL '"+t+"'.");return r}}class re{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=0){}}function g(e){return"string"==typeof e||e instanceof String}function se(e){return m()&&e instanceof Blob}function m(){return"undefined"!=typeof Blob}function b(e,t,r,s){if(s<t)throw u(`Invalid value for '${e}'. Expected ${t} or greater.`);if(r<s)throw u(`Invalid value for '${e}'. Expected ${r} or less.`)}function v(e,t,r){let s=null==r?"https://"+t:t;return`${r}://${s}/v0`+e}function ne(e){var t,r,s=encodeURIComponent;let n="?";for(t in e)e.hasOwnProperty(t)&&(r=s(t)+"="+s(e[t]),n=n+r+"&");return n=n.slice(0,-1)}function ie(e,t){var r=500<=e&&e<600,s=-1!==[408,429].indexOf(e),n=-1!==t.indexOf(e);return r||s||n}(e=i=i||{})[e.NO_ERROR=0]="NO_ERROR",e[e.NETWORK_ERROR=1]="NETWORK_ERROR",e[e.ABORT=2]="ABORT";class ae{constructor(e,t,r,s,n,i,a,o,h,l,u,c=!0,d=!1){this.url_=e,this.method_=t,this.headers_=r,this.body_=s,this.successCodes_=n,this.additionalRetryCodes_=i,this.callback_=a,this.errorCallback_=o,this.timeout_=h,this.progressCallback_=l,this.connectionFactory_=u,this.retry=c,this.isUsingEmulator=d,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((e,t)=>{this.resolve_=e,this.reject_=t,this.start_()})}start_(){var e=(n,e)=>{if(e)n(!1,new T(!1,null,!0));else{let r=this.connectionFactory_(),s=(this.pendingConnection_=r,e=>{var t=e.loaded,r=e.lengthComputable?e.total:-1;null!==this.progressCallback_&&this.progressCallback_(t,r)});null!==this.progressCallback_&&r.addUploadProgressListener(s),r.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then(()=>{null!==this.progressCallback_&&r.removeUploadProgressListener(s),this.pendingConnection_=null;var e=r.getErrorCode()===i.NO_ERROR,t=r.getStatus();!e||ie(t,this.additionalRetryCodes_)&&this.retry?(e=r.getErrorCode()===i.ABORT,n(!1,new T(!1,null,e))):(e=-1!==this.successCodes_.indexOf(t),n(!0,new T(e,r)))})}},t=(e,t)=>{var r=this.resolve_,s=this.reject_,n=t.connection;if(t.wasSuccessCode)try{var i=this.callback_(n,n.getResponse());void 0!==i?r(i):r()}catch(e){s(e)}else null!==n?((i=l()).serverResponse=n.getErrorText(),this.errorCallback_?s(this.errorCallback_(n,i)):s(i)):t.canceled?s((this.appDelete_?ee:Y)()):s(J())};this.canceled_?t(0,new T(!1,null,!0)):this.backoffId_=((t,r,e)=>{let s=1,n=null,i=null,a=!1,o=0;function h(){return 2===o}let l=!1;function u(...e){l||(l=!0,r.apply(null,e))}function c(e){n=setTimeout(()=>{n=null,t(_,h())},e)}function d(){i&&clearTimeout(i)}function _(e,...t){if(l)d();else if(e)d(),u.call(null,e,...t);else if(h()||a)d(),u.call(null,e,...t);else{s<64&&(s*=2);let e;c(e=1===o?(o=2,0):1e3*(s+Math.random()))}}let p=!1;function f(e){p||(p=!0,d(),l)||(null!==n?(e||(o=2),clearTimeout(n),c(0)):e||(o=1))}return c(0),i=setTimeout(()=>{f(a=!0)},e),f})(e,t,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,null!==this.backoffId_&&(0,this.backoffId_)(!1),null!==this.pendingConnection_&&this.pendingConnection_.abort()}}class T{constructor(e,t,r){this.wasSuccessCode=e,this.connection=t,this.canceled=!!r}}function oe(...t){var r="undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:void 0;if(void 0!==r){var s=new r;for(let e=0;e<t.length;e++)s.append(t[e]);return s.getBlob()}if(m())return new Blob(t);throw new p(f.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}function he(e){if("undefined"==typeof atob)throw new p(f.UNSUPPORTED_ENVIRONMENT,"base-64 is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.");return atob(e)}let y={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"};class w{constructor(e,t){this.data=e,this.contentType=t||null}}function le(e,t){switch(e){case y.RAW:return new w(ue(t));case y.BASE64:case y.BASE64URL:return new w(ce(e,t));case y.DATA_URL:return new w((r=t,(s=new de(r)).base64?ce(y.BASE64,s.rest):(e=>{let t;try{t=decodeURIComponent(e)}catch(e){throw c(y.DATA_URL,"Malformed data URL.")}return ue(t)})(s.rest)),(r=t,new de(r).contentType))}var r,s;throw l()}function ue(t){var r,s,n=[];for(let i=0;i<t.length;i++){let e=t.charCodeAt(i);e<=127?n.push(e):e<=2047?n.push(192|e>>6,128|63&e):55296==(64512&e)?i<t.length-1&&56320==(64512&t.charCodeAt(i+1))?(r=e,s=t.charCodeAt(++i),e=65536|(1023&r)<<10|1023&s,n.push(240|e>>18,128|e>>12&63,128|e>>6&63,128|63&e)):n.push(239,191,189):56320==(64512&e)?n.push(239,191,189):n.push(224|e>>12,128|e>>6&63,128|63&e)}return new Uint8Array(n)}function ce(t,e){switch(t){case y.BASE64:var r=-1!==e.indexOf("-"),s=-1!==e.indexOf("_");if(r||s)throw c(t,"Invalid character '"+(r?"-":"_")+"' found: is it base64url encoded?");break;case y.BASE64URL:s=-1!==e.indexOf("+"),r=-1!==e.indexOf("/");if(s||r)throw c(t,"Invalid character '"+(s?"+":"/")+"' found: is it base64 encoded?");e=e.replace(/-/g,"+").replace(/_/g,"/")}let n;try{n=he(e)}catch(e){if(e.message.includes("polyfill"))throw e;throw c(t,"Invalid character found")}var i=new Uint8Array(n.length);for(let a=0;a<n.length;a++)i[a]=n.charCodeAt(a);return i}class de{constructor(e){this.base64=!1,this.contentType=null;var t=e.match(/^data:([^,]+)?,/);if(null===t)throw c(y.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");var r,s,t=t[1]||null;null!=t&&(this.base64=(s=";base64",(r=t).length>=s.length&&r.substring(r.length-s.length)===s),this.contentType=this.base64?t.substring(0,t.length-";base64".length):t),this.rest=e.substring(e.indexOf(",")+1)}}class E{constructor(e,t){let r=0,s="";se(e)?(this.data_=e,r=e.size,s=e.type):e instanceof ArrayBuffer?(t?this.data_=new Uint8Array(e):(this.data_=new Uint8Array(e.byteLength),this.data_.set(new Uint8Array(e))),r=this.data_.length):e instanceof Uint8Array&&(t?this.data_=e:(this.data_=new Uint8Array(e.length),this.data_.set(e)),r=e.length),this.size_=r,this.type_=s}size(){return this.size_}type(){return this.type_}slice(e,t){var r,s,n,i;return se(this.data_)?(r=this.data_,n=e,i=t,null===(r=(s=r).webkitSlice?s.webkitSlice(n,i):s.mozSlice?s.mozSlice(n,i):s.slice?s.slice(n,i):null)?null:new E(r)):(r=new Uint8Array(this.data_.buffer,e,t-e),new E(r,!0))}static getBlob(...e){if(m())return n=e.map(e=>e instanceof E?e.data_:e),new E(oe.apply(null,n));{var n=e.map(e=>g(e)?le(y.RAW,e).data:e.data_);let t=0,r=(n.forEach(e=>{t+=e.byteLength}),new Uint8Array(t)),s=0;return n.forEach(e=>{for(let t=0;t<e.length;t++)r[s++]=e[t]}),new E(r,!0)}}uploadData(){return this.data_}}function _e(e){let t;try{t=JSON.parse(e)}catch(e){return null}return"object"!=typeof(e=t)||Array.isArray(e)?null:t}function pe(e){var t=e.lastIndexOf("/",e.length-2);return-1===t?e:e.slice(t+1)}function fe(e,t){return t}class r{constructor(e,t,r,s){this.server=e,this.local=t||e,this.writable=!!r,this.xform=s||fe}}let ge=null;function R(){var e,t;return ge||((e=[]).push(new r("bucket")),e.push(new r("generation")),e.push(new r("metageneration")),e.push(new r("name","fullPath",!0)),(t=new r("name")).xform=function(e,t){return!g(t=t)||t.length<2?t:pe(t)},e.push(t),(t=new r("size")).xform=function(e,t){return void 0!==t?Number(t):t},e.push(t),e.push(new r("timeCreated")),e.push(new r("updated")),e.push(new r("md5Hash",null,!0)),e.push(new r("cacheControl",null,!0)),e.push(new r("contentDisposition",null,!0)),e.push(new r("contentEncoding",null,!0)),e.push(new r("contentLanguage",null,!0)),e.push(new r("contentType",null,!0)),e.push(new r("metadata","customMetadata",!0)),ge=e),ge}function me(r,s){Object.defineProperty(r,"ref",{get:function(){var e=r.bucket,t=r.fullPath,e=new _(e,t);return s._makeStorageReference(e)}})}function be(e,t,r){var s=_e(t);if(null===s)return null;var t=e,n=s,i=r,a={type:"file"},o=i.length;for(let l=0;l<o;l++){var h=i[l];a[h.local]=h.xform(a,n[h.server])}return me(a,t),a}function ve(e,t){var r={},s=t.length;for(let i=0;i<s;i++){var n=t[i];n.writable&&(r[n.server]=e[n.local])}return JSON.stringify(r)}let Te="prefixes",ye="items";function we(e,t,r){var s=_e(r);if(null===s)return null;var n=e,i=t,r=s,a={prefixes:[],items:[],nextPageToken:r.nextPageToken};if(r[Te])for(var o of r[Te]){o=o.replace(/\/$/,""),o=n._makeStorageReference(new _(i,o));a.prefixes.push(o)}if(r[ye])for(var h of r[ye]){h=n._makeStorageReference(new _(i,h.name));a.items.push(h)}return a}class C{constructor(e,t,r,s){this.url=e,this.method=t,this.handler=r,this.timeout=s,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}}function k(e){if(!e)throw l()}function A(s,n){return function(e,t){var r=be(s,t,n);return k(null!==r),r}}function Ee(s,n){return function(e,t){var r=we(s,n,t);return k(null!==r),r}}function Re(o,s){return function(e,t){var r=be(o,t,s);k(null!==r);{var n=r,i=o.host,a=o._protocol;if(null===(r=_e(t)))return null;if(!g(r.downloadTokens))return null;if(0===(r=r.downloadTokens).length)return null;let s=encodeURIComponent;return r.split(",").map(e=>{var t=n.bucket,r=n.fullPath;return v("/b/"+s(t)+"/o/"+s(r),i,a)+ne({alt:"media",token:e})})[0]}}}function S(n){return function(e,t){let r;var s;return(r=401===e.getStatus()?e.getErrorText().includes("Firebase App Check token is invalid")?new p(f.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project."):new p(f.UNAUTHENTICATED,"User is not authenticated, please authenticate using Firebase Authentication and try again."):402===e.getStatus()?(s=n.bucket,new p(f.QUOTA_EXCEEDED,"Quota for bucket '"+s+"' exceeded, please view quota on https://firebase.google.com/pricing/.")):403===e.getStatus()?(s=n.path,new p(f.UNAUTHORIZED,"User does not have permission to access '"+s+"'.")):t).status=e.getStatus(),r.serverResponse=t.serverResponse,r}}function U(s){let n=S(s);return function(e,t){let r=n(e,t);return(r=404===e.getStatus()?(e=s.path,new p(f.OBJECT_NOT_FOUND,"Object '"+e+"' does not exist.")):r).serverResponse=t.serverResponse,r}}function Ce(e,t,r){var s=v(t.fullServerUrl(),e.host,e._protocol),n=e.maxOperationRetryTime,s=new C(s,"GET",A(e,r),n);return s.errorHandler=U(t),s}function ke(e,t,r){var s=Object.assign({},r);return s.fullPath=e.path,s.size=t.size(),s.contentType||(s.contentType=(r=t,(e=null)&&e.contentType||r&&r.type()||"application/octet-stream")),s}function Ae(e,t,r,s,n){var i=t.bucketOnlyServerUrl(),a={"X-Goog-Upload-Protocol":"multipart"};var o=(()=>{let e="";for(let t=0;t<2;t++)e+=Math.random().toString().slice(2);return e})(),h=(a["Content-Type"]="multipart/related; boundary="+o,ke(t,s,n)),l="--"+o+"\r\nContent-Type: application/json; charset=utf-8\r\n\r\n"+ve(h,r)+"\r\n--"+o+"\r\nContent-Type: "+h.contentType+"\r\n\r\n",o="\r\n--"+o+"--",l=E.getBlob(l,s,o);if(null===l)throw Q();o={name:h.fullPath},h=v(i,e.host,e._protocol),i=e.maxUploadRetryTime,h=new C(h,"POST",A(e,r),i);return h.urlParams=o,h.headers=a,h.body=l.uploadData(),h.errorHandler=S(t),h}class O{constructor(e,t,r,s){this.current=e,this.total=t,this.finalized=!!r,this.metadata=s||null}}function Se(e,t){let r=null;try{r=e.getResponseHeader("X-Goog-Upload-Status")}catch(e){k(!1)}return k(!!r&&-1!==(t||["active"]).indexOf(r)),r}function Ue(e,t,r,s,n){var i=t.bucketOnlyServerUrl(),a=ke(t,s,n),o={name:a.fullPath},i=v(i,e.host,e._protocol),h={"X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":""+s.size(),"X-Goog-Upload-Header-Content-Type":a.contentType,"Content-Type":"application/json; charset=utf-8"},a=ve(a,r),l=e.maxUploadRetryTime;i=new C(i,"POST",function(e){Se(e);let t;try{t=e.getResponseHeader("X-Goog-Upload-URL")}catch(e){k(!1)}return k(g(t)),t},l);return i.urlParams=o,i.headers=h,i.body=a,i.errorHandler=S(t),i}function Oe(e,t,r,n){var s=e.maxUploadRetryTime,s=new C(r,"POST",function(e){var t=Se(e,["active","final"]);let r=null;try{r=e.getResponseHeader("X-Goog-Upload-Size-Received")}catch(e){k(!1)}r||k(!1);var s=Number(r);return k(!isNaN(s)),new O(s,n.size(),"final"===t)},s);return s.headers={"X-Goog-Upload-Command":"query"},s.errorHandler=S(t),s}function xe(e,a,t,o,r,h,s,n){let l=new O(0,0);if(s?(l.current=s.current,l.total=s.total):(l.current=0,l.total=o.size()),o.size()!==l.total)throw new p(f.SERVER_FILE_WRONG_SIZE,"Server recorded incorrect upload file size, please retry the upload.");var i=l.total-l.current;let u=i;0<r&&(u=Math.min(u,r));var c=l.current,d=c+u;let _="";i={"X-Goog-Upload-Command":_=0===u?"finalize":i===u?"upload, finalize":"upload","X-Goog-Upload-Offset":""+l.current},c=o.slice(c,d);if(null===c)throw Q();d=a.maxUploadRetryTime,d=new C(t,"POST",function(e,t){var r=Se(e,["active","final"]),s=l.current+u,n=o.size();let i;return i="final"===r?A(a,h)(e,t):null,new O(s,n,"final"===r,i)},d);return d.headers=i,d.body=c.uploadData(),d.progressCallback=n||null,d.errorHandler=S(e),d}let Ne={STATE_CHANGED:"state_changed"},t={RUNNING:"running",PAUSED:"paused",SUCCESS:"success",CANCELED:"canceled",ERROR:"error"};function Ie(e){switch(e){case"running":case"pausing":case"canceling":return t.RUNNING;case"paused":return t.PAUSED;case"success":return t.SUCCESS;case"canceled":return t.CANCELED;default:return t.ERROR}}class Pe{constructor(e,t,r){var s;"function"==typeof e||null!=t||null!=r?(this.next=e,this.error=t??void 0,this.complete=r??void 0):(this.next=(s=e).next,this.error=s.error,this.complete=s.complete)}}function x(t){return(...e)=>{Promise.resolve().then(()=>t(...e))}}class De extends class{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=i.NO_ERROR,this.sendPromise_=new Promise(e=>{this.xhr_.addEventListener("abort",()=>{this.errorCode_=i.ABORT,e()}),this.xhr_.addEventListener("error",()=>{this.errorCode_=i.NETWORK_ERROR,e()}),this.xhr_.addEventListener("load",()=>{e()})})}send(e,t,r,s,n){if(this.sent_)throw d("cannot .send() more than once");if(a(e)&&r&&(this.xhr_.withCredentials=!0),this.sent_=!0,this.xhr_.open(t,e,!0),void 0!==n)for(var i in n)n.hasOwnProperty(i)&&this.xhr_.setRequestHeader(i,n[i].toString());return void 0!==s?this.xhr_.send(s):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(this.sent_)return this.errorCode_;throw d("cannot .getErrorCode() before sending")}getStatus(){if(!this.sent_)throw d("cannot .getStatus() before sending");try{return this.xhr_.status}catch(e){return-1}}getResponse(){if(this.sent_)return this.xhr_.response;throw d("cannot .getResponse() before sending")}getErrorText(){if(this.sent_)return this.xhr_.statusText;throw d("cannot .getErrorText() before sending")}abort(){this.xhr_.abort()}getResponseHeader(e){return this.xhr_.getResponseHeader(e)}addUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.addEventListener("progress",e)}removeUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.removeEventListener("progress",e)}}{initXhr(){this.xhr_.responseType="text"}}function N(){return new De}class Le{isExponentialBackoffExpired(){return this.sleepTime>this.maxSleepTime}constructor(e,t,r=null){this._transferred=0,this._needToFetchStatus=!1,this._needToFetchMetadata=!1,this._observers=[],this._error=void 0,this._uploadUrl=void 0,this._request=void 0,this._chunkMultiplier=1,this._resolve=void 0,this._reject=void 0,this._ref=e,this._blob=t,this._metadata=r,this._mappings=R(),this._resumable=this._shouldDoResumable(this._blob),this._state="running",this._errorHandler=e=>{if(this._request=void 0,this._chunkMultiplier=1,e._codeEquals(f.CANCELED))this._needToFetchStatus=!0,this.completeTransitions_();else{var t=this.isExponentialBackoffExpired();if(ie(e.status,[])){if(!t)return this.sleepTime=Math.max(2*this.sleepTime,1e3),this._needToFetchStatus=!0,void this.completeTransitions_();e=J()}this._error=e,this._transition("error")}},this._metadataErrorHandler=e=>{this._request=void 0,e._codeEquals(f.CANCELED)?this.completeTransitions_():(this._error=e,this._transition("error"))},this.sleepTime=0,this.maxSleepTime=this._ref.storage.maxUploadRetryTime,this._promise=new Promise((e,t)=>{this._resolve=e,this._reject=t,this._start()}),this._promise.then(null,()=>{})}_makeProgressCallback(){let t=this._transferred;return e=>this._updateProgress(t+e)}_shouldDoResumable(e){return 262144<e.size()}_start(){"running"===this._state&&void 0===this._request&&(this._resumable?void 0===this._uploadUrl?this._createResumable():this._needToFetchStatus?this._fetchStatus():this._needToFetchMetadata?this._fetchMetadata():this.pendingTimeout=setTimeout(()=>{this.pendingTimeout=void 0,this._continueUpload()},this.sleepTime):this._oneShotUpload())}_resolveToken(r){Promise.all([this._ref.storage._getAuthToken(),this._ref.storage._getAppCheckToken()]).then(([e,t])=>{switch(this._state){case"running":r(e,t);break;case"canceling":this._transition("canceled");break;case"pausing":this._transition("paused")}})}_createResumable(){this._resolveToken((e,t)=>{var r=Ue(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata),r=this._ref.storage._makeRequest(r,N,e,t);(this._request=r).getPromise().then(e=>{this._request=void 0,this._uploadUrl=e,this._needToFetchStatus=!1,this.completeTransitions_()},this._errorHandler)})}_fetchStatus(){let s=this._uploadUrl;this._resolveToken((e,t)=>{var r=Oe(this._ref.storage,this._ref._location,s,this._blob),r=this._ref.storage._makeRequest(r,N,e,t);(this._request=r).getPromise().then(e=>{this._request=void 0,this._updateProgress(e.current),this._needToFetchStatus=!1,e.finalized&&(this._needToFetchMetadata=!0),this.completeTransitions_()},this._errorHandler)})}_continueUpload(){let n=262144*this._chunkMultiplier,i=new O(this._transferred,this._blob.size()),a=this._uploadUrl;this._resolveToken((e,t)=>{let r;try{r=xe(this._ref._location,this._ref.storage,a,this._blob,n,this._mappings,i,this._makeProgressCallback())}catch(e){return this._error=e,void this._transition("error")}var s=this._ref.storage._makeRequest(r,N,e,t,!1);(this._request=s).getPromise().then(e=>{this._increaseMultiplier(),this._request=void 0,this._updateProgress(e.current),e.finalized?(this._metadata=e.metadata,this._transition("success")):this.completeTransitions_()},this._errorHandler)})}_increaseMultiplier(){2*(262144*this._chunkMultiplier)<33554432&&(this._chunkMultiplier*=2)}_fetchMetadata(){this._resolveToken((e,t)=>{var r=Ce(this._ref.storage,this._ref._location,this._mappings),r=this._ref.storage._makeRequest(r,N,e,t);(this._request=r).getPromise().then(e=>{this._request=void 0,this._metadata=e,this._transition("success")},this._metadataErrorHandler)})}_oneShotUpload(){this._resolveToken((e,t)=>{var r=Ae(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata),r=this._ref.storage._makeRequest(r,N,e,t);(this._request=r).getPromise().then(e=>{this._request=void 0,this._metadata=e,this._updateProgress(this._blob.size()),this._transition("success")},this._errorHandler)})}_updateProgress(e){var t=this._transferred;this._transferred=e,this._transferred!==t&&this._notifyObservers()}_transition(e){if(this._state!==e)switch(e){case"canceling":case"pausing":this._state=e,void 0!==this._request?this._request.cancel():this.pendingTimeout&&(clearTimeout(this.pendingTimeout),this.pendingTimeout=void 0,this.completeTransitions_());break;case"running":var t="paused"===this._state;this._state=e,t&&(this._notifyObservers(),this._start());break;case"paused":this._state=e,this._notifyObservers();break;case"canceled":this._error=Y(),this._state=e,this._notifyObservers();break;case"error":case"success":this._state=e,this._notifyObservers()}}completeTransitions_(){switch(this._state){case"pausing":this._transition("paused");break;case"canceling":this._transition("canceled");break;case"running":this._start()}}get snapshot(){var e=Ie(this._state);return{bytesTransferred:this._transferred,totalBytes:this._blob.size(),state:e,metadata:this._metadata,task:this,ref:this._ref}}on(e,t,r,s){let n=new Pe(t||void 0,r||void 0,s||void 0);return this._addObserver(n),()=>{this._removeObserver(n)}}then(e,t){return this._promise.then(e,t)}catch(e){return this.then(null,e)}_addObserver(e){this._observers.push(e),this._notifyObserver(e)}_removeObserver(e){var t=this._observers.indexOf(e);-1!==t&&this._observers.splice(t,1)}_notifyObservers(){this._finishPromise(),this._observers.slice().forEach(e=>{this._notifyObserver(e)})}_finishPromise(){if(void 0!==this._resolve){let e=!0;switch(Ie(this._state)){case t.SUCCESS:x(this._resolve.bind(null,this.snapshot))();break;case t.CANCELED:case t.ERROR:x(this._reject.bind(null,this._error))();break;default:e=!1}e&&(this._resolve=void 0,this._reject=void 0)}}_notifyObserver(e){switch(Ie(this._state)){case t.RUNNING:case t.PAUSED:e.next&&x(e.next.bind(e,this.snapshot))();break;case t.SUCCESS:e.complete&&x(e.complete.bind(e))();break;case t.CANCELED:case t.ERROR:default:e.error&&x(e.error.bind(e,this._error))()}}resume(){var e="paused"===this._state||"pausing"===this._state;return e&&this._transition("running"),e}pause(){var e="running"===this._state;return e&&this._transition("pausing"),e}cancel(){var e="running"===this._state||"pausing"===this._state;return e&&this._transition("canceling"),e}}class I{constructor(e,t){this._service=e,t instanceof _?this._location=t:this._location=_.makeFromUrl(t,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,t){return new I(e,t)}get root(){var e=new _(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return pe(this._location.path)}get storage(){return this._service}get parent(){var e,t,r=0===(e=this._location.path).length?null:-1===(r=e.lastIndexOf("/"))?"":e.slice(0,r);return null===r?null:(t=new _(this._location.bucket,r),new I(this._service,t))}_throwIfRoot(e){if(""===this._location.path)throw te(e)}}function Me(e){let t={prefixes:[],items:[]};return async function e(t,r,s){let n={pageToken:s};let i=await Be(t,n);r.prefixes.push(...i.prefixes);r.items.push(...i.items);null!=i.nextPageToken&&await e(t,r,i.nextPageToken)}(e,t).then(()=>t)}function Be(e,t){null!=t&&"number"==typeof t.maxResults&&b("options.maxResults",1,1e3,t.maxResults);var r,s,n,i,a,o=t||{},h=(t=e.storage,r=e._location,s="/",n=o.pageToken,i=o.maxResults,o={},r.isRoot?o.prefix="":o.prefix=r.path+"/",s&&0<s.length&&(o.delimiter=s),n&&(o.pageToken=n),i&&(o.maxResults=i),a=v(a=r.bucketOnlyServerUrl(),t.host,t._protocol),h=t.maxOperationRetryTime,(a=new C(a,"GET",Ee(t,r.bucket),h)).urlParams=o,a.errorHandler=S(r),a);return e.storage.makeRequestWithTokens(h,N)}function Fe(e,t){e._throwIfRoot("updateMetadata");r=e.storage,s=e._location,t=t,n=R(),i=v(s.fullServerUrl(),r.host,r._protocol),a=ve(t,n),o=r.maxOperationRetryTime,(i=new C(i,"PATCH",A(r,n),o)).headers={"Content-Type":"application/json; charset=utf-8"},i.body=a,i.errorHandler=U(s);var r,s,n,i,a,o=i;return e.storage.makeRequestWithTokens(o,N)}function He(e){e._throwIfRoot("getDownloadURL");t=e.storage,r=e._location,s=R(),n=v(r.fullServerUrl(),t.host,t._protocol),i=t.maxOperationRetryTime,(n=new C(n,"GET",Re(t,s),i)).errorHandler=U(r);var t,r,s,n,i=n;return e.storage.makeRequestWithTokens(i,N).then(e=>{if(null===e)throw new p(f.NO_DOWNLOAD_URL,"The given file does not have any download URLs.");return e})}function qe(e){e._throwIfRoot("deleteObject");t=e.storage,s=v((r=e._location).fullServerUrl(),t.host,t._protocol),n=t.maxOperationRetryTime,(s=new C(s,"DELETE",function(e,t){},n)).successCodes=[200,204],s.errorHandler=U(r);var t,r,s,n=s;return e.storage.makeRequestWithTokens(n,N)}function Ve(e,t){r=e._location.path,s=t.split("/").filter(e=>0<e.length).join("/");var r,s=0===r.length?s:r+"/"+s,s=new _(e._location.bucket,s);return new I(e.storage,s)}function We(e,t){if(e instanceof Xe){var r=e;if(null==r._bucket)throw new p(f.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+$+"' property when initializing the app?");r=new I(r,r._bucket);return null!=t?We(r,t):r}return void 0!==t?Ve(e,t):e}function ze(e,t){if(t&&/^[A-Za-z]+:\/\//.test(t)){if(e instanceof Xe)return r=e,s=t,new I(r,s);throw u("To use ref(service, url), the first argument must be a Storage instance.")}return We(e,t);var r,s}function je(e,t){var r=t?.[$];return null==r?null:_.makeFromBucketSpec(r,e)}function Ge(e,t,r,s={}){e.host=t+":"+r;var n=a(t),n=(n&&((async e=>(await fetch(e,{credentials:"include"})).ok)(`https://${e.host}/b`),z("Storage",!0)),e._isUsingEmulator=!0,e._protocol=n?"https":"http",s).mockUserToken;n&&(e._overrideAuthToken="string"==typeof n?n:((e,t)=>{if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');var r=t||"demo-project",s=e.iat||0,n=e.sub||e.user_id;if(n)return r={iss:"https://securetoken.google.com/"+r,aud:r,iat:s,exp:s+3600,auth_time:s,sub:n,user_id:n,firebase:{sign_in_provider:"custom",identities:{}},...e},[V(JSON.stringify({alg:"none",type:"JWT"})),V(JSON.stringify(r)),""].join(".");throw new Error("mockUserToken must contain 'sub' or 'user_id' field!")})(n,e.app.options.projectId))}class Xe{constructor(e,t,r,s,n,i=!1){this.app=e,this._authProvider=t,this._appCheckProvider=r,this._url=s,this._firebaseVersion=n,this._isUsingEmulator=i,this._bucket=null,this._host=Z,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=12e4,this._maxUploadRetryTime=6e5,this._requests=new Set,this._bucket=null!=s?_.makeFromBucketSpec(s,this._host):je(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,null!=this._url?this._bucket=_.makeFromBucketSpec(this._url,e):this._bucket=je(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){b("time",0,Number.POSITIVE_INFINITY,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){b("time",0,Number.POSITIVE_INFINITY,e),this._maxOperationRetryTime=e}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;var e=this._authProvider.getImmediate({optional:!0});if(e){e=await e.getToken();if(null!==e)return e.accessToken}return null}async _getAppCheckToken(){var e;return it._isFirebaseServerApp(this.app)&&this.app.settings.appCheckToken?this.app.settings.appCheckToken:(e=this._appCheckProvider.getImmediate({optional:!0}))?(await e.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(e=>e.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new I(this,e)}_makeRequest(t,r,s,n,i=!0){if(this._deleted)return new re(ee());{[t,s,n,r,i,a,o=!0,h=!1]=[t,this._appId,s,n,r,this._firebaseVersion,i,this._isUsingEmulator],u=ne(t.urlParams),u=t.url+u,c=Object.assign({},t.headers),l=c,(s=s)&&(l["X-Firebase-GMPID"]=s),l=c,null!==(s=n)&&0<s.length&&(l.Authorization="Firebase "+s),c["X-Firebase-Storage-Version"]="webjs/"+(a??"AppManager"),n=c,null!==(l=r)&&(n["X-Firebase-AppCheck"]=l);let e=new ae(u,t.method,c,t.body,t.successCodes,t.additionalRetryCodes,t.handler,t.errorHandler,t.timeout,t.progressCallback,i,o,h);return this._requests.add(e),e.getPromise().then(()=>this._requests.delete(e),()=>this._requests.delete(e)),e}var a,o,h,l,u,c}async makeRequestWithTokens(e,t){var[r,s]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,t,r,s).getPromise()}}let Ze="@firebase/storage";function $e(e,t,r){return e=n(e),t=t,r=r,(e=e)._throwIfRoot("uploadBytesResumable"),new Le(e,new E(t),r)}function Ke(e){return e=n(e),(e=e)._throwIfRoot("getMetadata"),t=Ce(e.storage,e._location,R()),e.storage.makeRequestWithTokens(t,N);var t}function Je(e,t){return ze(e=n(e),t)}function Ye(e,{instanceIdentifier:t}){var r=e.getProvider("app").getImmediate(),s=e.getProvider("auth-internal"),n=e.getProvider("app-check-internal");return new Xe(r,s,n,t,it.SDK_VERSION)}it._registerComponent(new X("storage",Ye,"PUBLIC").setMultipleInstances(!0)),it.registerVersion(Ze,"0.14.0",""),it.registerVersion(Ze,"0.14.0","esm2020");class P{constructor(e,t,r){this._delegate=e,this.task=t,this.ref=r}get bytesTransferred(){return this._delegate.bytesTransferred}get metadata(){return this._delegate.metadata}get state(){return this._delegate.state}get totalBytes(){return this._delegate.totalBytes}}class Qe{constructor(e,t){this._delegate=e,this._ref=t,this.cancel=this._delegate.cancel.bind(this._delegate),this.catch=this._delegate.catch.bind(this._delegate),this.pause=this._delegate.pause.bind(this._delegate),this.resume=this._delegate.resume.bind(this._delegate)}get snapshot(){return new P(this._delegate.snapshot,this,this._ref)}then(t,e){return this._delegate.then(e=>{if(t)return t(new P(e,this,this._ref))},e)}on(e,t,r,s){let n=void 0;return t&&(n="function"==typeof t?e=>t(new P(e,this,this._ref)):{next:t.next?e=>t.next(new P(e,this,this._ref)):void 0,complete:t.complete||void 0,error:t.error||void 0}),this._delegate.on(e,n,r||void 0,s||void 0)}}class et{constructor(e,t){this._delegate=e,this._service=t}get prefixes(){return this._delegate.prefixes.map(e=>new D(e,this._service))}get items(){return this._delegate.items.map(e=>new D(e,this._service))}get nextPageToken(){return this._delegate.nextPageToken||null}}class D{constructor(e,t){this._delegate=e,this.storage=t}get name(){return this._delegate.name}get bucket(){return this._delegate.bucket}get fullPath(){return this._delegate.fullPath}toString(){return this._delegate.toString()}child(e){var t=Ve(this._delegate,e);return new D(t,this.storage)}get root(){return new D(this._delegate.root,this.storage)}get parent(){var e=this._delegate.parent;return null==e?null:new D(e,this.storage)}put(e,t){return this._throwIfRoot("put"),new Qe($e(this._delegate,e,t),this)}putString(e,t=y.RAW,r){this._throwIfRoot("putString");var s=le(t,e),n={...r};return null==n.contentType&&null!=s.contentType&&(n.contentType=s.contentType),new Qe(new Le(this._delegate,new E(s.data,!0),n),this)}listAll(){return Me(n(this._delegate)).then(e=>new et(e,this.storage))}list(e){return t=this._delegate,e=e||void 0,Be(t=n(t),e).then(e=>new et(e,this.storage));var t}getMetadata(){return Ke(this._delegate)}updateMetadata(e){return Fe(n(this._delegate),e)}getDownloadURL(){return He(n(this._delegate))}delete(){return this._throwIfRoot("delete"),qe(n(this._delegate))}_throwIfRoot(e){if(""===this._delegate._location.path)throw te(e)}}class tt{constructor(e,t){this.app=e,this._delegate=t}get maxOperationRetryTime(){return this._delegate.maxOperationRetryTime}get maxUploadRetryTime(){return this._delegate.maxUploadRetryTime}ref(e){if(rt(e))throw u("ref() expected a child path but got a URL, use refFromURL instead.");return new D(Je(this._delegate,e),this)}refFromURL(e){if(!rt(e))throw u("refFromURL() expected a full URL but got a child path, use ref() instead.");try{_.makeFromUrl(e,this._delegate.host)}catch(e){throw u("refFromUrl() expected a valid full URL but got an invalid one.")}return new D(Je(this._delegate,e),this)}setMaxUploadRetryTime(e){this._delegate.maxUploadRetryTime=e}setMaxOperationRetryTime(e){this._delegate.maxOperationRetryTime=e}useEmulator(e,t,r={}){var s;[e,t,r,s={}]=[this._delegate,e,t,r],Ge(e,t,r,s)}}function rt(e){return/^[A-Za-z]+:\/\//.test(e)}function st(e,{instanceIdentifier:t}){var r=e.getProvider("app-compat").getImmediate(),s=e.getProvider("storage").getImmediate({identifier:t});return new tt(r,s)}e=M.default,K={TaskState:t,TaskEvent:Ne,StringFormat:y,Storage:tt,Reference:D},e.INTERNAL.registerComponent(new X("storage-compat",st,"PUBLIC").setServiceProps(K).setMultipleInstances(!0)),e.registerVersion("@firebase/storage-compat","0.4.0")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-storage-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-storage-compat.js.map
