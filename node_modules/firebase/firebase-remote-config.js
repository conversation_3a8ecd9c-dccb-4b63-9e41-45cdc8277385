import{registerVersion as e,_registerComponent as t,_getProvider,getApp as r,SDK_VERSION as n}from"https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";function isIndexedDBAvailable(){try{return"object"==typeof indexedDB}catch(e){return!1}}class FirebaseError extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){const r=t[0]||{},n=`${this.service}/${e}`,a=this.errors[e],i=a?function replaceTemplate(e,t){return e.replace(s,((e,r)=>{const n=t[r];return null!=n?String(n):`<${r}?>`}))}(a,r):"Error",o=`${this.serviceName}: ${i} (${n}).`;return new FirebaseError(n,o,r)}}const s=/\{\$([^}]+)}/g;function deepEqual(e,t){if(e===t)return!0;const r=Object.keys(e),n=Object.keys(t);for(const s of r){if(!n.includes(s))return!1;const r=e[s],a=t[s];if(isObject(r)&&isObject(a)){if(!deepEqual(r,a))return!1}else if(r!==a)return!1}for(const e of n)if(!r.includes(e))return!1;return!0}function isObject(e){return null!==e&&"object"==typeof e}function calculateBackoffMillis(e,t=1e3,r=2){const n=t*Math.pow(r,e),s=Math.round(.5*n*(Math.random()-.5)*2);return Math.min(144e5,n+s)}function getModularInstance(e){return e&&e._delegate?e._delegate:e}class Component{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var a;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(a||(a={}));const i={debug:a.DEBUG,verbose:a.VERBOSE,info:a.INFO,warn:a.WARN,error:a.ERROR,silent:a.SILENT},o=a.INFO,c={[a.DEBUG]:"log",[a.VERBOSE]:"log",[a.INFO]:"info",[a.WARN]:"warn",[a.ERROR]:"error"},defaultLogHandler=(e,t,...r)=>{if(t<e.logLevel)return;const n=(new Date).toISOString(),s=c[t];if(!s)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[s](`[${n}]  ${e.name}:`,...r)};class Logger{constructor(e){this.name=e,this._logLevel=o,this._logHandler=defaultLogHandler,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in a))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?i[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,a.DEBUG,...e),this._logHandler(this,a.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,a.VERBOSE,...e),this._logHandler(this,a.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,a.INFO,...e),this._logHandler(this,a.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,a.WARN,...e),this._logHandler(this,a.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,a.ERROR,...e),this._logHandler(this,a.ERROR,...e)}}let l,u;const g=new WeakMap,h=new WeakMap,f=new WeakMap,d=new WeakMap,p=new WeakMap;let m={get(e,t,r){if(e instanceof IDBTransaction){if("done"===t)return h.get(e);if("objectStoreNames"===t)return e.objectStoreNames||f.get(e);if("store"===t)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return wrap(e[t])},set:(e,t,r)=>(e[t]=r,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function wrapFunction(e){return e!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?function getCursorAdvanceMethods(){return u||(u=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}().includes(e)?function(...t){return e.apply(unwrap(this),t),wrap(g.get(this))}:function(...t){return wrap(e.apply(unwrap(this),t))}:function(t,...r){const n=e.call(unwrap(this),t,...r);return f.set(n,t.sort?t.sort():[t]),wrap(n)}}function transformCachableValue(e){return"function"==typeof e?wrapFunction(e):(e instanceof IDBTransaction&&function cacheDonePromiseForTransaction(e){if(h.has(e))return;const t=new Promise(((t,r)=>{const unlisten=()=>{e.removeEventListener("complete",complete),e.removeEventListener("error",error),e.removeEventListener("abort",error)},complete=()=>{t(),unlisten()},error=()=>{r(e.error||new DOMException("AbortError","AbortError")),unlisten()};e.addEventListener("complete",complete),e.addEventListener("error",error),e.addEventListener("abort",error)}));h.set(e,t)}(e),t=e,function getIdbProxyableTypes(){return l||(l=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}().some((e=>t instanceof e))?new Proxy(e,m):e);var t}function wrap(e){if(e instanceof IDBRequest)return function promisifyRequest(e){const t=new Promise(((t,r)=>{const unlisten=()=>{e.removeEventListener("success",success),e.removeEventListener("error",error)},success=()=>{t(wrap(e.result)),unlisten()},error=()=>{r(e.error),unlisten()};e.addEventListener("success",success),e.addEventListener("error",error)}));return t.then((t=>{t instanceof IDBCursor&&g.set(t,e)})).catch((()=>{})),p.set(t,e),t}(e);if(d.has(e))return d.get(e);const t=transformCachableValue(e);return t!==e&&(d.set(e,t),p.set(t,e)),t}const unwrap=e=>p.get(e);const w=["get","getKey","getAll","getAllKeys","count"],y=["put","add","delete","clear"],C=new Map;function getMethod(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(C.get(t))return C.get(t);const r=t.replace(/FromIndex$/,""),n=t!==r,s=y.includes(r);if(!(r in(n?IDBIndex:IDBObjectStore).prototype)||!s&&!w.includes(r))return;const method=async function(e,...t){const a=this.transaction(e,s?"readwrite":"readonly");let i=a.store;return n&&(i=i.index(t.shift())),(await Promise.all([i[r](...t),s&&a.done]))[0]};return C.set(t,method),method}!function replaceTraps(e){m=e(m)}((e=>({...e,get:(t,r,n)=>getMethod(t,r)||e.get(t,r,n),has:(t,r)=>!!getMethod(t,r)||e.has(t,r)})));const E="@firebase/installations",v="0.6.19",S=1e4,I=`w:${v}`,b="FIS_v2",T=36e5,_=new ErrorFactory("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function isServerError(e){return e instanceof FirebaseError&&e.code.includes("request-failed")}function getInstallationsEndpoint({projectId:e}){return`https://firebaseinstallations.googleapis.com/v1/projects/${e}/installations`}function extractAuthTokenInfoFromResponse(e){return{token:e.token,requestStatus:2,expiresIn:(t=e.expiresIn,Number(t.replace("s","000"))),creationTime:Date.now()};var t}async function getErrorFromResponse(e,t){const r=(await t.json()).error;return _.create("request-failed",{requestName:e,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})}function getHeaders({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function getHeadersWithAuth(e,{refreshToken:t}){const r=getHeaders(e);return r.append("Authorization",function getAuthorizationHeader(e){return`${b} ${e}`}(t)),r}async function retryIfServerError(e){const t=await e();return t.status>=500&&t.status<600?e():t}function sleep(e){return new Promise((t=>{setTimeout(t,e)}))}const F=/^[cdef][\w-]{21}$/;function generateFid(){try{const e=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16;const t=function encode(e){const t=function bufferToBase64UrlSafe(e){return btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_")}(e);return t.substr(0,22)}(e);return F.test(t)?t:""}catch{return""}}function getKey(e){return`${e.appName}!${e.appId}`}const M=new Map;function fidChanged(e,t){const r=getKey(e);callFidChangeCallbacks(r,t),function broadcastFidChange(e,t){const r=function getBroadcastChannel(){!k&&"BroadcastChannel"in self&&(k=new BroadcastChannel("[Firebase] FID Change"),k.onmessage=e=>{callFidChangeCallbacks(e.data.key,e.data.fid)});return k}();r&&r.postMessage({key:e,fid:t});!function closeBroadcastChannel(){0===M.size&&k&&(k.close(),k=null)}()}(r,t)}function callFidChangeCallbacks(e,t){const r=M.get(e);if(r)for(const e of r)e(t)}let k=null;const R="firebase-installations-store";let L=null;function getDbPromise(){return L||(L=function openDB(e,t,{blocked:r,upgrade:n,blocking:s,terminated:a}={}){const i=indexedDB.open(e,t),o=wrap(i);return n&&i.addEventListener("upgradeneeded",(e=>{n(wrap(i.result),e.oldVersion,e.newVersion,wrap(i.transaction),e)})),r&&i.addEventListener("blocked",(e=>r(e.oldVersion,e.newVersion,e))),o.then((e=>{a&&e.addEventListener("close",(()=>a())),s&&e.addEventListener("versionchange",(e=>s(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),o}("firebase-installations-database",1,{upgrade:(e,t)=>{if(0===t)e.createObjectStore(R)}})),L}async function set(e,t){const r=getKey(e),n=(await getDbPromise()).transaction(R,"readwrite"),s=n.objectStore(R),a=await s.get(r);return await s.put(t,r),await n.done,a&&a.fid===t.fid||fidChanged(e,t.fid),t}async function remove(e){const t=getKey(e),r=(await getDbPromise()).transaction(R,"readwrite");await r.objectStore(R).delete(t),await r.done}async function update(e,t){const r=getKey(e),n=(await getDbPromise()).transaction(R,"readwrite"),s=n.objectStore(R),a=await s.get(r),i=t(a);return void 0===i?await s.delete(r):await s.put(i,r),await n.done,!i||a&&a.fid===i.fid||fidChanged(e,i.fid),i}async function getInstallationEntry(e){let t;const r=await update(e.appConfig,(r=>{const n=function updateOrCreateInstallationEntry(e){const t=e||{fid:generateFid(),registrationStatus:0};return clearTimedOutRequest(t)}(r),s=function triggerRegistrationIfNecessary(e,t){if(0===t.registrationStatus){if(!navigator.onLine){return{installationEntry:t,registrationPromise:Promise.reject(_.create("app-offline"))}}const r={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},n=async function registerInstallation(e,t){try{const r=await async function createInstallationRequest({appConfig:e,heartbeatServiceProvider:t},{fid:r}){const n=getInstallationsEndpoint(e),s=getHeaders(e),a=t.getImmediate({optional:!0});if(a){const e=await a.getHeartbeatsHeader();e&&s.append("x-firebase-client",e)}const i={fid:r,authVersion:b,appId:e.appId,sdkVersion:I},o={method:"POST",headers:s,body:JSON.stringify(i)},c=await retryIfServerError((()=>fetch(n,o)));if(c.ok){const e=await c.json();return{fid:e.fid||r,registrationStatus:2,refreshToken:e.refreshToken,authToken:extractAuthTokenInfoFromResponse(e.authToken)}}throw await getErrorFromResponse("Create Installation",c)}(e,t);return set(e.appConfig,r)}catch(r){throw isServerError(r)&&409===r.customData.serverCode?await remove(e.appConfig):await set(e.appConfig,{fid:t.fid,registrationStatus:0}),r}}(e,r);return{installationEntry:r,registrationPromise:n}}return 1===t.registrationStatus?{installationEntry:t,registrationPromise:waitUntilFidRegistration(e)}:{installationEntry:t}}(e,n);return t=s.registrationPromise,s.installationEntry}));return""===r.fid?{installationEntry:await t}:{installationEntry:r,registrationPromise:t}}async function waitUntilFidRegistration(e){let t=await updateInstallationRequest(e.appConfig);for(;1===t.registrationStatus;)await sleep(100),t=await updateInstallationRequest(e.appConfig);if(0===t.registrationStatus){const{installationEntry:t,registrationPromise:r}=await getInstallationEntry(e);return r||t}return t}function updateInstallationRequest(e){return update(e,(e=>{if(!e)throw _.create("installation-not-found");return clearTimedOutRequest(e)}))}function clearTimedOutRequest(e){return function hasInstallationRequestTimedOut(e){return 1===e.registrationStatus&&e.registrationTime+S<Date.now()}(e)?{fid:e.fid,registrationStatus:0}:e}async function generateAuthTokenRequest({appConfig:e,heartbeatServiceProvider:t},r){const n=function getGenerateAuthTokenEndpoint(e,{fid:t}){return`${getInstallationsEndpoint(e)}/${t}/authTokens:generate`}(e,r),s=getHeadersWithAuth(e,r),a=t.getImmediate({optional:!0});if(a){const e=await a.getHeartbeatsHeader();e&&s.append("x-firebase-client",e)}const i={installation:{sdkVersion:I,appId:e.appId}},o={method:"POST",headers:s,body:JSON.stringify(i)},c=await retryIfServerError((()=>fetch(n,o)));if(c.ok){return extractAuthTokenInfoFromResponse(await c.json())}throw await getErrorFromResponse("Generate Auth Token",c)}async function refreshAuthToken(e,t=!1){let r;const n=await update(e.appConfig,(n=>{if(!isEntryRegistered(n))throw _.create("not-registered");const s=n.authToken;if(!t&&function isAuthTokenValid(e){return 2===e.requestStatus&&!function isAuthTokenExpired(e){const t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+T}(e)}(s))return n;if(1===s.requestStatus)return r=async function waitUntilAuthTokenRequest(e,t){let r=await updateAuthTokenRequest(e.appConfig);for(;1===r.authToken.requestStatus;)await sleep(100),r=await updateAuthTokenRequest(e.appConfig);const n=r.authToken;return 0===n.requestStatus?refreshAuthToken(e,t):n}(e,t),n;{if(!navigator.onLine)throw _.create("app-offline");const t=function makeAuthTokenRequestInProgressEntry(e){const t={requestStatus:1,requestTime:Date.now()};return{...e,authToken:t}}(n);return r=async function fetchAuthTokenFromServer(e,t){try{const r=await generateAuthTokenRequest(e,t),n={...t,authToken:r};return await set(e.appConfig,n),r}catch(r){if(!isServerError(r)||401!==r.customData.serverCode&&404!==r.customData.serverCode){const r={...t,authToken:{requestStatus:0}};await set(e.appConfig,r)}else await remove(e.appConfig);throw r}}(e,t),t}}));return r?await r:n.authToken}function updateAuthTokenRequest(e){return update(e,(e=>{if(!isEntryRegistered(e))throw _.create("not-registered");return function hasAuthTokenRequestTimedOut(e){return 1===e.requestStatus&&e.requestTime+S<Date.now()}(e.authToken)?{...e,authToken:{requestStatus:0}}:e}))}function isEntryRegistered(e){return void 0!==e&&2===e.registrationStatus}async function getToken(e,t=!1){const r=e;await async function completeInstallationRegistration(e){const{registrationPromise:t}=await getInstallationEntry(e);t&&await t}(r);return(await refreshAuthToken(r,t)).token}function getMissingValueError(e){return _.create("missing-app-config-values",{valueName:e})}const A="installations",publicFactory=e=>{const t=e.getProvider("app").getImmediate(),r=function extractAppConfig(e){if(!e||!e.options)throw getMissingValueError("App Configuration");if(!e.name)throw getMissingValueError("App Name");const t=["projectId","apiKey","appId"];for(const r of t)if(!e.options[r])throw getMissingValueError(r);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}(t);return{app:t,appConfig:r,heartbeatServiceProvider:_getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},internalFactory=e=>{const t=e.getProvider("app").getImmediate(),r=_getProvider(t,A).getImmediate();return{getId:()=>async function getId(e){const t=e,{installationEntry:r,registrationPromise:n}=await getInstallationEntry(t);return n?n.catch(console.error):refreshAuthToken(t).catch(console.error),r.fid}(r),getToken:e=>getToken(r,e)}};!function registerInstallations(){t(new Component(A,publicFactory,"PUBLIC")),t(new Component("installations-internal",internalFactory,"PRIVATE"))}(),e(E,v),e(E,v,"esm2020");const D="@firebase/remote-config",P="0.6.6";class RemoteConfigAbortSignal{constructor(){this.listeners=[]}addEventListener(e){this.listeners.push(e)}abort(){this.listeners.forEach((e=>e()))}}const O="remote-config",B=new ErrorFactory("remoteconfig","Remote Config",{"already-initialized":"Remote Config already initialized","registration-window":"Undefined window object. This SDK only supports usage in a browser environment.","registration-project-id":"Undefined project identifier. Check Firebase app initialization.","registration-api-key":"Undefined API key. Check Firebase app initialization.","registration-app-id":"Undefined app identifier. Check Firebase app initialization.","storage-open":"Error thrown when opening storage. Original error: {$originalErrorMessage}.","storage-get":"Error thrown when reading from storage. Original error: {$originalErrorMessage}.","storage-set":"Error thrown when writing to storage. Original error: {$originalErrorMessage}.","storage-delete":"Error thrown when deleting from storage. Original error: {$originalErrorMessage}.","fetch-client-network":"Fetch client failed to connect to a network. Check Internet connection. Original error: {$originalErrorMessage}.","fetch-timeout":'The config fetch request timed out.  Configure timeout using "fetchTimeoutMillis" SDK setting.',"fetch-throttle":'The config fetch request timed out while in an exponential backoff state. Configure timeout using "fetchTimeoutMillis" SDK setting. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',"fetch-client-parse":"Fetch client could not parse response. Original error: {$originalErrorMessage}.","fetch-status":"Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.","indexed-db-unavailable":"Indexed DB is not supported by current browser","custom-signal-max-allowed-signals":"Setting more than {$maxSignals} custom signals is not supported."});const N=["1","true","t","yes","y","on"];class Value{constructor(e,t=""){this._source=e,this._value=t}asString(){return this._value}asBoolean(){return"static"!==this._source&&N.indexOf(this._value.toLowerCase())>=0}asNumber(){if("static"===this._source)return 0;let e=Number(this._value);return isNaN(e)&&(e=0),e}getSource(){return this._source}}function getRemoteConfig(e=r(),t={}){e=getModularInstance(e);const n=_getProvider(e,O);if(n.isInitialized()){if(deepEqual(n.getOptions(),t))return n.getImmediate();throw B.create("already-initialized")}n.initialize({options:t});const s=n.getImmediate();return t.initialFetchResponse&&(s._initializePromise=Promise.all([s._storage.setLastSuccessfulFetchResponse(t.initialFetchResponse),s._storage.setActiveConfigEtag(t.initialFetchResponse?.eTag||""),s._storageCache.setLastSuccessfulFetchTimestampMillis(Date.now()),s._storageCache.setLastFetchStatus("success"),s._storageCache.setActiveConfig(t.initialFetchResponse?.config||{})]).then(),s._isInitializationComplete=!0),s}async function activate(e){const t=getModularInstance(e),[r,n]=await Promise.all([t._storage.getLastSuccessfulFetchResponse(),t._storage.getActiveConfigEtag()]);return!!(r&&r.config&&r.eTag&&r.eTag!==n)&&(await Promise.all([t._storageCache.setActiveConfig(r.config),t._storage.setActiveConfigEtag(r.eTag)]),!0)}function ensureInitialized(e){const t=getModularInstance(e);return t._initializePromise||(t._initializePromise=t._storageCache.loadFromStorage().then((()=>{t._isInitializationComplete=!0}))),t._initializePromise}async function fetchConfig(e){const t=getModularInstance(e),r=new RemoteConfigAbortSignal;setTimeout((async()=>{r.abort()}),t.settings.fetchTimeoutMillis);const n=t._storageCache.getCustomSignals();n&&t._logger.debug(`Fetching config with custom signals: ${JSON.stringify(n)}`);try{await t._client.fetch({cacheMaxAgeMillis:t.settings.minimumFetchIntervalMillis,signal:r,customSignals:n}),await t._storageCache.setLastFetchStatus("success")}catch(e){const r=function hasErrorCode(e,t){return e instanceof FirebaseError&&-1!==e.code.indexOf(t)}(e,"fetch-throttle")?"throttle":"failure";throw await t._storageCache.setLastFetchStatus(r),e}}function getAll(e){const t=getModularInstance(e);return function getAllKeys(e={},t={}){return Object.keys({...e,...t})}(t._storageCache.getActiveConfig(),t.defaultConfig).reduce(((t,r)=>(t[r]=getValue(e,r),t)),{})}function getBoolean(e,t){return getValue(getModularInstance(e),t).asBoolean()}function getNumber(e,t){return getValue(getModularInstance(e),t).asNumber()}function getString(e,t){return getValue(getModularInstance(e),t).asString()}function getValue(e,t){const r=getModularInstance(e);r._isInitializationComplete||r._logger.debug(`A value was requested for key "${t}" before SDK initialization completed. Await on ensureInitialized if the intent was to get a previously activated value.`);const n=r._storageCache.getActiveConfig();return n&&void 0!==n[t]?new Value("remote",n[t]):r.defaultConfig&&void 0!==r.defaultConfig[t]?new Value("default",String(r.defaultConfig[t])):(r._logger.debug(`Returning static value for key "${t}". Define a default or remote value if this is unintentional.`),new Value("static"))}function setLogLevel(e,t){const r=getModularInstance(e);switch(t){case"debug":r._logger.logLevel=a.DEBUG;break;case"silent":r._logger.logLevel=a.SILENT;break;default:r._logger.logLevel=a.ERROR}}async function setCustomSignals(e,t){const r=getModularInstance(e);if(0!==Object.keys(t).length){for(const e in t){if(e.length>250)return void r._logger.error(`Custom signal key ${e} is too long, max allowed length is 250.`);const n=t[e];if("string"==typeof n&&n.length>500)return void r._logger.error(`Value supplied for custom signal ${e} is too long, max allowed length is 500.`)}try{await r._storageCache.setCustomSignals(t)}catch(e){r._logger.error(`Error encountered while setting custom signals: ${e}`)}}}class CachingClient{constructor(e,t,r,n){this.client=e,this.storage=t,this.storageCache=r,this.logger=n}isCachedDataFresh(e,t){if(!t)return this.logger.debug("Config fetch cache check. Cache unpopulated."),!1;const r=Date.now()-t,n=r<=e;return this.logger.debug(`Config fetch cache check. Cache age millis: ${r}. Cache max age millis (minimumFetchIntervalMillis setting): ${e}. Is cache hit: ${n}.`),n}async fetch(e){const[t,r]=await Promise.all([this.storage.getLastSuccessfulFetchTimestampMillis(),this.storage.getLastSuccessfulFetchResponse()]);if(r&&this.isCachedDataFresh(e.cacheMaxAgeMillis,t))return r;e.eTag=r&&r.eTag;const n=await this.client.fetch(e),s=[this.storageCache.setLastSuccessfulFetchTimestampMillis(Date.now())];return 200===n.status&&s.push(this.storage.setLastSuccessfulFetchResponse(n)),await Promise.all(s),n}}function getUserLanguage(e=navigator){return e.languages&&e.languages[0]||e.language}class RestClient{constructor(e,t,r,n,s,a){this.firebaseInstallations=e,this.sdkVersion=t,this.namespace=r,this.projectId=n,this.apiKey=s,this.appId=a}async fetch(e){const[t,r]=await Promise.all([this.firebaseInstallations.getId(),this.firebaseInstallations.getToken()]),n=`${window.FIREBASE_REMOTE_CONFIG_URL_BASE||"https://firebaseremoteconfig.googleapis.com"}/v1/projects/${this.projectId}/namespaces/${this.namespace}:fetch?key=${this.apiKey}`,s={"Content-Type":"application/json","Content-Encoding":"gzip","If-None-Match":e.eTag||"*"},a={sdk_version:this.sdkVersion,app_instance_id:t,app_instance_id_token:r,app_id:this.appId,language_code:getUserLanguage(),custom_signals:e.customSignals},i={method:"POST",headers:s,body:JSON.stringify(a)},o=fetch(n,i),c=new Promise(((t,r)=>{e.signal.addEventListener((()=>{const e=new Error("The operation was aborted.");e.name="AbortError",r(e)}))}));let l;try{await Promise.race([o,c]),l=await o}catch(e){let t="fetch-client-network";throw"AbortError"===e?.name&&(t="fetch-timeout"),B.create(t,{originalErrorMessage:e?.message})}let u=l.status;const g=l.headers.get("ETag")||void 0;let h,f;if(200===l.status){let e;try{e=await l.json()}catch(e){throw B.create("fetch-client-parse",{originalErrorMessage:e?.message})}h=e.entries,f=e.state}if("INSTANCE_STATE_UNSPECIFIED"===f?u=500:"NO_CHANGE"===f?u=304:"NO_TEMPLATE"!==f&&"EMPTY_CONFIG"!==f||(h={}),304!==u&&200!==u)throw B.create("fetch-status",{httpStatus:u});return{status:u,eTag:g,config:h}}}class RetryingClient{constructor(e,t){this.client=e,this.storage=t}async fetch(e){const t=await this.storage.getThrottleMetadata()||{backoffCount:0,throttleEndTimeMillis:Date.now()};return this.attemptFetch(e,t)}async attemptFetch(e,{throttleEndTimeMillis:t,backoffCount:r}){await function setAbortableTimeout(e,t){return new Promise(((r,n)=>{const s=Math.max(t-Date.now(),0),a=setTimeout(r,s);e.addEventListener((()=>{clearTimeout(a),n(B.create("fetch-throttle",{throttleEndTimeMillis:t}))}))}))}(e.signal,t);try{const t=await this.client.fetch(e);return await this.storage.deleteThrottleMetadata(),t}catch(t){if(!function isRetriableError(e){if(!(e instanceof FirebaseError&&e.customData))return!1;const t=Number(e.customData.httpStatus);return 429===t||500===t||503===t||504===t}(t))throw t;const n={throttleEndTimeMillis:Date.now()+calculateBackoffMillis(r),backoffCount:r+1};return await this.storage.setThrottleMetadata(n),this.attemptFetch(e,n)}}}class RemoteConfig{get fetchTimeMillis(){return this._storageCache.getLastSuccessfulFetchTimestampMillis()||-1}get lastFetchStatus(){return this._storageCache.getLastFetchStatus()||"no-fetch-yet"}constructor(e,t,r,n,s){this.app=e,this._client=t,this._storageCache=r,this._storage=n,this._logger=s,this._isInitializationComplete=!1,this.settings={fetchTimeoutMillis:6e4,minimumFetchIntervalMillis:432e5},this.defaultConfig={}}}function toFirebaseError(e,t){const r=e.target.error||void 0;return B.create(t,{originalErrorMessage:r&&r?.message})}const j="app_namespace_store";class Storage{getLastFetchStatus(){return this.get("last_fetch_status")}setLastFetchStatus(e){return this.set("last_fetch_status",e)}getLastSuccessfulFetchTimestampMillis(){return this.get("last_successful_fetch_timestamp_millis")}setLastSuccessfulFetchTimestampMillis(e){return this.set("last_successful_fetch_timestamp_millis",e)}getLastSuccessfulFetchResponse(){return this.get("last_successful_fetch_response")}setLastSuccessfulFetchResponse(e){return this.set("last_successful_fetch_response",e)}getActiveConfig(){return this.get("active_config")}setActiveConfig(e){return this.set("active_config",e)}getActiveConfigEtag(){return this.get("active_config_etag")}setActiveConfigEtag(e){return this.set("active_config_etag",e)}getThrottleMetadata(){return this.get("throttle_metadata")}setThrottleMetadata(e){return this.set("throttle_metadata",e)}deleteThrottleMetadata(){return this.delete("throttle_metadata")}getCustomSignals(){return this.get("custom_signals")}}class IndexedDbStorage extends Storage{constructor(e,t,r,n=function openDatabase(){return new Promise(((e,t)=>{try{const r=indexedDB.open("firebase_remote_config",1);r.onerror=e=>{t(toFirebaseError(e,"storage-open"))},r.onsuccess=t=>{e(t.target.result)},r.onupgradeneeded=e=>{const t=e.target.result;0===e.oldVersion&&t.createObjectStore(j,{keyPath:"compositeKey"})}}catch(e){t(B.create("storage-open",{originalErrorMessage:e?.message}))}}))}()){super(),this.appId=e,this.appName=t,this.namespace=r,this.openDbPromise=n}async setCustomSignals(e){const t=(await this.openDbPromise).transaction([j],"readwrite"),r=mergeCustomSignals(e,await this.getWithTransaction("custom_signals",t)||{});return await this.setWithTransaction("custom_signals",r,t),r}async getWithTransaction(e,t){return new Promise(((r,n)=>{const s=t.objectStore(j),a=this.createCompositeKey(e);try{const e=s.get(a);e.onerror=e=>{n(toFirebaseError(e,"storage-get"))},e.onsuccess=e=>{const t=e.target.result;r(t?t.value:void 0)}}catch(e){n(B.create("storage-get",{originalErrorMessage:e?.message}))}}))}async setWithTransaction(e,t,r){return new Promise(((n,s)=>{const a=r.objectStore(j),i=this.createCompositeKey(e);try{const e=a.put({compositeKey:i,value:t});e.onerror=e=>{s(toFirebaseError(e,"storage-set"))},e.onsuccess=()=>{n()}}catch(e){s(B.create("storage-set",{originalErrorMessage:e?.message}))}}))}async get(e){const t=(await this.openDbPromise).transaction([j],"readonly");return this.getWithTransaction(e,t)}async set(e,t){const r=(await this.openDbPromise).transaction([j],"readwrite");return this.setWithTransaction(e,t,r)}async delete(e){const t=await this.openDbPromise;return new Promise(((r,n)=>{const s=t.transaction([j],"readwrite").objectStore(j),a=this.createCompositeKey(e);try{const e=s.delete(a);e.onerror=e=>{n(toFirebaseError(e,"storage-delete"))},e.onsuccess=()=>{r()}}catch(e){n(B.create("storage-delete",{originalErrorMessage:e?.message}))}}))}createCompositeKey(e){return[this.appId,this.appName,this.namespace,e].join()}}class InMemoryStorage extends Storage{constructor(){super(...arguments),this.storage={}}async get(e){return Promise.resolve(this.storage[e])}async set(e,t){return this.storage[e]=t,Promise.resolve(void 0)}async delete(e){return this.storage[e]=void 0,Promise.resolve()}async setCustomSignals(e){const t=this.storage.custom_signals||{};return this.storage.custom_signals=mergeCustomSignals(e,t),Promise.resolve(this.storage.custom_signals)}}function mergeCustomSignals(e,t){const r={...t,...e},n=Object.fromEntries(Object.entries(r).filter((([e,t])=>null!==t)).map((([e,t])=>"number"==typeof t?[e,t.toString()]:[e,t])));if(Object.keys(n).length>100)throw B.create("custom-signal-max-allowed-signals",{maxSignals:100});return n}class StorageCache{constructor(e){this.storage=e}getLastFetchStatus(){return this.lastFetchStatus}getLastSuccessfulFetchTimestampMillis(){return this.lastSuccessfulFetchTimestampMillis}getActiveConfig(){return this.activeConfig}getCustomSignals(){return this.customSignals}async loadFromStorage(){const e=this.storage.getLastFetchStatus(),t=this.storage.getLastSuccessfulFetchTimestampMillis(),r=this.storage.getActiveConfig(),n=this.storage.getCustomSignals(),s=await e;s&&(this.lastFetchStatus=s);const a=await t;a&&(this.lastSuccessfulFetchTimestampMillis=a);const i=await r;i&&(this.activeConfig=i);const o=await n;o&&(this.customSignals=o)}setLastFetchStatus(e){return this.lastFetchStatus=e,this.storage.setLastFetchStatus(e)}setLastSuccessfulFetchTimestampMillis(e){return this.lastSuccessfulFetchTimestampMillis=e,this.storage.setLastSuccessfulFetchTimestampMillis(e)}setActiveConfig(e){return this.activeConfig=e,this.storage.setActiveConfig(e)}async setCustomSignals(e){this.customSignals=await this.storage.setCustomSignals(e)}}async function fetchAndActivate(e){return e=getModularInstance(e),await fetchConfig(e),activate(e)}async function isSupported(){if(!isIndexedDBAvailable())return!1;try{return await function validateIndexedDBOpenable(){return new Promise(((e,t)=>{try{let r=!0;const n="validate-browser-context-for-indexeddb-analytics-module",s=self.indexedDB.open(n);s.onsuccess=()=>{s.result.close(),r||self.indexedDB.deleteDatabase(n),e(!0)},s.onupgradeneeded=()=>{r=!1},s.onerror=()=>{t(s.error?.message||"")}}catch(e){t(e)}}))}()}catch(e){return!1}}!function registerRemoteConfig(){t(new Component(O,(function remoteConfigFactory(e,{options:t}){const r=e.getProvider("app").getImmediate(),s=e.getProvider("installations-internal").getImmediate(),{projectId:i,apiKey:o,appId:c}=r.options;if(!i)throw B.create("registration-project-id");if(!o)throw B.create("registration-api-key");if(!c)throw B.create("registration-app-id");const l=t?.templateId||"firebase",u=isIndexedDBAvailable()?new IndexedDbStorage(c,r.name,l):new InMemoryStorage,g=new StorageCache(u),h=new Logger(D);h.logLevel=a.ERROR;const f=new RestClient(s,n,l,i,o,c),d=new RetryingClient(f,u),p=new CachingClient(d,u,g,h),m=new RemoteConfig(r,p,g,u,h);return ensureInitialized(m),m}),"PUBLIC").setMultipleInstances(!0)),e(D,P),e(D,P,"esm2020")}();export{activate,ensureInitialized,fetchAndActivate,fetchConfig,getAll,getBoolean,getNumber,getRemoteConfig,getString,getValue,isSupported,setCustomSignals,setLogLevel};

//# sourceMappingURL=firebase-remote-config.js.map
