((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).firebase=t()})(this,function(){let F=()=>{},r=function(t){var r=[];let n=0;for(let a=0;a<t.length;a++){let e=t.charCodeAt(a);e<128?r[n++]=e:(e<2048?r[n++]=e>>6|192:(55296==(64512&e)&&a+1<t.length&&56320==(64512&t.charCodeAt(a+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++a)),r[n++]=e>>18|240,r[n++]=e>>12&63|128):r[n++]=e>>12|224,r[n++]=e>>6&63|128),r[n++]=63&e|128)}return r},M={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,a=[];for(let h=0;h<r.length;h+=3){var i=r[h],s=h+1<r.length,o=s?r[h+1]:0,c=h+2<r.length,l=c?r[h+2]:0;let e=(15&o)<<2|l>>6,t=63&l;c||(t=64,s)||(e=64),a.push(n[i>>2],n[(3&i)<<4|o>>4],n[e],n[t])}return a.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(r(e),t)},decodeString(r,n){if(this.HAS_NATIVE_SUPPORT&&!n)return atob(r);{var a=this.decodeStringToByteArray(r,n);var i=[];let e=0,t=0;for(;e<a.length;){var s,o,c,l=a[e++];l<128?i[t++]=String.fromCharCode(l):191<l&&l<224?(s=a[e++],i[t++]=String.fromCharCode((31&l)<<6|63&s)):239<l&&l<365?(s=((7&l)<<18|(63&a[e++])<<12|(63&a[e++])<<6|63&a[e++])-65536,i[t++]=String.fromCharCode(55296+(s>>10)),i[t++]=String.fromCharCode(56320+(1023&s))):(o=a[e++],c=a[e++],i[t++]=String.fromCharCode((15&l)<<12|(63&o)<<6|63&c))}return i.join("");return}},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let c=0;c<e.length;){var a=r[e.charAt(c++)],i=c<e.length?r[e.charAt(c)]:0,s=++c<e.length?r[e.charAt(c)]:64,o=++c<e.length?r[e.charAt(c)]:64;if(++c,null==a||null==i||null==s||null==o)throw new z;n.push(a<<2|i>>4),64!==s&&(n.push(i<<4&240|s>>2),64!==o)&&n.push(s<<6&192|o)}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class z extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}function x(e){var t=r(e);return M.encodeByteArray(t,!0)}let H=function(e){return x(e).replace(/\./g,"")},j=function(e){try{return M.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};function c(e,t){if(!(t instanceof Object))return t;switch(t.constructor){case Date:return new Date(t.getTime());case Object:void 0===e&&(e={});break;case Array:e=[];break;default:return t}for(var r in t)t.hasOwnProperty(r)&&"__proto__"!==r&&(e[r]=c(e[r],t[r]));return e}function V(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}let $=()=>V().__FIREBASE_DEFAULTS__,W=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},U=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&j(e[1]);return t&&JSON.parse(t)}},J=()=>{try{return F()||$()||W()||U()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}},l=()=>J()?.config;class G{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}wrapCallback(r){return(e,t)=>{e?this.reject(e):this.resolve(t),"function"==typeof r&&(this.promise.catch(()=>{}),1===r.length?r(e):r(e,t))}}}function K(){return"undefined"!=typeof WorkerGlobalScope&&"undefined"!=typeof self&&self instanceof WorkerGlobalScope}class s extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,s.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,n.prototype.create)}}class n{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,r=t[0]||{},a=this.service+"/"+e,i=this.errors[e],i=i?(n=r,i.replace(Y,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",i=this.serviceName+`: ${i} (${a}).`;return new s(a,i,r)}}let Y=/\{\$([^}]+)}/g;function X(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function h(e,t){if(e!==t){var r,n,a=Object.keys(e),i=Object.keys(t);for(r of a){if(!i.includes(r))return;var s=e[r],o=t[r];if(q(s)&&q(o)){if(!h(s,o))return}else if(s!==o)return}for(n of i)if(!a.includes(n))return}return 1}function q(e){return null!==e&&"object"==typeof e}function Z(e,t){var r=new Q(e,t);return r.subscribe.bind(r)}class Q{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let n;if(void 0===e&&void 0===t&&void 0===r)throw new Error("Missing Observer.");void 0===(n=((e,t)=>{if("object"==typeof e&&null!==e)for(var r of t)if(r in e&&"function"==typeof e[r])return 1})(e,["next","error","complete"])?e:{next:e,error:t,complete:r}).next&&(n.next=i),void 0===n.error&&(n.error=i),void 0===n.complete&&(n.complete=i);var a=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?n.error(this.finalError):n.complete()}catch(e){}}),this.observers.push(n),a}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount)&&void 0!==this.onNoObservers&&this.onNoObservers(this)}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function i(){}class o{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let a="[DEFAULT]";class ee{constructor(e,t){this.name=e,this.container=t,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){var t=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(t)){var r=new G;if(this.instancesDeferred.set(t,r),this.isInitialized(t)||this.shouldAutoInitialize())try{var n=this.getOrInitializeService({instanceIdentifier:t});n&&r.resolve(n)}catch(e){}}return this.instancesDeferred.get(t).promise}getImmediate(e){var t=this.normalizeInstanceIdentifier(e?.identifier),r=e?.optional??!1;if(!this.isInitialized(t)&&!this.shouldAutoInitialize()){if(r)return null;throw Error(`Service ${this.name} is not available`)}try{return this.getOrInitializeService({instanceIdentifier:t})}catch(e){if(r)return null;throw e}}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,this.shouldAutoInitialize()){if("EAGER"===e.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:a})}catch(e){}for(var[t,r]of this.instancesDeferred.entries()){t=this.normalizeInstanceIdentifier(t);try{var n=this.getOrInitializeService({instanceIdentifier:t});r.resolve(n)}catch(e){}}}}clearInstance(e=a){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){var e=Array.from(this.instances.values());await Promise.all([...e.filter(e=>"INTERNAL"in e).map(e=>e.INTERNAL.delete()),...e.filter(e=>"_delete"in e).map(e=>e._delete())])}isComponentSet(){return null!=this.component}isInitialized(e=a){return this.instances.has(e)}getOptions(e=a){return this.instancesOptions.get(e)||{}}initialize(e={}){var{options:t={}}=e,r=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(r))throw Error(this.name+`(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);var n,a,i=this.getOrInitializeService({instanceIdentifier:r,options:t});for([n,a]of this.instancesDeferred.entries())r===this.normalizeInstanceIdentifier(n)&&a.resolve(i);return i}onInit(e,t){var r=this.normalizeInstanceIdentifier(t);let n=this.onInitCallbacks.get(r)??new Set;n.add(e),this.onInitCallbacks.set(r,n);var a=this.instances.get(r);return a&&e(a,r),()=>{n.delete(e)}}invokeOnInitCallbacks(e,t){var r=this.onInitCallbacks.get(t);if(r)for(var n of r)try{n(e,t)}catch{}}getOrInitializeService({instanceIdentifier:e,options:t={}}){let r=this.instances.get(e);if(!r&&this.component&&(r=this.component.instanceFactory(this.container,{instanceIdentifier:(n=e)===a?void 0:n,options:t}),this.instances.set(e,r),this.instancesOptions.set(e,t),this.invokeOnInitCallbacks(r,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,r)}catch{}var n;return r||null}normalizeInstanceIdentifier(e=a){return!this.component||this.component.multipleInstances?e:a}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class te{constructor(e){this.name=e,this.providers=new Map}addComponent(e){var t=this.getProvider(e.name);if(t.isComponentSet())throw new Error(`Component ${e.name} has already been registered with `+this.name);t.setComponent(e)}addOrOverwriteComponent(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){var t;return this.providers.has(e)?this.providers.get(e):(t=new ee(e,this),this.providers.set(e,t),t)}getProviders(){return Array.from(this.providers.values())}}let d=[];var p,u;(e=p=p||{})[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT";let re={debug:p.DEBUG,verbose:p.VERBOSE,info:p.INFO,warn:p.WARN,error:p.ERROR,silent:p.SILENT},ne=p.INFO,ae={[p.DEBUG]:"log",[p.VERBOSE]:"log",[p.INFO]:"info",[p.WARN]:"warn",[p.ERROR]:"error"},ie=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),a=ae[t];if(!a)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[a](`[${n}]  ${e.name}:`,...r)}};class se{constructor(e){this.name=e,this._logLevel=ne,this._logHandler=ie,this._userLogHandler=null,d.push(this)}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in p))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?re[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,p.DEBUG,...e),this._logHandler(this,p.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,p.VERBOSE,...e),this._logHandler(this,p.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,p.INFO,...e),this._logHandler(this,p.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,p.WARN,...e),this._logHandler(this,p.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,p.ERROR,...e),this._logHandler(this,p.ERROR,...e)}}let oe=(t,e)=>e.some(e=>t instanceof e),ce,le;let he=new WeakMap,f=new WeakMap,de=new WeakMap,g=new WeakMap,b=new WeakMap;let m={get(e,t,r){if(e instanceof IDBTransaction){if("done"===t)return f.get(e);if("objectStoreNames"===t)return e.objectStoreNames||de.get(e);if("store"===t)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return v(e[t])},set(e,t,r){return e[t]=r,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function pe(n){return n!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(le=le||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(n)?function(...e){return n.apply(_(this),e),v(he.get(this))}:function(...e){return v(n.apply(_(this),e))}:function(e,...t){var r=n.call(_(this),e,...t);return de.set(r,e.sort?e.sort():[e]),v(r)}}function ue(e){var i,t;return"function"==typeof e?pe(e):(e instanceof IDBTransaction&&(i=e,f.has(i)||(t=new Promise((e,t)=>{let r=()=>{i.removeEventListener("complete",n),i.removeEventListener("error",a),i.removeEventListener("abort",a)},n=()=>{e(),r()},a=()=>{t(i.error||new DOMException("AbortError","AbortError")),r()};i.addEventListener("complete",n),i.addEventListener("error",a),i.addEventListener("abort",a)}),f.set(i,t))),oe(e,ce=ce||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,m):e)}function v(e){var i,t;return e instanceof IDBRequest?(i=e,(t=new Promise((e,t)=>{let r=()=>{i.removeEventListener("success",n),i.removeEventListener("error",a)},n=()=>{e(v(i.result)),r()},a=()=>{t(i.error),r()};i.addEventListener("success",n),i.addEventListener("error",a)})).then(e=>{e instanceof IDBCursor&&he.set(e,i)}).catch(()=>{}),b.set(t,i),t):g.has(e)?g.get(e):((t=ue(e))!==e&&(g.set(e,t),b.set(t,e)),t)}let _=e=>b.get(e);let fe=["get","getKey","getAll","getAllKeys","count"],ge=["put","add","delete","clear"],y=new Map;function be(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(y.get(t))return y.get(t);let a=t.replace(/FromIndex$/,""),i=t!==a,s=ge.includes(a);var r;return a in(i?IDBIndex:IDBObjectStore).prototype&&(s||fe.includes(a))?(r=async function(e,...t){var r=this.transaction(e,s?"readwrite":"readonly");let n=r.store;return i&&(n=n.index(t.shift())),(await Promise.all([n[a](...t),s&&r.done]))[0]},y.set(t,r),r):void 0}}m={...u=m,get:(e,t,r)=>be(e,t)||u.get(e,t,r),has:(e,t)=>!!be(e,t)||u.has(e,t)};class me{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map(e=>{var t;return"VERSION"===e.getComponent()?.type?(t=e.getImmediate()).library+"/"+t.version:null}).filter(e=>e).join(" ")}}let E="@firebase/app",C="0.14.0",w=new se("@firebase/app");var e;let I="[DEFAULT]",ve={"@firebase/app":"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},D=new Map,S=new Map,A=new Map;function O(t,r){try{t.container.addComponent(r)}catch(e){w.debug(`Component ${r.name} failed to register with FirebaseApp `+t.name,e)}}function _e(e,t){e.container.addOrOverwriteComponent(t)}function N(e){var t,r,n=e.name;if(A.has(n))return w.debug(`There were multiple attempts to register component ${n}.`),!1;A.set(n,e);for(t of D.values())O(t,e);for(r of S.values())O(r,e);return!0}function ye(e,t){var r=e.container.getProvider("heartbeat").getImmediate({optional:!0});return r&&r.triggerHeartbeat(),e.container.getProvider(t)}function L(e){return void 0!==e.options}function Ee(e){return!L(e)&&("authIdToken"in e||"appCheckToken"in e||"releaseOnDeref"in e||"automaticDataCollectionEnabled"in e)}let B=new n("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class Ce{constructor(e,t,r){this._isDeleted=!1,this._options={...e},this._config={...t},this._name=t.name,this._automaticDataCollectionEnabled=t.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new o("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw B.create("app-deleted",{appName:this._name})}}function we(e,t){var r=j(e.split(".")[1]);null===r?console.error(`FirebaseServerApp ${t} is invalid: second part could not be parsed.`):void 0===JSON.parse(r).exp?console.error(`FirebaseServerApp ${t} is invalid: expiration claim could not be parsed`):1e3*JSON.parse(r).exp-(new Date).getTime()<=0&&console.error(`FirebaseServerApp ${t} is invalid: the token has expired.`)}class Ie extends Ce{constructor(e,t,r,n){var a=void 0===t.automaticDataCollectionEnabled||t.automaticDataCollectionEnabled,i={name:r,automaticDataCollectionEnabled:a};void 0!==e.apiKey?super(e,i,n):super(e.options,i,n),this._serverConfig={automaticDataCollectionEnabled:a,...t},this._serverConfig.authIdToken&&we(this._serverConfig.authIdToken,"authIdToken"),this._serverConfig.appCheckToken&&we(this._serverConfig.appCheckToken,"appCheckToken"),this._finalizationRegistry=null,"undefined"!=typeof FinalizationRegistry&&(this._finalizationRegistry=new FinalizationRegistry(()=>{this.automaticCleanup()})),this._refCount=0,this.incRefCount(this._serverConfig.releaseOnDeref),this._serverConfig.releaseOnDeref=void 0,t.releaseOnDeref=void 0,k(E,C,"serverapp")}toJSON(){}get refCount(){return this._refCount}incRefCount(e){this.isDeleted||(this._refCount++,void 0!==e&&null!==this._finalizationRegistry&&this._finalizationRegistry.register(e,this))}decRefCount(){return this.isDeleted?0:--this._refCount}automaticCleanup(){t(this)}get settings(){return this.checkDestroyed(),this._serverConfig}checkDestroyed(){if(this.isDeleted)throw B.create("server-app-deleted")}}let De="12.0.0";function T(e,t={}){let r=e;if("object"!=typeof t){let e=t;t={name:e}}var n={name:I,automaticDataCollectionEnabled:!0,...t};let a=n.name;if("string"!=typeof a||!a)throw B.create("bad-app-name",{appName:String(a)});if(!(r=r||l()))throw B.create("no-options");var i=D.get(a);if(i){if(h(r,i.options)&&h(n,i.config))return i;throw B.create("duplicate-app",{appName:a})}var s,o=new te(a);for(s of A.values())o.addComponent(s);i=new Ce(r,n,o);return D.set(a,i),i}async function t(e){let t=!1;var r=e.name;D.has(r)?(t=!0,D.delete(r)):S.has(r)&&e.decRefCount()<=0&&(S.delete(r),t=!0),t&&(await Promise.all(e.container.getProviders().map(e=>e.delete())),e.isDeleted=!0)}function k(e,t,r){let n=ve[e]??e;r&&(n+="-"+r);var a,i=n.match(/\s|\//),s=t.match(/\s|\//);i||s?(a=[`Unable to register library "${n}" with version "${t}":`],i&&a.push(`library name "${n}" contains illegal characters (whitespace or "/")`),i&&s&&a.push("and"),s&&a.push(`version name "${t}" contains illegal characters (whitespace or "/")`),w.warn(a.join(" "))):N(new o(n+"-version",()=>({library:n,version:t}),"VERSION"))}function Se(e,t){if(null!==e&&"function"!=typeof e)throw B.create("invalid-log-argument");var r,i=e,n=t;for(r of d){let a=null;n&&n.level&&(a=re[n.level]),r.userLogHandler=null===i?null:(e,t,...r)=>{var n=r.map(e=>{if(null==e)return null;if("string"==typeof e)return e;if("number"==typeof e||"boolean"==typeof e)return e.toString();if(e instanceof Error)return e.message;try{return JSON.stringify(e)}catch(e){return null}}).filter(e=>e).join(" ");t>=(a??e.logLevel)&&i({level:p[t].toLowerCase(),message:n,args:r,type:e.name})}}}function Ae(e){var t;t=e,d.forEach(e=>{e.setLogLevel(t)})}let Oe="firebase-heartbeat-database",Ne=1,R="firebase-heartbeat-store",Le=null;function Be(){return Le=Le||((e,t,{blocked:r,upgrade:n,blocking:a,terminated:i})=>{let s=indexedDB.open(e,t);var o=v(s);return n&&s.addEventListener("upgradeneeded",e=>{n(v(s.result),e.oldVersion,e.newVersion,v(s.transaction),e)}),r&&s.addEventListener("blocked",e=>r(e.oldVersion,e.newVersion,e)),o.then(e=>{i&&e.addEventListener("close",()=>i()),a&&e.addEventListener("versionchange",e=>a(e.oldVersion,e.newVersion,e))}).catch(()=>{}),o})(Oe,Ne,{upgrade:(e,t)=>{if(0===t)try{e.createObjectStore(R)}catch(e){console.warn(e)}}}).catch(e=>{throw B.create("idb-open",{originalErrorMessage:e.message})})}async function Te(e,t){try{var r=(await Be()).transaction(R,"readwrite");await r.objectStore(R).put(t,ke(e)),await r.done}catch(e){e instanceof s?w.warn(e.message):(r=B.create("idb-set",{originalErrorMessage:e?.message}),w.warn(r.message))}}function ke(e){return e.name+"!"+e.options.appId}class Re{constructor(e){this.container=e,this._heartbeatsCache=null;var t=this.container.getProvider("app").getImmediate();this._storage=new Fe(t),this._heartbeatsCachePromise=this._storage.read().then(e=>this._heartbeatsCache=e)}async triggerHeartbeat(){try{var e,r=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString();let t=Pe();if(null!=this._heartbeatsCache?.heartbeats||(this._heartbeatsCache=await this._heartbeatsCachePromise,null!=this._heartbeatsCache?.heartbeats))if(this._heartbeatsCache.lastSentHeartbeatDate!==t&&!this._heartbeatsCache.heartbeats.some(e=>e.date===t))return this._heartbeatsCache.heartbeats.push({date:t,agent:r}),30<this._heartbeatsCache.heartbeats.length&&(e=(e=>{if(0===e.length)return-1;let t=0,r=e[0].date;for(let n=1;n<e.length;n++)e[n].date<r&&(r=e[n].date,t=n);return t})(this._heartbeatsCache.heartbeats),this._heartbeatsCache.heartbeats.splice(e,1)),this._storage.overwrite(this._heartbeatsCache)}catch(e){w.warn(e)}}async getHeartbeatsHeader(){try{var e,t,r,n;return(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,null==this._heartbeatsCache?.heartbeats||0===this._heartbeatsCache.heartbeats.length)?"":(e=Pe(),{heartbeatsToSend:t,unsentEntries:r}=((e,t=1024)=>{let r=[],n=e.slice();for(let i of e){var a=r.find(e=>e.agent===i.agent);if(a){if(a.dates.push(i.date),Me(r)>t){a.dates.pop();break}}else if(r.push({agent:i.agent,dates:[i.date]}),Me(r)>t){r.pop();break}n=n.slice(1)}return{heartbeatsToSend:r,unsentEntries:n}})(this._heartbeatsCache.heartbeats),n=H(JSON.stringify({version:2,heartbeats:t})),this._heartbeatsCache.lastSentHeartbeatDate=e,0<r.length?(this._heartbeatsCache.heartbeats=r,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),n)}catch(e){return w.warn(e),""}}}function Pe(){return(new Date).toISOString().substring(0,10)}class Fe{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(()=>{try{return"object"==typeof indexedDB}catch(e){}})()&&new Promise((n,a)=>{try{let e=!0,t="validate-browser-context-for-indexeddb-analytics-module",r=self.indexedDB.open(t);r.onsuccess=()=>{r.result.close(),e||self.indexedDB.deleteDatabase(t),n(!0)},r.onupgradeneeded=()=>{e=!1},r.onerror=()=>{a(r.error?.message||"")}}catch(e){a(e)}}).then(()=>!0).catch(()=>!1)}async read(){var e;return await this._canUseIndexedDBPromise&&(e=await(async e=>{try{var t=(await Be()).transaction(R),r=await t.objectStore(R).get(ke(e));return await t.done,r}catch(e){e instanceof s?w.warn(e.message):(t=B.create("idb-get",{originalErrorMessage:e?.message}),w.warn(t.message))}})(this.app))?.heartbeats?e:{heartbeats:[]}}async overwrite(e){var t;if(await this._canUseIndexedDBPromise)return t=await this.read(),Te(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??t.lastSentHeartbeatDate,heartbeats:e.heartbeats})}async add(e){var t;if(await this._canUseIndexedDBPromise)return t=await this.read(),Te(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??t.lastSentHeartbeatDate,heartbeats:[...t.heartbeats,...e.heartbeats]})}}function Me(e){return H(JSON.stringify({version:2,heartbeats:e})).length}e="",N(new o("platform-logger",e=>new me(e),"PRIVATE")),N(new o("heartbeat",e=>new Re(e),"PRIVATE")),k(E,C,e),k(E,C,"esm2020"),k("fire-js","");var ze=Object.freeze({__proto__:null,SDK_VERSION:De,_DEFAULT_ENTRY_NAME:I,_addComponent:O,_addOrOverwriteComponent:_e,_apps:D,_clearComponents:function(){A.clear()},_components:A,_getProvider:ye,_isFirebaseApp:L,_isFirebaseServerApp:function(e){return null!=e&&void 0!==e.settings},_isFirebaseServerAppSettings:Ee,_registerComponent:N,_removeServiceInstance:function(e,t,r=I){ye(e,t).clearInstance(r)},_serverApps:S,deleteApp:t,getApp:function(e=I){var t=D.get(e);if(!t&&e===I&&l())return T();if(t)return t;throw B.create("no-app",{appName:e})},getApps:function(){return Array.from(D.values())},initializeApp:T,initializeServerApp:function(e,t={}){if(("undefined"!=typeof window||K())&&!K())throw B.create("invalid-server-app-environment");let r,n=t||{};if(e&&(L(e)?r=e.options:Ee(e)?n=e:r=e),void 0===n.automaticDataCollectionEnabled&&(n.automaticDataCollectionEnabled=!0),!(r=r||l()))throw B.create("no-options");var a={...n,...r};if(void 0!==a.releaseOnDeref&&delete a.releaseOnDeref,void 0!==n.releaseOnDeref&&"undefined"==typeof FinalizationRegistry)throw B.create("finalization-registry-not-supported",{});var a=""+[...JSON.stringify(a)].reduce((e,t)=>Math.imul(31,e)+t.charCodeAt(0)|0,0),i=S.get(a);if(i)i.incRefCount(n.releaseOnDeref);else{var s,o=new te(a);for(s of A.values())o.addComponent(s);i=new Ie(r,n,a,o),S.set(a,i)}return i},onLog:Se,registerVersion:k,setLogLevel:Ae,FirebaseError:s});class xe{constructor(e,t){this._delegate=e,this.firebase=t,O(e,new o("app-compat",()=>this,"PUBLIC")),this.container=e.container}get automaticDataCollectionEnabled(){return this._delegate.automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this._delegate.automaticDataCollectionEnabled=e}get name(){return this._delegate.name}get options(){return this._delegate.options}delete(){return new Promise(e=>{this._delegate.checkDestroyed(),e()}).then(()=>(this.firebase.INTERNAL.removeApp(this.name),t(this._delegate)))}_getService(e,t=I){this._delegate.checkDestroyed();var r=this._delegate.container.getProvider(e);return r.isInitialized()||"EXPLICIT"!==r.getComponent()?.instantiationMode||r.initialize(),r.getImmediate({identifier:t})}_removeServiceInstance(e,t=I){this._delegate.container.getProvider(e).clearInstance(t)}_addComponent(e){O(this._delegate,e)}_addOrOverwriteComponent(e){_e(this._delegate,e)}toJSON(){return{name:this.name,automaticDataCollectionEnabled:this.automaticDataCollectionEnabled,options:this.options}}}let He=new n("app-compat","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call Firebase App.initializeApp()","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance."});function je(a){let i={},s={__esModule:!0,initializeApp:function(e,t={}){var r=T(e,t);if(X(i,r.name))return i[r.name];var n=new a(r,s);return i[r.name]=n},app:o,registerVersion:k,setLogLevel:Ae,onLog:Se,apps:null,SDK_VERSION:De,INTERNAL:{registerComponent:function(t){let r=t.name,n=r.replace("-compat","");{var e;N(t)&&"PUBLIC"===t.type&&(e=(e=o())=>{if("function"!=typeof e[n])throw He.create("invalid-app-argument",{appName:r});return e[n]()},void 0!==t.serviceProps&&c(e,t.serviceProps),s[n]=e,a.prototype[n]=function(...e){return this._getService.bind(this,r).apply(this,t.multipleInstances?e:[])})}return"PUBLIC"===t.type?s[n]:null},removeApp:function(e){delete i[e]},useAsService:function(e,t){if("serverAuth"===t)return null;var r=t;return r},modularAPIs:ze}};function o(e){if(e=e||I,X(i,e))return i[e];throw He.create("no-app",{appName:e})}return s.default=s,Object.defineProperty(s,"apps",{get:function(){return Object.keys(i).map(e=>i[e])}}),o.App=a,s}var Ve=function e(){let t=je(xe);return t.INTERNAL={...t.INTERNAL,createFirebaseNamespace:e,extendNamespace:function(e){c(t,e)},createSubscribe:Z,ErrorFactory:n,deepExtend:c},t}(),$e=new se("@firebase/app-compat");try{var We,P=V();void 0!==P.firebase&&($e.warn(`
      Warning: Firebase is already defined in the global scope. Please make sure
      Firebase library is only loaded once.
    `),We=P.firebase.SDK_VERSION)&&0<=We.indexOf("LITE")&&$e.warn(`
        Warning: You are trying to load Firebase while using Firebase Performance standalone script.
        You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.
        `)}catch{}P=Ve;k("@firebase/app-compat","0.5.0",void 0);return P.registerVersion("firebase","12.0.0","app-compat-cdn"),P});
//# sourceMappingURL=firebase-app-compat.js.map
