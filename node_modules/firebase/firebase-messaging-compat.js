((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(ra,oa){try{!(function(){function x(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n,B=x(ra);function L(){try{return"object"==typeof indexedDB}catch(e){return!1}}class o extends Error{constructor(e,t,a){super(t),this.code=e,this.customData=a,this.name="FirebaseError",Object.setPrototypeOf(this,o.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,i.prototype.create)}}class i{constructor(e,t,a){this.service=e,this.serviceName=t,this.errors=a}create(e,...t){var n,a=t[0]||{},i=this.service+"/"+e,r=this.errors[e],r=r?(n=a,r.replace(F,(e,t)=>{var a=n[t];return null!=a?String(a):`<${t}?>`})):"Error",r=this.serviceName+`: ${r} (${i}).`;return new o(i,r,a)}}let F=/\{\$([^}]+)}/g;function r(e){return e&&e._delegate?e._delegate:e}class e{constructor(e,t,a){this.name=e,this.instanceFactory=t,this.type=a,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let R=(t,e)=>e.some(e=>t instanceof e),H,q;let V=new WeakMap,s=new WeakMap,W=new WeakMap,a=new WeakMap,c=new WeakMap;let p={get(e,t,a){if(e instanceof IDBTransaction){if("done"===t)return s.get(e);if("objectStoreNames"===t)return e.objectStoreNames||W.get(e);if("store"===t)return a.objectStoreNames[1]?void 0:a.objectStore(a.objectStoreNames[0])}return d(e[t])},set(e,t,a){return e[t]=a,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function $(n){return n!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(q=q||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(n)?function(...e){return n.apply(u(this),e),d(V.get(this))}:function(...e){return d(n.apply(u(this),e))}:function(e,...t){var a=n.call(u(this),e,...t);return W.set(a,e.sort?e.sort():[e]),d(a)}}function U(e){var r,t;return"function"==typeof e?$(e):(e instanceof IDBTransaction&&(r=e,s.has(r)||(t=new Promise((e,t)=>{let a=()=>{r.removeEventListener("complete",n),r.removeEventListener("error",i),r.removeEventListener("abort",i)},n=()=>{e(),a()},i=()=>{t(r.error||new DOMException("AbortError","AbortError")),a()};r.addEventListener("complete",n),r.addEventListener("error",i),r.addEventListener("abort",i)}),s.set(r,t))),R(e,H=H||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,p):e)}function d(e){var r,t;return e instanceof IDBRequest?(r=e,(t=new Promise((e,t)=>{let a=()=>{r.removeEventListener("success",n),r.removeEventListener("error",i)},n=()=>{e(d(r.result)),a()},i=()=>{t(r.error),a()};r.addEventListener("success",n),r.addEventListener("error",i)})).then(e=>{e instanceof IDBCursor&&V.set(e,r)}).catch(()=>{}),c.set(t,r),t):a.has(e)?a.get(e):((t=U(e))!==e&&(a.set(e,t),c.set(t,e)),t)}let u=e=>c.get(e);function t(e,t,{blocked:a,upgrade:n,blocking:i,terminated:r}={}){let o=indexedDB.open(e,t);var s=d(o);return n&&o.addEventListener("upgradeneeded",e=>{n(d(o.result),e.oldVersion,e.newVersion,d(o.transaction),e)}),a&&o.addEventListener("blocked",e=>a(e.oldVersion,e.newVersion,e)),s.then(e=>{r&&e.addEventListener("close",()=>r()),i&&e.addEventListener("versionchange",e=>i(e.oldVersion,e.newVersion,e))}).catch(()=>{}),s}function l(e,{blocked:t}={}){var a=indexedDB.deleteDatabase(e);return t&&a.addEventListener("blocked",e=>t(e.oldVersion,e)),d(a).then(()=>{})}let G=["get","getKey","getAll","getAllKeys","count"],J=["put","add","delete","clear"],f=new Map;function z(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(f.get(t))return f.get(t);let i=t.replace(/FromIndex$/,""),r=t!==i,o=J.includes(i);var a;return i in(r?IDBIndex:IDBObjectStore).prototype&&(o||G.includes(i))?(a=async function(e,...t){var a=this.transaction(e,o?"readwrite":"readonly");let n=a.store;return r&&(n=n.index(t.shift())),(await Promise.all([n[i](...t),o&&a.done]))[0]},f.set(t,a),a):void 0}}p={...n=p,get:(e,t,a)=>z(e,t)||n.get(e,t,a),has:(e,t)=>!!z(e,t)||n.has(e,t)};var Y="@firebase/installations",g="0.6.19";let Q=1e4,Z="w:"+g,X="FIS_v2",ee="https://firebaseinstallations.googleapis.com/v1",te=36e5;var w,h,m,v;let b=new i("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function ae(e){return e instanceof o&&e.code.includes("request-failed")}function ne({projectId:e}){return ee+`/projects/${e}/installations`}function ie(e){return{token:e.token,requestStatus:2,expiresIn:Number(e.expiresIn.replace("s","000")),creationTime:Date.now()}}async function re(e,t){var a=(await t.json()).error;return b.create("request-failed",{requestName:e,serverCode:a.code,serverMessage:a.message,serverStatus:a.status})}function oe({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function se(e,{refreshToken:t}){var a=oe(e);return a.append("Authorization",(e=t,X+" "+e)),a}async function ce(e){var t=await e();return 500<=t.status&&t.status<600?e():t}function pe(t){return new Promise(e=>{setTimeout(e,t)})}let de=/^[cdef][\w-]{21}$/,y="";function ue(){try{var e=new Uint8Array(17),t=((self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16,(e=>btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_"))(e).substr(0,22));return de.test(t)?t:y}catch{return y}}function k(e){return e.appName+"!"+e.appId}let le=new Map;function fe(e,t){var a=k(e),e=(ge(a,t),a),a=(()=>(!I&&"BroadcastChannel"in self&&((I=new BroadcastChannel("[Firebase] FID Change")).onmessage=e=>{ge(e.data.key,e.data.fid)}),I))();a&&a.postMessage({key:e,fid:t}),0===le.size&&I&&(I.close(),I=null)}function ge(e,t){var a=le.get(e);if(a)for(var n of a)n(t)}let I=null;let we="firebase-installations-database",he=1,S="firebase-installations-store",me=null;function T(){return me=me||t(we,he,{upgrade:(e,t)=>{0===t&&e.createObjectStore(S)}})}async function C(e,t){var a=k(e),n=(await T()).transaction(S,"readwrite"),i=n.objectStore(S),r=await i.get(a);return await i.put(t,a),await n.done,r&&r.fid===t.fid||fe(e,t.fid),t}async function ve(e){var t=k(e),a=(await T()).transaction(S,"readwrite");await a.objectStore(S).delete(t),await a.done}async function _(e,t){var a=k(e),n=(await T()).transaction(S,"readwrite"),i=n.objectStore(S),r=await i.get(a),o=t(r);return void 0===o?await i.delete(a):await i.put(o,a),await n.done,!o||r&&r.fid===o.fid||fe(e,o.fid),o}async function D(a){let n;var e=await _(a.appConfig,e=>{var t=ye(e||{fid:ue(),registrationStatus:0}),t=((e,t)=>{var a,n;return 0===t.registrationStatus?navigator.onLine?(a={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},n=(async(t,a)=>{try{var e=await(async({appConfig:e,heartbeatServiceProvider:t},{fid:a})=>{let n=ne(e);var i=oe(e),r=((r=t.getImmediate({optional:!0}))&&(r=await r.getHeartbeatsHeader())&&i.append("x-firebase-client",r),{fid:a,authVersion:X,appId:e.appId,sdkVersion:Z});let o={method:"POST",headers:i,body:JSON.stringify(r)};if((i=await ce(()=>fetch(n,o))).ok)return{fid:(r=await i.json()).fid||a,registrationStatus:2,refreshToken:r.refreshToken,authToken:ie(r.authToken)};throw await re("Create Installation",i)})(t,a);return C(t.appConfig,e)}catch(e){throw ae(e)&&409===e.customData.serverCode?await ve(t.appConfig):await C(t.appConfig,{fid:a.fid,registrationStatus:0}),e}})(e,a),{installationEntry:a,registrationPromise:n}):(a=Promise.reject(b.create("app-offline")),{installationEntry:t,registrationPromise:a}):1===t.registrationStatus?{installationEntry:t,registrationPromise:(async e=>{let t=await be(e.appConfig);for(;1===t.registrationStatus;)await pe(100),t=await be(e.appConfig);var a,n;return 0!==t.registrationStatus?t:({installationEntry:a,registrationPromise:n}=await D(e),n||a)})(e)}:{installationEntry:t}})(a,t);return n=t.registrationPromise,t.installationEntry});return e.fid===y?{installationEntry:await n}:{installationEntry:e,registrationPromise:n}}function be(e){return _(e,e=>{if(e)return ye(e);throw b.create("installation-not-found")})}function ye(e){var t;return 1===(t=e).registrationStatus&&t.registrationTime+Q<Date.now()?{fid:e.fid,registrationStatus:0}:e}async function ke({appConfig:e,heartbeatServiceProvider:t},a){[i,r]=[e,a.fid];let n=ne(i)+`/${r}/authTokens:generate`;var i,r,o=se(e,a),s=t.getImmediate({optional:!0}),s=(s&&(s=await s.getHeartbeatsHeader())&&o.append("x-firebase-client",s),{installation:{sdkVersion:Z,appId:e.appId}});let c={method:"POST",headers:o,body:JSON.stringify(s)};o=await ce(()=>fetch(n,c));if(o.ok)return ie(await o.json());throw await re("Generate Auth Token",o)}async function Ie(n,i=!1){let r;var e=await _(n.appConfig,e=>{if(!Te(e))throw b.create("not-registered");var t,a=e.authToken;if(i||2!==(t=a).requestStatus||(e=>{var t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+te})(t)){if(1===a.requestStatus)return r=(async(e,t)=>{let a=await Se(e.appConfig);for(;1===a.authToken.requestStatus;)await pe(100),a=await Se(e.appConfig);var n=a.authToken;return 0===n.requestStatus?Ie(e,t):n})(n,i),e;if(navigator.onLine)return t=e,a={requestStatus:1,requestTime:Date.now()},a={...t,authToken:a},r=(async(t,a)=>{try{var e=await ke(t,a),n={...a,authToken:e};return await C(t.appConfig,n),e}catch(e){var i;throw!ae(e)||401!==e.customData.serverCode&&404!==e.customData.serverCode?(i={...a,authToken:{requestStatus:0}},await C(t.appConfig,i)):await ve(t.appConfig),e}})(n,a),a;throw b.create("app-offline")}return e});return r?await r:e.authToken}function Se(e){return _(e,e=>{var t,a;if(Te(e))return t=e.authToken,1===(a=t).requestStatus&&a.requestTime+Q<Date.now()?{...e,authToken:{requestStatus:0}}:e;throw b.create("not-registered")})}function Te(e){return void 0!==e&&2===e.registrationStatus}async function Ce(e,t=!1){var a=e,n=(await(!(n=(await D(a)).registrationPromise)||!await n),await Ie(a,t));return n.token}function _e(e){return b.create("missing-app-config-values",{valueName:e})}let De="installations",Pe=e=>{var t=e.getProvider("app").getImmediate();return{app:t,appConfig:(e=>{if(!e||!e.options)throw _e("App Configuration");if(!e.name)throw _e("App Name");var t;for(t of["projectId","apiKey","appId"])if(!e.options[t])throw _e(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}})(t),heartbeatServiceProvider:oa._getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},Ee=e=>{var t=e.getProvider("app").getImmediate();let a=oa._getProvider(t,De).getImmediate();return{getId:()=>(async e=>{var t=e,{installationEntry:a,registrationPromise:n}=await D(t);return(n||Ie(t)).catch(console.error),a.fid})(a),getToken:e=>Ce(a,e)}};oa._registerComponent(new e(De,Pe,"PUBLIC")),oa._registerComponent(new e("installations-internal",Ee,"PRIVATE")),oa.registerVersion(Y,g),oa.registerVersion(Y,g,"esm2020");let Me="/firebase-messaging-sw.js",je="/firebase-cloud-messaging-push-scope",Ae="BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",Oe="https://fcmregistrations.googleapis.com/v1",Ke="google.c.a.c_id",Ne="google.c.a.c_l",xe="google.c.a.ts",Be="google.c.a.e",Le=1e4;function P(e){var t=new Uint8Array(e);return btoa(String.fromCharCode(...t)).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(v=w=w||{}).PUSH_RECEIVED="push-received",v.NOTIFICATION_CLICKED="notification-clicked";let Fe="fcm_token_details_db",Re=5,He="fcm_token_object_Store";async function qe(o){if("databases"in indexedDB&&!(await indexedDB.databases()).map(e=>e.name).includes(Fe))return null;let s=null;return(await t(Fe,Re,{upgrade:async(e,t,a,n)=>{var i,r;t<2||e.objectStoreNames.contains(He)&&(i=await(r=n.objectStore(He)).index("fcmSenderId").get(o),await r.clear(),i)&&(2===t?(r=i).auth&&r.p256dh&&r.endpoint&&(s={token:r.fcmToken,createTime:r.createTime??Date.now(),subscriptionOptions:{auth:r.auth,p256dh:r.p256dh,endpoint:r.endpoint,swScope:r.swScope,vapidKey:"string"==typeof r.vapidKey?r.vapidKey:P(r.vapidKey)}}):3===t?(r=i,s={token:r.fcmToken,createTime:r.createTime,subscriptionOptions:{auth:P(r.auth),p256dh:P(r.p256dh),endpoint:r.endpoint,swScope:r.swScope,vapidKey:P(r.vapidKey)}}):4===t&&(r=i,s={token:r.fcmToken,createTime:r.createTime,subscriptionOptions:{auth:P(r.auth),p256dh:P(r.p256dh),endpoint:r.endpoint,swScope:r.swScope,vapidKey:P(r.vapidKey)}}))}})).close(),await l(Fe),await l("fcm_vapid_details_db"),await l("undefined"),(e=>{var t;if(e&&e.subscriptionOptions)return t=e.subscriptionOptions,"number"==typeof e.createTime&&0<e.createTime&&"string"==typeof e.token&&0<e.token.length&&"string"==typeof t.auth&&0<t.auth.length&&"string"==typeof t.p256dh&&0<t.p256dh.length&&"string"==typeof t.endpoint&&0<t.endpoint.length&&"string"==typeof t.swScope&&0<t.swScope.length&&"string"==typeof t.vapidKey&&0<t.vapidKey.length})(s)?s:null}let Ve="firebase-messaging-database",We=1,E="firebase-messaging-store",$e=null;function Ue(){return $e=$e||t(Ve,We,{upgrade:(e,t)=>{0===t&&e.createObjectStore(E)}})}async function Ge(e){var t=ze(e),t=await(await Ue()).transaction(E).objectStore(E).get(t);return t||((t=await qe(e.appConfig.senderId))?(await Je(e,t),t):void 0)}async function Je(e,t){var a=ze(e),n=(await Ue()).transaction(E,"readwrite");return await n.objectStore(E).put(t,a),await n.done,t}function ze({appConfig:e}){return e.appId}let M=new i("messaging","Messaging",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"only-available-in-window":"This method is available in a Window context.","only-available-in-sw":"This method is available in a service worker context.","permission-default":"The notification permission was not granted and dismissed instead.","permission-blocked":"The notification permission was not granted and blocked instead.","unsupported-browser":"This browser doesn't support the API's required to use the Firebase SDK.","indexed-db-unsupported":"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)","failed-service-worker-registration":"We are unable to register the default service worker. {$browserErrorMessage}","token-subscribe-failed":"A problem occurred while subscribing the user to FCM: {$errorInfo}","token-subscribe-no-token":"FCM returned no token when subscribing the user to push.","token-unsubscribe-failed":"A problem occurred while unsubscribing the user from FCM: {$errorInfo}","token-update-failed":"A problem occurred while updating the user from FCM: {$errorInfo}","token-update-no-token":"FCM returned no token when updating the user to push.","use-sw-after-get-token":"The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.","invalid-sw-registration":"The input to useServiceWorker() must be a ServiceWorkerRegistration.","invalid-bg-handler":"The input to setBackgroundMessageHandler() must be a function.","invalid-vapid-key":"The public VAPID key must be a string.","use-vapid-key-after-get-token":"The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used."});async function Ye(e,t){var a={method:"DELETE",headers:await Ze(e)};try{var n,i=await(await fetch(Qe(e.appConfig)+"/"+t,a)).json();if(i.error)throw n=i.error.message,M.create("token-unsubscribe-failed",{errorInfo:n})}catch(e){throw M.create("token-unsubscribe-failed",{errorInfo:e?.toString()})}}function Qe({projectId:e}){return Oe+`/projects/${e}/registrations`}async function Ze({appConfig:e,installations:t}){var a=await t.getToken();return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e.apiKey,"x-goog-firebase-installations-auth":"FIS "+a})}function Xe({p256dh:e,auth:t,endpoint:a,vapidKey:n}){var i={web:{endpoint:a,auth:t,p256dh:e}};return n!==Ae&&(i.web.applicationPubKey=n),i}let et=6048e5;async function tt(e){var t,a,n,i,r,o=await(async(e,t)=>{var a=await e.pushManager.getSubscription();return a||e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:(e=>{var t=(e+"=".repeat((4-e.length%4)%4)).replace(/\-/g,"+").replace(/_/g,"/"),a=atob(t),n=new Uint8Array(a.length);for(let i=0;i<a.length;++i)n[i]=a.charCodeAt(i);return n})(t)})})(e.swRegistration,e.vapidKey),o={vapidKey:e.vapidKey,swScope:e.swRegistration.scope,endpoint:o.endpoint,auth:P(o.getKey("auth")),p256dh:P(o.getKey("p256dh"))},s=await Ge(e.firebaseDependencies);if(s){if(t=s.subscriptionOptions,a=o.vapidKey===t.vapidKey,n=o.endpoint===t.endpoint,i=o.auth===t.auth,r=o.p256dh===t.p256dh,a&&n&&i&&r)return Date.now()>=s.createTime+et?(async(e,t)=>{try{var a=await(async(e,t)=>{var a=await Ze(e),n=Xe(t.subscriptionOptions),a={method:"PATCH",headers:a,body:JSON.stringify(n)};let i;try{var r=await fetch(Qe(e.appConfig)+"/"+t.token,a);i=await r.json()}catch(e){throw M.create("token-update-failed",{errorInfo:e?.toString()})}if(i.error)throw n=i.error.message,M.create("token-update-failed",{errorInfo:n});if(i.token)return i.token;throw M.create("token-update-no-token")})(e.firebaseDependencies,t),n={...t,token:a,createTime:Date.now()};return await Je(e.firebaseDependencies,n),a}catch(e){throw e}})(e,{token:s.token,createTime:Date.now(),subscriptionOptions:o}):s.token;try{await Ye(e.firebaseDependencies,s.token)}catch(e){console.warn(e)}}return nt(e.firebaseDependencies,o)}async function at(e){var t,a=await Ge(e.firebaseDependencies),a=(a&&(await Ye(e.firebaseDependencies,a.token),a=ze(e.firebaseDependencies),await(t=(await Ue()).transaction(E,"readwrite")).objectStore(E).delete(a),await t.done),await e.swRegistration.pushManager.getSubscription());return!a||a.unsubscribe()}async function nt(e,t){var a={token:await(async(e,t)=>{var a=await Ze(e),n=Xe(t),a={method:"POST",headers:a,body:JSON.stringify(n)};let i;try{var r=await fetch(Qe(e.appConfig),a);i=await r.json()}catch(e){throw M.create("token-subscribe-failed",{errorInfo:e?.toString()})}if(i.error)throw n=i.error.message,M.create("token-subscribe-failed",{errorInfo:n});if(i.token)return i.token;throw M.create("token-subscribe-no-token")})(e,t),createTime:Date.now(),subscriptionOptions:t};return await Je(e,a),a.token}function it(e){var t,a,n,i={from:e.from,collapseKey:e.collapse_key,messageId:e.fcmMessageId};return a=i,(t=e).notification&&(a.notification={},(n=t.notification.title)&&(a.notification.title=n),(n=t.notification.body)&&(a.notification.body=n),(n=t.notification.image)&&(a.notification.image=n),n=t.notification.icon)&&(a.notification.icon=n),t=i,(a=e).data&&(t.data=a.data),t=i,((a=e).fcmOptions||a.notification?.click_action)&&(t.fcmOptions={},(n=a.fcmOptions?.link??a.notification?.click_action)&&(t.fcmOptions.link=n),n=a.fcmOptions?.analytics_label)&&(t.fcmOptions.analyticsLabel=n),i}var rt="AzSCbw63g1R0nCw85jG8",ot="Iaya3yLKwmgvh7cF0q4",st=[];for(let K=0;K<rt.length;K++)st.push(rt.charAt(K)),K<ot.length&&st.push(ot.charAt(K));function ct(e){return M.create("missing-app-config-values",{valueName:e})}st.join("");class pt{constructor(e,t,a){this.deliveryMetricsExportedToBigQueryEnabled=!1,this.onBackgroundMessageHandler=null,this.onMessageHandler=null,this.logEvents=[],this.isLogServiceStarted=!1;var n=(e=>{if(!e||!e.options)throw ct("App Configuration Object");if(!e.name)throw ct("App Name");var t,a=e.options;for(t of["projectId","apiKey","appId","messagingSenderId"])if(!a[t])throw ct(t);return{appName:e.name,projectId:a.projectId,apiKey:a.apiKey,appId:a.appId,senderId:a.messagingSenderId}})(e);this.firebaseDependencies={app:e,appConfig:n,installations:t,analyticsProvider:a}}_delete(){return Promise.resolve()}}async function dt(e){try{e.swRegistration=await navigator.serviceWorker.register(Me,{scope:je}),e.swRegistration.update().catch(()=>{}),i=e.swRegistration,await new Promise((t,e)=>{let a=setTimeout(()=>e(new Error(`Service worker not registered after ${Le} ms`)),Le),n=i.installing||i.waiting;i.active?(clearTimeout(a),t()):n?n.onstatechange=e=>{"activated"===e.target?.state&&(n.onstatechange=null,clearTimeout(a),t())}:(clearTimeout(a),e(new Error("No incoming service worker found.")))})}catch(e){throw M.create("failed-service-worker-registration",{browserErrorMessage:e?.message})}var i}async function ut(e,t){if(!navigator)throw M.create("only-available-in-window");if("default"===Notification.permission&&await Notification.requestPermission(),"granted"!==Notification.permission)throw M.create("permission-blocked");n=e,await!((a=t?.vapidKey)?n.vapidKey=a:n.vapidKey||(n.vapidKey=Ae));var a=e,n=t?.serviceWorkerRegistration;if(n||a.swRegistration||await dt(a),n||!a.swRegistration){if(!(n instanceof ServiceWorkerRegistration))throw M.create("invalid-sw-registration");a.swRegistration=n}return await 0,tt(e)}async function lt(e,t,a){var n=(e=>{switch(e){case w.NOTIFICATION_CLICKED:return"notification_open";case w.PUSH_RECEIVED:return"notification_foreground";default:throw new Error}})(t);(await e.firebaseDependencies.analyticsProvider.get()).logEvent(n,{message_id:a[Ke],message_name:a[Ne],message_time:a[xe],message_device_time:Math.floor(Date.now()/1e3)})}async function ft(e,t){var a,n=t.data;n.isFirebaseMessaging&&(e.onMessageHandler&&n.messageType===w.PUSH_RECEIVED&&("function"==typeof e.onMessageHandler?e.onMessageHandler(it(n)):e.onMessageHandler.next(it(n))),"object"==typeof(t=a=n.data))&&t&&Ke in t&&"1"===a[Be]&&await lt(e,n.messageType,a)}let gt="@firebase/messaging",wt="0.12.23",ht=e=>{let t=new pt(e.getProvider("app").getImmediate(),e.getProvider("installations-internal").getImmediate(),e.getProvider("analytics-internal"));return navigator.serviceWorker.addEventListener("message",e=>ft(t,e)),t},mt=e=>{let t=e.getProvider("messaging").getImmediate();return{getToken:e=>ut(t,e)}};function vt(e){return(async e=>{if(navigator)return e.swRegistration||await dt(e),at(e);throw M.create("only-available-in-window")})(e=r(e))}function bt(e,t){var a=e=r(e),e=t;if(navigator)return a.onMessageHandler=e,()=>{a.onMessageHandler=null};throw M.create("only-available-in-window")}oa._registerComponent(new e("messaging",ht,"PUBLIC")),oa._registerComponent(new e("messaging-internal",mt,"PRIVATE")),oa.registerVersion(gt,wt),oa.registerVersion(gt,wt,"esm2020");let yt="BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",kt="https://fcmregistrations.googleapis.com/v1",It="FCM_MSG",St="google.c.a.c_id",Tt=3,Ct=1;function j(e){var t=new Uint8Array(e);return btoa(String.fromCharCode(...t)).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(v=h=h||{})[v.DATA_MESSAGE=1]="DATA_MESSAGE",v[v.DISPLAY_NOTIFICATION=3]="DISPLAY_NOTIFICATION",(v=m=m||{}).PUSH_RECEIVED="push-received",v.NOTIFICATION_CLICKED="notification-clicked";let _t="fcm_token_details_db",Dt=5,Pt="fcm_token_object_Store";async function Et(o){if("databases"in indexedDB&&!(await indexedDB.databases()).map(e=>e.name).includes(_t))return null;let s=null;return(await t(_t,Dt,{upgrade:async(e,t,a,n)=>{var i,r;t<2||e.objectStoreNames.contains(Pt)&&(i=await(r=n.objectStore(Pt)).index("fcmSenderId").get(o),await r.clear(),i)&&(2===t?(r=i).auth&&r.p256dh&&r.endpoint&&(s={token:r.fcmToken,createTime:r.createTime??Date.now(),subscriptionOptions:{auth:r.auth,p256dh:r.p256dh,endpoint:r.endpoint,swScope:r.swScope,vapidKey:"string"==typeof r.vapidKey?r.vapidKey:j(r.vapidKey)}}):3===t?(r=i,s={token:r.fcmToken,createTime:r.createTime,subscriptionOptions:{auth:j(r.auth),p256dh:j(r.p256dh),endpoint:r.endpoint,swScope:r.swScope,vapidKey:j(r.vapidKey)}}):4===t&&(r=i,s={token:r.fcmToken,createTime:r.createTime,subscriptionOptions:{auth:j(r.auth),p256dh:j(r.p256dh),endpoint:r.endpoint,swScope:r.swScope,vapidKey:j(r.vapidKey)}}))}})).close(),await l(_t),await l("fcm_vapid_details_db"),await l("undefined"),(e=>{var t;if(e&&e.subscriptionOptions)return t=e.subscriptionOptions,"number"==typeof e.createTime&&0<e.createTime&&"string"==typeof e.token&&0<e.token.length&&"string"==typeof t.auth&&0<t.auth.length&&"string"==typeof t.p256dh&&0<t.p256dh.length&&"string"==typeof t.endpoint&&0<t.endpoint.length&&"string"==typeof t.swScope&&0<t.swScope.length&&"string"==typeof t.vapidKey&&0<t.vapidKey.length})(s)?s:null}let Mt="firebase-messaging-database",jt=1,A="firebase-messaging-store",At=null;function Ot(){return At=At||t(Mt,jt,{upgrade:(e,t)=>{0===t&&e.createObjectStore(A)}})}async function Kt(e){var t=xt(e),t=await(await Ot()).transaction(A).objectStore(A).get(t);return t||((t=await Et(e.appConfig.senderId))?(await Nt(e,t),t):void 0)}async function Nt(e,t){var a=xt(e),n=(await Ot()).transaction(A,"readwrite");return await n.objectStore(A).put(t,a),await n.done,t}function xt({appConfig:e}){return e.appId}let O=new i("messaging","Messaging",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"only-available-in-window":"This method is available in a Window context.","only-available-in-sw":"This method is available in a service worker context.","permission-default":"The notification permission was not granted and dismissed instead.","permission-blocked":"The notification permission was not granted and blocked instead.","unsupported-browser":"This browser doesn't support the API's required to use the Firebase SDK.","indexed-db-unsupported":"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)","failed-service-worker-registration":"We are unable to register the default service worker. {$browserErrorMessage}","token-subscribe-failed":"A problem occurred while subscribing the user to FCM: {$errorInfo}","token-subscribe-no-token":"FCM returned no token when subscribing the user to push.","token-unsubscribe-failed":"A problem occurred while unsubscribing the user from FCM: {$errorInfo}","token-update-failed":"A problem occurred while updating the user from FCM: {$errorInfo}","token-update-no-token":"FCM returned no token when updating the user to push.","use-sw-after-get-token":"The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.","invalid-sw-registration":"The input to useServiceWorker() must be a ServiceWorkerRegistration.","invalid-bg-handler":"The input to setBackgroundMessageHandler() must be a function.","invalid-vapid-key":"The public VAPID key must be a string.","use-vapid-key-after-get-token":"The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used."});async function Bt(e,t){var a={method:"DELETE",headers:await Ft(e)};try{var n,i=await(await fetch(Lt(e.appConfig)+"/"+t,a)).json();if(i.error)throw n=i.error.message,O.create("token-unsubscribe-failed",{errorInfo:n})}catch(e){throw O.create("token-unsubscribe-failed",{errorInfo:e?.toString()})}}function Lt({projectId:e}){return kt+`/projects/${e}/registrations`}async function Ft({appConfig:e,installations:t}){var a=await t.getToken();return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e.apiKey,"x-goog-firebase-installations-auth":"FIS "+a})}function Rt({p256dh:e,auth:t,endpoint:a,vapidKey:n}){var i={web:{endpoint:a,auth:t,p256dh:e}};return n!==yt&&(i.web.applicationPubKey=n),i}let Ht=6048e5;async function qt(e){var t,a,n,i,r,o=await(async(e,t)=>{var a=await e.pushManager.getSubscription();return a||e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:(e=>{var t=(e+"=".repeat((4-e.length%4)%4)).replace(/\-/g,"+").replace(/_/g,"/"),a=atob(t),n=new Uint8Array(a.length);for(let i=0;i<a.length;++i)n[i]=a.charCodeAt(i);return n})(t)})})(e.swRegistration,e.vapidKey),o={vapidKey:e.vapidKey,swScope:e.swRegistration.scope,endpoint:o.endpoint,auth:j(o.getKey("auth")),p256dh:j(o.getKey("p256dh"))},s=await Kt(e.firebaseDependencies);if(s){if(t=s.subscriptionOptions,a=o.vapidKey===t.vapidKey,n=o.endpoint===t.endpoint,i=o.auth===t.auth,r=o.p256dh===t.p256dh,a&&n&&i&&r)return Date.now()>=s.createTime+Ht?(async(e,t)=>{try{var a=await(async(e,t)=>{var a=await Ft(e),n=Rt(t.subscriptionOptions),a={method:"PATCH",headers:a,body:JSON.stringify(n)};let i;try{var r=await fetch(Lt(e.appConfig)+"/"+t.token,a);i=await r.json()}catch(e){throw O.create("token-update-failed",{errorInfo:e?.toString()})}if(i.error)throw n=i.error.message,O.create("token-update-failed",{errorInfo:n});if(i.token)return i.token;throw O.create("token-update-no-token")})(e.firebaseDependencies,t),n={...t,token:a,createTime:Date.now()};return await Nt(e.firebaseDependencies,n),a}catch(e){throw e}})(e,{token:s.token,createTime:Date.now(),subscriptionOptions:o}):s.token;try{await Bt(e.firebaseDependencies,s.token)}catch(e){console.warn(e)}}return Wt(e.firebaseDependencies,o)}async function Vt(e){var t,a=await Kt(e.firebaseDependencies),a=(a&&(await Bt(e.firebaseDependencies,a.token),a=xt(e.firebaseDependencies),await(t=(await Ot()).transaction(A,"readwrite")).objectStore(A).delete(a),await t.done),await e.swRegistration.pushManager.getSubscription());return!a||a.unsubscribe()}async function Wt(e,t){var a={token:await(async(e,t)=>{var a=await Ft(e),n=Rt(t),a={method:"POST",headers:a,body:JSON.stringify(n)};let i;try{var r=await fetch(Lt(e.appConfig),a);i=await r.json()}catch(e){throw O.create("token-subscribe-failed",{errorInfo:e?.toString()})}if(i.error)throw n=i.error.message,O.create("token-subscribe-failed",{errorInfo:n});if(i.token)return i.token;throw O.create("token-subscribe-no-token")})(e,t),createTime:Date.now(),subscriptionOptions:t};return await Nt(e,a),a.token}var $t="AzSCbw63g1R0nCw85jG8",Ut="Iaya3yLKwmgvh7cF0q4",Gt=[];for(let N=0;N<$t.length;N++)Gt.push($t.charAt(N)),N<Ut.length&&Gt.push(Ut.charAt(N));async function Jt(e,t){var a=((e,t)=>{var a={};return e.from&&(a.project_number=e.from),e.fcmMessageId&&(a.message_id=e.fcmMessageId),a.instance_id=t,a.message_type=(e.notification?h.DISPLAY_NOTIFICATION:h.DATA_MESSAGE).toString(),a.sdk_platform=Tt.toString(),a.package_name=self.origin.replace(/(^\w+:|^)\/\//,""),e.collapse_key&&(a.collapse_key=e.collapse_key),a.event=Ct.toString(),e.fcmOptions?.analytics_label&&(a.analytics_label=e.fcmOptions?.analytics_label),a})(t,await e.firebaseDependencies.installations.getId()),n=a,t=t.productId,a={};a.event_time_ms=Math.floor(Date.now()).toString(),a.source_extension_json_proto3=JSON.stringify({messaging_client_event:n}),t&&(a.compliance_data=(e=>({privacy_context:{prequest:{origin_associated_product_id:e}}}))(t)),e.logEvents.push(a)}async function zt(e,t){var a=(({data:e})=>{if(!e)return null;try{return e.json()}catch(e){return null}})(e);if(a){t.deliveryMetricsExportedToBigQueryEnabled&&await Jt(t,a);var n,i,r,o=await Qt();if(o.some(e=>"visible"===e.visibilityState&&!e.url.startsWith("chrome-extension://"))){var s,e=o,c=a;c.isFirebaseMessaging=!0,c.messageType=m.PUSH_RECEIVED;for(s of e)s.postMessage(c)}else a.notification&&await(e=>{var t=e.actions,a=Notification.maxActions;return t&&a&&t.length>a&&console.warn(`This browser only supports ${a} actions. The remaining actions will not be displayed.`),self.registration.showNotification(e.title??"",e)})(((o={...(e=a).notification}).data={[It]:e},o)),t&&t.onBackgroundMessageHandler&&(o={from:(e=a).from,collapseKey:e.collapse_key,messageId:e.fcmMessageId},i=o,(n=e).notification&&(i.notification={},(r=n.notification.title)&&(i.notification.title=r),(r=n.notification.body)&&(i.notification.body=r),(r=n.notification.image)&&(i.notification.image=r),r=n.notification.icon)&&(i.notification.icon=r),n=o,(i=e).data&&(n.data=i.data),n=o,((i=e).fcmOptions||i.notification?.click_action)&&(n.fcmOptions={},(r=i.fcmOptions?.link??i.notification?.click_action)&&(n.fcmOptions.link=r),r=i.fcmOptions?.analytics_label)&&(n.fcmOptions.analyticsLabel=r),a=o,"function"==typeof t.onBackgroundMessageHandler?await t.onBackgroundMessageHandler(a):t.onBackgroundMessageHandler.next(a))}}async function Yt(e){var t=e.notification?.data?.[It];if(t&&!e.action){e.stopImmediatePropagation(),e.notification.close();var a=(e=>{var t=e.fcmOptions?.link??e.notification?.click_action;return t||((e=>"object"==typeof e&&e&&St in e)(e.data)?self.location.origin:null)})(t);if(a){var n,i=new URL(a,self.location.href),r=new URL(self.location.origin);if(i.host===r.host){let e=await(async e=>{var t;for(t of await Qt()){var a=new URL(t.url,self.location.href);if(e.host===a.host)return t}return null})(i);if(e?e=await e.focus():(e=await self.clients.openWindow(a),n=3e3,await new Promise(e=>{setTimeout(e,n)})),e)return t.messageType=m.NOTIFICATION_CLICKED,t.isFirebaseMessaging=!0,e.postMessage(t)}}}}function Qt(){return self.clients.matchAll({type:"window",includeUncontrolled:!0})}function Zt(e){return O.create("missing-app-config-values",{valueName:e})}Gt.join("");class Xt{constructor(e,t,a){this.deliveryMetricsExportedToBigQueryEnabled=!1,this.onBackgroundMessageHandler=null,this.onMessageHandler=null,this.logEvents=[],this.isLogServiceStarted=!1;var n=(e=>{if(!e||!e.options)throw Zt("App Configuration Object");if(!e.name)throw Zt("App Name");var t,a=e.options;for(t of["projectId","apiKey","appId","messagingSenderId"])if(!a[t])throw Zt(t);return{appName:e.name,projectId:a.projectId,apiKey:a.apiKey,appId:a.appId,senderId:a.messagingSenderId}})(e);this.firebaseDependencies={app:e,appConfig:n,installations:t,analyticsProvider:a}}_delete(){return Promise.resolve()}}let ea=e=>{let t=new Xt(e.getProvider("app").getImmediate(),e.getProvider("installations-internal").getImmediate(),e.getProvider("analytics-internal"));return self.addEventListener("push",e=>{e.waitUntil(zt(e,t))}),self.addEventListener("pushsubscriptionchange",e=>{e.waitUntil((async(e,t)=>{var a;(a=e.newSubscription)?(a=await Kt(t.firebaseDependencies),await Vt(t),t.vapidKey=a?.subscriptionOptions?.vapidKey??yt,await qt(t)):await Vt(t)})(e,t))}),self.addEventListener("notificationclick",e=>{e.waitUntil(Yt(e))}),t};function ta(e,t){var a=e=r(e),e=t;if(void 0!==self.document)throw O.create("only-available-in-sw");return a.onBackgroundMessageHandler=e,()=>{a.onBackgroundMessageHandler=null}}oa._registerComponent(new e("messaging-sw",ea,"PUBLIC"));class aa{constructor(e,t){this.app=e,this._delegate=t,this.app=e,this._delegate=t}async getToken(e){return(async(e,t)=>ut(e=r(e),t))(this._delegate,e)}async deleteToken(){return vt(this._delegate)}onMessage(e){return bt(this._delegate,e)}onBackgroundMessage(e){return ta(this._delegate,e)}}let na=e=>self&&"ServiceWorkerGlobalScope"in self?new aa(e.getProvider("app-compat").getImmediate(),e.getProvider("messaging-sw").getImmediate()):new aa(e.getProvider("app-compat").getImmediate(),e.getProvider("messaging").getImmediate()),ia={isSupported:function(){return self&&"ServiceWorkerGlobalScope"in self?L()&&"PushManager"in self&&"Notification"in self&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey"):"undefined"!=typeof window&&L()&&!("undefined"==typeof navigator||!navigator.cookieEnabled)&&"serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window&&"fetch"in window&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey")}};B.default.INTERNAL.registerComponent(new e("messaging-compat",na,"PUBLIC").setServiceProps(ia)),B.default.registerVersion("@firebase/messaging-compat","0.2.23")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-messaging-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-messaging-compat.js.map
