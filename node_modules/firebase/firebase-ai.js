import{_isFirebaseServerApp as e,_get<PERSON>rovider,getApp as t,_registerComponent as n,registerVersion as s}from"https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";class FirebaseError extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){const n=t[0]||{},s=`${this.service}/${e}`,o=this.errors[e],a=o?function replaceTemplate(e,t){return e.replace(r,((e,n)=>{const s=t[n];return null!=s?String(s):`<${n}?>`}))}(o,n):"Error",i=`${this.serviceName}: ${a} (${s}).`;return new FirebaseError(s,i,n)}}const r=/\{\$([^}]+)}/g;class Component{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var o;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(o||(o={}));const a={debug:o.DEBUG,verbose:o.VERBOSE,info:o.INFO,warn:o.WARN,error:o.ERROR,silent:o.SILENT},i=o.INFO,c={[o.DEBUG]:"log",[o.VERBOSE]:"log",[o.INFO]:"info",[o.WARN]:"warn",[o.ERROR]:"error"},defaultLogHandler=(e,t,...n)=>{if(t<e.logLevel)return;const s=(new Date).toISOString(),r=c[t];if(!r)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[r](`[${s}]  ${e.name}:`,...n)};var d="@firebase/ai",l="2.0.0";const p="AI",h="us-central1",u=l,g=["user","model","function","system"],f={HARM_CATEGORY_HATE_SPEECH:"HARM_CATEGORY_HATE_SPEECH",HARM_CATEGORY_SEXUALLY_EXPLICIT:"HARM_CATEGORY_SEXUALLY_EXPLICIT",HARM_CATEGORY_HARASSMENT:"HARM_CATEGORY_HARASSMENT",HARM_CATEGORY_DANGEROUS_CONTENT:"HARM_CATEGORY_DANGEROUS_CONTENT"},E={BLOCK_LOW_AND_ABOVE:"BLOCK_LOW_AND_ABOVE",BLOCK_MEDIUM_AND_ABOVE:"BLOCK_MEDIUM_AND_ABOVE",BLOCK_ONLY_HIGH:"BLOCK_ONLY_HIGH",BLOCK_NONE:"BLOCK_NONE",OFF:"OFF"},m={SEVERITY:"SEVERITY",PROBABILITY:"PROBABILITY"},I={NEGLIGIBLE:"NEGLIGIBLE",LOW:"LOW",MEDIUM:"MEDIUM",HIGH:"HIGH"},R={HARM_SEVERITY_NEGLIGIBLE:"HARM_SEVERITY_NEGLIGIBLE",HARM_SEVERITY_LOW:"HARM_SEVERITY_LOW",HARM_SEVERITY_MEDIUM:"HARM_SEVERITY_MEDIUM",HARM_SEVERITY_HIGH:"HARM_SEVERITY_HIGH",HARM_SEVERITY_UNSUPPORTED:"HARM_SEVERITY_UNSUPPORTED"},O={SAFETY:"SAFETY",OTHER:"OTHER",BLOCKLIST:"BLOCKLIST",PROHIBITED_CONTENT:"PROHIBITED_CONTENT"},A={STOP:"STOP",MAX_TOKENS:"MAX_TOKENS",SAFETY:"SAFETY",RECITATION:"RECITATION",OTHER:"OTHER",BLOCKLIST:"BLOCKLIST",PROHIBITED_CONTENT:"PROHIBITED_CONTENT",SPII:"SPII",MALFORMED_FUNCTION_CALL:"MALFORMED_FUNCTION_CALL"},S={AUTO:"AUTO",ANY:"ANY",NONE:"NONE"},y={MODALITY_UNSPECIFIED:"MODALITY_UNSPECIFIED",TEXT:"TEXT",IMAGE:"IMAGE",VIDEO:"VIDEO",AUDIO:"AUDIO",DOCUMENT:"DOCUMENT"},_={TEXT:"TEXT",IMAGE:"IMAGE"},T={ERROR:"error",REQUEST_ERROR:"request-error",RESPONSE_ERROR:"response-error",FETCH_ERROR:"fetch-error",INVALID_CONTENT:"invalid-content",API_NOT_ENABLED:"api-not-enabled",INVALID_SCHEMA:"invalid-schema",NO_API_KEY:"no-api-key",NO_APP_ID:"no-app-id",NO_MODEL:"no-model",NO_PROJECT_ID:"no-project-id",PARSE_FAILED:"parse-failed",UNSUPPORTED:"unsupported"},N={STRING:"string",NUMBER:"number",INTEGER:"integer",BOOLEAN:"boolean",ARRAY:"array",OBJECT:"object"},C={BLOCK_LOW_AND_ABOVE:"block_low_and_above",BLOCK_MEDIUM_AND_ABOVE:"block_medium_and_above",BLOCK_ONLY_HIGH:"block_only_high",BLOCK_NONE:"block_none"},w={BLOCK_ALL:"dont_allow",ALLOW_ADULT:"allow_adult",ALLOW_ALL:"allow_all"},b={SQUARE:"1:1",LANDSCAPE_3x4:"3:4",PORTRAIT_4x3:"4:3",LANDSCAPE_16x9:"16:9",PORTRAIT_9x16:"9:16"},k={VERTEX_AI:"VERTEX_AI",GOOGLE_AI:"GOOGLE_AI"};class Backend{constructor(e){this.backendType=e}}class GoogleAIBackend extends Backend{constructor(){super(k.GOOGLE_AI)}}class VertexAIBackend extends Backend{constructor(e=h){super(k.VERTEX_AI),this.location=e||h}}class AIService{constructor(e,t,n,s){this.app=e,this.backend=t;const r=s?.getImmediate({optional:!0}),o=n?.getImmediate({optional:!0});this.auth=o||null,this.appCheck=r||null,this.location=t instanceof VertexAIBackend?t.location:""}_delete(){return Promise.resolve()}}class AIError extends FirebaseError{constructor(e,t,n){const s=`${p}: ${t} (${`${p}/${e}`})`;super(e,s),this.code=e,this.customErrorData=n,Error.captureStackTrace&&Error.captureStackTrace(this,AIError),Object.setPrototypeOf(this,AIError.prototype),this.toString=()=>s}}class AIModel{constructor(t,n){if(!t.app?.options?.apiKey)throw new AIError(T.NO_API_KEY,'The "apiKey" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid API key.');if(!t.app?.options?.projectId)throw new AIError(T.NO_PROJECT_ID,'The "projectId" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid project ID.');if(!t.app?.options?.appId)throw new AIError(T.NO_APP_ID,'The "appId" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid app ID.');if(this._apiSettings={apiKey:t.app.options.apiKey,project:t.app.options.projectId,appId:t.app.options.appId,automaticDataCollectionEnabled:t.app.automaticDataCollectionEnabled,location:t.location,backend:t.backend},e(t.app)&&t.app.settings.appCheckToken){const e=t.app.settings.appCheckToken;this._apiSettings.getAppCheckToken=()=>Promise.resolve({token:e})}else t.appCheck&&(this._apiSettings.getAppCheckToken=()=>t.appCheck.getToken());t.auth&&(this._apiSettings.getAuthToken=()=>t.auth.getToken()),this.model=AIModel.normalizeModelName(n,this._apiSettings.backend.backendType)}static normalizeModelName(e,t){return t===k.GOOGLE_AI?AIModel.normalizeGoogleAIModelName(e):AIModel.normalizeVertexAIModelName(e)}static normalizeGoogleAIModelName(e){return`models/${e}`}static normalizeVertexAIModelName(e){let t;return t=e.includes("/")?e.startsWith("models/")?`publishers/google/${e}`:e:`publishers/google/models/${e}`,t}}const L=new class Logger{constructor(e){this.name=e,this._logLevel=i,this._logHandler=defaultLogHandler,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in o))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?a[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,o.DEBUG,...e),this._logHandler(this,o.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,o.VERBOSE,...e),this._logHandler(this,o.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,o.INFO,...e),this._logHandler(this,o.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,o.WARN,...e),this._logHandler(this,o.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,o.ERROR,...e),this._logHandler(this,o.ERROR,...e)}}("@firebase/vertexai");var P;!function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.PREDICT="predict"}(P||(P={}));class RequestUrl{constructor(e,t,n,s,r){this.model=e,this.task=t,this.apiSettings=n,this.stream=s,this.requestOptions=r}toString(){const e=new URL(this.baseUrl);return e.pathname=`/${this.apiVersion}/${this.modelPath}:${this.task}`,e.search=this.queryParams.toString(),e.toString()}get baseUrl(){return this.requestOptions?.baseUrl||"https://firebasevertexai.googleapis.com"}get apiVersion(){return"v1beta"}get modelPath(){if(this.apiSettings.backend instanceof GoogleAIBackend)return`projects/${this.apiSettings.project}/${this.model}`;if(this.apiSettings.backend instanceof VertexAIBackend)return`projects/${this.apiSettings.project}/locations/${this.apiSettings.backend.location}/${this.model}`;throw new AIError(T.ERROR,`Invalid backend: ${JSON.stringify(this.apiSettings.backend)}`)}get queryParams(){const e=new URLSearchParams;return this.stream&&e.set("alt","sse"),e}}async function getHeaders(e){const t=new Headers;if(t.append("Content-Type","application/json"),t.append("x-goog-api-client",function getClientHeaders(){const e=[];return e.push(`gl-js/${u}`),e.push(`fire/${u}`),e.join(" ")}()),t.append("x-goog-api-key",e.apiSettings.apiKey),e.apiSettings.automaticDataCollectionEnabled&&t.append("X-Firebase-Appid",e.apiSettings.appId),e.apiSettings.getAppCheckToken){const n=await e.apiSettings.getAppCheckToken();n&&(t.append("X-Firebase-AppCheck",n.token),n.error&&L.warn(`Unable to obtain a valid App Check token: ${n.error.message}`))}if(e.apiSettings.getAuthToken){const n=await e.apiSettings.getAuthToken();n&&t.append("Authorization",`Firebase ${n.accessToken}`)}return t}async function makeRequest(e,t,n,s,r,o){const a=new RequestUrl(e,t,n,s,o);let i,c;try{const d=await async function constructRequest(e,t,n,s,r,o){const a=new RequestUrl(e,t,n,s,o);return{url:a.toString(),fetchOptions:{method:"POST",headers:await getHeaders(a),body:r}}}(e,t,n,s,r,o),l=null!=o?.timeout&&o.timeout>=0?o.timeout:18e4,p=new AbortController;if(c=setTimeout((()=>p.abort()),l),d.fetchOptions.signal=p.signal,i=await fetch(d.url,d.fetchOptions),!i.ok){let e,t="";try{const n=await i.json();t=n.error.message,n.error.details&&(t+=` ${JSON.stringify(n.error.details)}`,e=n.error.details)}catch(e){}if(403===i.status&&e&&e.some((e=>"SERVICE_DISABLED"===e.reason))&&e.some((e=>e.links?.[0]?.description.includes("Google developers console API activation"))))throw new AIError(T.API_NOT_ENABLED,`The Firebase AI SDK requires the Firebase AI API ('firebasevertexai.googleapis.com') to be enabled in your Firebase project. Enable this API by visiting the Firebase Console at https://console.firebase.google.com/project/${a.apiSettings.project}/genai/ and clicking "Get started". If you enabled this API recently, wait a few minutes for the action to propagate to our systems and then retry.`,{status:i.status,statusText:i.statusText,errorDetails:e});throw new AIError(T.FETCH_ERROR,`Error fetching from ${a}: [${i.status} ${i.statusText}] ${t}`,{status:i.status,statusText:i.statusText,errorDetails:e})}}catch(e){let t=e;throw e.code!==T.FETCH_ERROR&&e.code!==T.API_NOT_ENABLED&&e instanceof Error&&(t=new AIError(T.ERROR,`Error fetching from ${a.toString()}: ${e.message}`),t.stack=e.stack),t}finally{c&&clearTimeout(c)}return i}function createEnhancedContentResponse(e){e.candidates&&!e.candidates[0].hasOwnProperty("index")&&(e.candidates[0].index=0);const t=function addHelpers(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&L.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),hadBadFinishReason(e.candidates[0]))throw new AIError(T.RESPONSE_ERROR,`Response error: ${formatBlockErrorMessage(e)}. Response body stored in error.response`,{response:e});return function getText(e){const t=[];if(e.candidates?.[0].content?.parts)for(const n of e.candidates?.[0].content?.parts)n.text&&t.push(n.text);return t.length>0?t.join(""):""}(e)}if(e.promptFeedback)throw new AIError(T.RESPONSE_ERROR,`Text not available. ${formatBlockErrorMessage(e)}`,{response:e});return""},e.inlineDataParts=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&L.warn(`This response had ${e.candidates.length} candidates. Returning data from the first candidate only. Access response.candidates directly to use the other candidates.`),hadBadFinishReason(e.candidates[0]))throw new AIError(T.RESPONSE_ERROR,`Response error: ${formatBlockErrorMessage(e)}. Response body stored in error.response`,{response:e});return function getInlineDataParts(e){const t=[];if(e.candidates?.[0].content?.parts)for(const n of e.candidates?.[0].content?.parts)n.inlineData&&t.push(n);return t.length>0?t:void 0}(e)}if(e.promptFeedback)throw new AIError(T.RESPONSE_ERROR,`Data not available. ${formatBlockErrorMessage(e)}`,{response:e})},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&L.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),hadBadFinishReason(e.candidates[0]))throw new AIError(T.RESPONSE_ERROR,`Response error: ${formatBlockErrorMessage(e)}. Response body stored in error.response`,{response:e});return function getFunctionCalls(e){const t=[];if(e.candidates?.[0].content?.parts)for(const n of e.candidates?.[0].content?.parts)n.functionCall&&t.push(n.functionCall);return t.length>0?t:void 0}(e)}if(e.promptFeedback)throw new AIError(T.RESPONSE_ERROR,`Function call not available. ${formatBlockErrorMessage(e)}`,{response:e})},e}(e);return t}const M=[A.RECITATION,A.SAFETY];function hadBadFinishReason(e){return!!e.finishReason&&M.some((t=>t===e.finishReason))}function formatBlockErrorMessage(e){let t="";if(e.candidates&&0!==e.candidates.length||!e.promptFeedback){if(e.candidates?.[0]){const n=e.candidates[0];hadBadFinishReason(n)&&(t+=`Candidate was blocked due to ${n.finishReason}`,n.finishMessage&&(t+=`: ${n.finishMessage}`))}}else t+="Response was blocked",e.promptFeedback?.blockReason&&(t+=` due to ${e.promptFeedback.blockReason}`),e.promptFeedback?.blockReasonMessage&&(t+=`: ${e.promptFeedback.blockReasonMessage}`);return t}async function handlePredictResponse(e){const t=await e.json(),n=[];let s;if(!t.predictions||0===t.predictions?.length)throw new AIError(T.RESPONSE_ERROR,"No predictions or filtered reason received from Vertex AI. Please report this issue with the full error details at https://github.com/firebase/firebase-js-sdk/issues.");for(const e of t.predictions)if(e.raiFilteredReason)s=e.raiFilteredReason;else if(e.mimeType&&e.bytesBase64Encoded)n.push({mimeType:e.mimeType,bytesBase64Encoded:e.bytesBase64Encoded});else{if(!e.mimeType||!e.gcsUri)throw new AIError(T.RESPONSE_ERROR,`Predictions array in response has missing properties. Response: ${JSON.stringify(t)}`);n.push({mimeType:e.mimeType,gcsURI:e.gcsUri})}return{images:n,filteredReason:s}}function mapGenerateContentRequest(e){if(e.safetySettings?.forEach((e=>{if(e.method)throw new AIError(T.UNSUPPORTED,"SafetySetting.method is not supported in the the Gemini Developer API. Please remove this property.")})),e.generationConfig?.topK){const t=Math.round(e.generationConfig.topK);t!==e.generationConfig.topK&&(L.warn("topK in GenerationConfig has been rounded to the nearest integer to match the format for requests to the Gemini Developer API."),e.generationConfig.topK=t)}return e}function mapGenerateContentResponse(e){return{candidates:e.candidates?mapGenerateContentCandidates(e.candidates):void 0,prompt:e.promptFeedback?mapPromptFeedback(e.promptFeedback):void 0,usageMetadata:e.usageMetadata}}function mapGenerateContentCandidates(e){const t=[];let n;return t&&e.forEach((e=>{let s;if(e.citationMetadata&&(s={citations:e.citationMetadata.citationSources}),e.safetyRatings&&(n=e.safetyRatings.map((e=>({...e,severity:e.severity??R.HARM_SEVERITY_UNSUPPORTED,probabilityScore:e.probabilityScore??0,severityScore:e.severityScore??0})))),e.content?.parts.some((e=>e?.videoMetadata)))throw new AIError(T.UNSUPPORTED,"Part.videoMetadata is not supported in the Gemini Developer API. Please remove this property.");const r={index:e.index,content:e.content,finishReason:e.finishReason,finishMessage:e.finishMessage,safetyRatings:n,citationMetadata:s,groundingMetadata:e.groundingMetadata};t.push(r)})),t}function mapPromptFeedback(e){const t=[];e.safetyRatings.forEach((e=>{t.push({category:e.category,probability:e.probability,severity:e.severity??R.HARM_SEVERITY_UNSUPPORTED,probabilityScore:e.probabilityScore??0,severityScore:e.severityScore??0,blocked:e.blocked})}));return{blockReason:e.blockReason,safetyRatings:t,blockReasonMessage:e.blockReasonMessage}}const v=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function processStream(e,t){const n=function getResponseStream(e){const t=e.getReader();return new ReadableStream({start(e){let n="";return pump();function pump(){return t.read().then((({value:t,done:s})=>{if(s)return n.trim()?void e.error(new AIError(T.PARSE_FAILED,"Failed to parse stream")):void e.close();n+=t;let r,o=n.match(v);for(;o;){try{r=JSON.parse(o[1])}catch(t){return void e.error(new AIError(T.PARSE_FAILED,`Error parsing JSON response: "${o[1]}`))}e.enqueue(r),n=n.substring(o[0].length),o=n.match(v)}return pump()}))}}})}(e.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))),[s,r]=n.tee();return{stream:generateResponseSequence(s,t),response:getResponsePromise(r,t)}}async function getResponsePromise(e,t){const n=[],s=e.getReader();for(;;){const{done:e,value:r}=await s.read();if(e){let e=aggregateResponses(n);return t.backend.backendType===k.GOOGLE_AI&&(e=mapGenerateContentResponse(e)),createEnhancedContentResponse(e)}n.push(r)}}async function*generateResponseSequence(e,t){const n=e.getReader();for(;;){const{value:e,done:s}=await n.read();if(s)break;let r;r=t.backend.backendType===k.GOOGLE_AI?createEnhancedContentResponse(mapGenerateContentResponse(e)):createEnhancedContentResponse(e),yield r}}function aggregateResponses(e){const t=e[e.length-1],n={promptFeedback:t?.promptFeedback};for(const t of e)if(t.candidates)for(const e of t.candidates){const t=e.index||0;if(n.candidates||(n.candidates=[]),n.candidates[t]||(n.candidates[t]={index:e.index}),n.candidates[t].citationMetadata=e.citationMetadata,n.candidates[t].finishReason=e.finishReason,n.candidates[t].finishMessage=e.finishMessage,n.candidates[t].safetyRatings=e.safetyRatings,n.candidates[t].groundingMetadata=e.groundingMetadata,e.content&&e.content.parts){n.candidates[t].content||(n.candidates[t].content={role:e.content.role||"user",parts:[]});const s={};for(const r of e.content.parts){if(void 0!==r.text){if(""===r.text)continue;s.text=r.text}if(r.functionCall&&(s.functionCall=r.functionCall),0===Object.keys(s).length)throw new AIError(T.INVALID_CONTENT,"Part should have at least one property, but there are none. This is likely caused by a malformed response from the backend.");n.candidates[t].content.parts.push(s)}}}return n}async function generateContentStream(e,t,n,s){e.backend.backendType===k.GOOGLE_AI&&(n=mapGenerateContentRequest(n));return processStream(await makeRequest(t,P.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(n),s),e)}async function generateContent(e,t,n,s){e.backend.backendType===k.GOOGLE_AI&&(n=mapGenerateContentRequest(n));const r=await makeRequest(t,P.GENERATE_CONTENT,e,!1,JSON.stringify(n),s),o=await async function processGenerateContentResponse(e,t){const n=await e.json();return t.backend.backendType===k.GOOGLE_AI?mapGenerateContentResponse(n):n}(r,e);return{response:createEnhancedContentResponse(o)}}function formatSystemInstruction(e){if(null!=e)return"string"==typeof e?{role:"system",parts:[{text:e}]}:e.text?{role:"system",parts:[e]}:e.parts?e.role?e:{role:"system",parts:e.parts}:void 0}function formatNewContent(e){let t=[];if("string"==typeof e)t=[{text:e}];else for(const n of e)"string"==typeof n?t.push({text:n}):t.push(n);return function assignRoleToPartsAndValidateSendMessageRequest(e){const t={role:"user",parts:[]},n={role:"function",parts:[]};let s=!1,r=!1;for(const o of e)"functionResponse"in o?(n.parts.push(o),r=!0):(t.parts.push(o),s=!0);if(s&&r)throw new AIError(T.INVALID_CONTENT,"Within a single message, FunctionResponse cannot be mixed with other type of Part in the request for sending chat message.");if(!s&&!r)throw new AIError(T.INVALID_CONTENT,"No Content is provided for sending chat message.");if(s)return t;return n}(t)}function formatGenerateContentInput(e){let t;if(e.contents)t=e;else{t={contents:[formatNewContent(e)]}}return e.systemInstruction&&(t.systemInstruction=formatSystemInstruction(e.systemInstruction)),t}function createPredictRequestBody(e,{gcsURI:t,imageFormat:n,addWatermark:s,numberOfImages:r=1,negativePrompt:o,aspectRatio:a,safetyFilterLevel:i,personFilterLevel:c}){return{instances:[{prompt:e}],parameters:{storageUri:t,negativePrompt:o,sampleCount:r,aspectRatio:a,outputOptions:n,addWatermark:s,safetyFilterLevel:i,personGeneration:c,includeRaiReason:!0}}}const G=["text","inlineData","functionCall","functionResponse"],D={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall"],system:["text"]},B={user:["model"],function:["model"],model:["user","function"],system:[]};const H="SILENT_ERROR";class ChatSession{constructor(e,t,n,s){this.model=t,this.params=n,this.requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiSettings=e,n?.history&&(!function validateChatHistory(e){let t=null;for(const n of e){const{role:e,parts:s}=n;if(!t&&"user"!==e)throw new AIError(T.INVALID_CONTENT,`First Content should be with role 'user', got ${e}`);if(!g.includes(e))throw new AIError(T.INVALID_CONTENT,`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(g)}`);if(!Array.isArray(s))throw new AIError(T.INVALID_CONTENT,"Content should have 'parts' but property with an array of Parts");if(0===s.length)throw new AIError(T.INVALID_CONTENT,"Each Content should have at least one part");const r={text:0,inlineData:0,functionCall:0,functionResponse:0};for(const e of s)for(const t of G)t in e&&(r[t]+=1);const o=D[e];for(const t of G)if(!o.includes(t)&&r[t]>0)throw new AIError(T.INVALID_CONTENT,`Content with role '${e}' can't contain '${t}' part`);if(t&&!B[e].includes(t.role))throw new AIError(T.INVALID_CONTENT,`Content with role '${e}' can't follow '${t.role}'. Valid previous roles: ${JSON.stringify(B)}`);t=n}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e){await this._sendPromise;const t=formatNewContent(e),n={safetySettings:this.params?.safetySettings,generationConfig:this.params?.generationConfig,tools:this.params?.tools,toolConfig:this.params?.toolConfig,systemInstruction:this.params?.systemInstruction,contents:[...this._history,t]};let s={};return this._sendPromise=this._sendPromise.then((()=>generateContent(this._apiSettings,this.model,n,this.requestOptions))).then((e=>{if(e.response.candidates&&e.response.candidates.length>0){this._history.push(t);const n={parts:e.response.candidates?.[0].content.parts||[],role:e.response.candidates?.[0].content.role||"model"};this._history.push(n)}else{const t=formatBlockErrorMessage(e.response);t&&L.warn(`sendMessage() was unsuccessful. ${t}. Inspect response object for details.`)}s=e})),await this._sendPromise,s}async sendMessageStream(e){await this._sendPromise;const t=formatNewContent(e),n={safetySettings:this.params?.safetySettings,generationConfig:this.params?.generationConfig,tools:this.params?.tools,toolConfig:this.params?.toolConfig,systemInstruction:this.params?.systemInstruction,contents:[...this._history,t]},s=generateContentStream(this._apiSettings,this.model,n,this.requestOptions);return this._sendPromise=this._sendPromise.then((()=>s)).catch((e=>{throw new Error(H)})).then((e=>e.response)).then((e=>{if(e.candidates&&e.candidates.length>0){this._history.push(t);const n={...e.candidates[0].content};n.role||(n.role="model"),this._history.push(n)}else{const t=formatBlockErrorMessage(e);t&&L.warn(`sendMessageStream() was unsuccessful. ${t}. Inspect response object for details.`)}})).catch((e=>{e.message!==H&&L.error(e)})),s}}class GenerativeModel extends AIModel{constructor(e,t,n){super(e,t.model),this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=formatSystemInstruction(t.systemInstruction),this.requestOptions=n||{}}async generateContent(e){const t=formatGenerateContentInput(e);return generateContent(this._apiSettings,this.model,{generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,...t},this.requestOptions)}async generateContentStream(e){const t=formatGenerateContentInput(e);return generateContentStream(this._apiSettings,this.model,{generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,...t},this.requestOptions)}startChat(e){return new ChatSession(this._apiSettings,this.model,{tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,generationConfig:this.generationConfig,safetySettings:this.safetySettings,...e},this.requestOptions)}async countTokens(e){const t=formatGenerateContentInput(e);return async function countTokens(e,t,n,s){let r="";if(e.backend.backendType===k.GOOGLE_AI){const e=function mapCountTokensRequest(e,t){return{generateContentRequest:{model:t,...e}}}(n,t);r=JSON.stringify(e)}else r=JSON.stringify(n);return(await makeRequest(t,P.COUNT_TOKENS,e,!1,r,s)).json()}(this._apiSettings,this.model,t)}}class ImagenModel extends AIModel{constructor(e,t,n){const{model:s,generationConfig:r,safetySettings:o}=t;super(e,s),this.requestOptions=n,this.generationConfig=r,this.safetySettings=o}async generateImages(e){const t=createPredictRequestBody(e,{...this.generationConfig,...this.safetySettings});return handlePredictResponse(await makeRequest(this.model,P.PREDICT,this._apiSettings,!1,JSON.stringify(t),this.requestOptions))}async generateImagesGCS(e,t){const n=createPredictRequestBody(e,{gcsURI:t,...this.generationConfig,...this.safetySettings});return handlePredictResponse(await makeRequest(this.model,P.PREDICT,this._apiSettings,!1,JSON.stringify(n),this.requestOptions))}}class Schema{constructor(e){if(!e.type&&!e.anyOf)throw new AIError(T.INVALID_SCHEMA,"A schema must have either a 'type' or an 'anyOf' array of sub-schemas.");for(const t in e)this[t]=e[t];this.type=e.type,this.format=e.hasOwnProperty("format")?e.format:void 0,this.nullable=!!e.hasOwnProperty("nullable")&&!!e.nullable}toJSON(){const e={type:this.type};for(const t in this)this.hasOwnProperty(t)&&void 0!==this[t]&&("required"===t&&this.type!==N.OBJECT||(e[t]=this[t]));return e}static array(e){return new ArraySchema(e,e.items)}static object(e){return new ObjectSchema(e,e.properties,e.optionalProperties)}static string(e){return new StringSchema(e)}static enumString(e){return new StringSchema(e,e.enum)}static integer(e){return new IntegerSchema(e)}static number(e){return new NumberSchema(e)}static boolean(e){return new BooleanSchema(e)}static anyOf(e){return new AnyOfSchema(e)}}class IntegerSchema extends Schema{constructor(e){super({type:N.INTEGER,...e})}}class NumberSchema extends Schema{constructor(e){super({type:N.NUMBER,...e})}}class BooleanSchema extends Schema{constructor(e){super({type:N.BOOLEAN,...e})}}class StringSchema extends Schema{constructor(e,t){super({type:N.STRING,...e}),this.enum=t}toJSON(){const e=super.toJSON();return this.enum&&(e.enum=this.enum),e}}class ArraySchema extends Schema{constructor(e,t){super({type:N.ARRAY,...e}),this.items=t}toJSON(){const e=super.toJSON();return e.items=this.items.toJSON(),e}}class ObjectSchema extends Schema{constructor(e,t,n=[]){super({type:N.OBJECT,...e}),this.properties=t,this.optionalProperties=n}toJSON(){const e=super.toJSON();e.properties={...this.properties};const t=[];if(this.optionalProperties)for(const e of this.optionalProperties)if(!this.properties.hasOwnProperty(e))throw new AIError(T.INVALID_SCHEMA,`Property "${e}" specified in "optionalProperties" does not exist.`);for(const n in this.properties)this.properties.hasOwnProperty(n)&&(e.properties[n]=this.properties[n].toJSON(),this.optionalProperties.includes(n)||t.push(n));return t.length>0&&(e.required=t),delete e.optionalProperties,e}}class AnyOfSchema extends Schema{constructor(e){if(0===e.anyOf.length)throw new AIError(T.INVALID_SCHEMA,"The 'anyOf' array must not be empty.");super({...e,type:void 0}),this.anyOf=e.anyOf}toJSON(){const e=super.toJSON();return this.anyOf&&Array.isArray(this.anyOf)&&(e.anyOf=this.anyOf.map((e=>e.toJSON()))),e}}class ImagenImageFormat{constructor(){this.mimeType="image/png"}static jpeg(e){return e&&(e<0||e>100)&&L.warn(`Invalid JPEG compression quality of ${e} specified; the supported range is [0, 100].`),{mimeType:"image/jpeg",compressionQuality:e}}static png(){return{mimeType:"image/png"}}}function getAI(e=t(),n={backend:new GoogleAIBackend}){e=function getModularInstance(e){return e&&e._delegate?e._delegate:e}(e);const s=_getProvider(e,p),r=function encodeInstanceIdentifier(e){if(e instanceof GoogleAIBackend)return`${p}/googleai`;if(e instanceof VertexAIBackend)return`${p}/vertexai/${e.location}`;throw new AIError(T.ERROR,`Invalid backend: ${JSON.stringify(e.backendType)}`)}(n.backend);return s.getImmediate({identifier:r})}function getGenerativeModel(e,t,n){if(!t.model)throw new AIError(T.NO_MODEL,"Must provide a model name. Example: getGenerativeModel({ model: 'my-model-name' })");return new GenerativeModel(e,t,n)}function getImagenModel(e,t,n){if(!t.model)throw new AIError(T.NO_MODEL,"Must provide a model name. Example: getImagenModel({ model: 'my-model-name' })");return new ImagenModel(e,t,n)}!function registerAI(){n(new Component(p,((e,{instanceIdentifier:t})=>{if(!t)throw new AIError(T.ERROR,"AIService instance identifier is undefined.");const n=function decodeInstanceIdentifier(e){const t=e.split("/");if(t[0]!==p)throw new AIError(T.ERROR,`Invalid instance identifier, unknown prefix '${t[0]}'`);switch(t[1]){case"vertexai":const n=t[2];if(!n)throw new AIError(T.ERROR,`Invalid instance identifier, unknown location '${e}'`);return new VertexAIBackend(n);case"googleai":return new GoogleAIBackend;default:throw new AIError(T.ERROR,`Invalid instance identifier string: '${e}'`)}}(t),s=e.getProvider("app").getImmediate(),r=e.getProvider("auth-internal"),o=e.getProvider("app-check-internal");return new AIService(s,n,r,o)}),"PUBLIC").setMultipleInstances(!0)),s(d,l),s(d,l,"esm2020")}();export{AIError,T as AIErrorCode,AIModel,AnyOfSchema,ArraySchema,Backend,k as BackendType,O as BlockReason,BooleanSchema,ChatSession,A as FinishReason,S as FunctionCallingMode,GenerativeModel,GoogleAIBackend,m as HarmBlockMethod,E as HarmBlockThreshold,f as HarmCategory,I as HarmProbability,R as HarmSeverity,b as ImagenAspectRatio,ImagenImageFormat,ImagenModel,w as ImagenPersonFilterLevel,C as ImagenSafetyFilterLevel,IntegerSchema,y as Modality,NumberSchema,ObjectSchema,g as POSSIBLE_ROLES,_ as ResponseModality,Schema,N as SchemaType,StringSchema,VertexAIBackend,getAI,getGenerativeModel,getImagenModel};

//# sourceMappingURL=firebase-ai.js.map
