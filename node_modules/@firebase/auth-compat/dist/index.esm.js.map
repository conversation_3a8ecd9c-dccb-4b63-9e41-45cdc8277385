{"version": 3, "file": "index.esm.js", "sources": ["../src/platform.ts", "../src/persistence.ts", "../src/popup_redirect.ts", "../src/wrap.ts", "../src/user_credential.ts", "../src/user.ts", "../src/auth.ts", "../src/phone_auth_provider.ts", "../src/recaptcha_verifier.ts", "../index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as impl from '@firebase/auth/internal';\nimport {\n  getUA,\n  isBrowserExtension,\n  isReactNative,\n  isNode,\n  isIE,\n  isIndexedDBAvailable\n} from '@firebase/util';\n\ndeclare global {\n  interface Document {\n    documentMode?: number;\n  }\n}\n\nconst CORDOVA_ONDEVICEREADY_TIMEOUT_MS = 1000;\n\nfunction _getCurrentScheme(): string | null {\n  return self?.location?.protocol || null;\n}\n\n/**\n * @return {boolean} Whether the current environment is http or https.\n */\nfunction _isHttpOrHttps(): boolean {\n  return _getCurrentScheme() === 'http:' || _getCurrentScheme() === 'https:';\n}\n\n/**\n * @param {?string=} ua The user agent.\n * @return {boolean} Whether the app is rendered in a mobile iOS or Android\n *     Cordova environment.\n */\nexport function _isAndroidOrIosCordovaScheme(ua: string = getUA()): boolean {\n  return !!(\n    (_getCurrentScheme() === 'file:' ||\n      _getCurrentScheme() === 'ionic:' ||\n      _getCurrentScheme() === 'capacitor:') &&\n    ua.toLowerCase().match(/iphone|ipad|ipod|android/)\n  );\n}\n\n/**\n * @return {boolean} Whether the environment is a native environment, where\n *     CORS checks do not apply.\n */\nfunction _isNativeEnvironment(): boolean {\n  return isReactNative() || isNode();\n}\n\n/**\n * Checks whether the user agent is IE11.\n * @return {boolean} True if it is IE11.\n */\nfunction _isIe11(): boolean {\n  return isIE() && document?.documentMode === 11;\n}\n\n/**\n * Checks whether the user agent is Edge.\n * @param {string} userAgent The browser user agent string.\n * @return {boolean} True if it is Edge.\n */\nfunction _isEdge(ua: string = getUA()): boolean {\n  return /Edge\\/\\d+/.test(ua);\n}\n\n/**\n * @param {?string=} opt_userAgent The navigator user agent.\n * @return {boolean} Whether local storage is not synchronized between an iframe\n *     and a popup of the same domain.\n */\nfunction _isLocalStorageNotSynchronized(ua: string = getUA()): boolean {\n  return _isIe11() || _isEdge(ua);\n}\n\n/** @return {boolean} Whether web storage is supported. */\nexport function _isWebStorageSupported(): boolean {\n  try {\n    const storage = self.localStorage;\n    const key = impl._generateEventId();\n    if (storage) {\n      // setItem will throw an exception if we cannot access WebStorage (e.g.,\n      // Safari in private mode).\n      storage['setItem'](key, '1');\n      storage['removeItem'](key);\n      // For browsers where iframe web storage does not synchronize with a popup\n      // of the same domain, indexedDB is used for persistent storage. These\n      // browsers include IE11 and Edge.\n      // Make sure it is supported (IE11 and Edge private mode does not support\n      // that).\n      if (_isLocalStorageNotSynchronized()) {\n        // In such browsers, if indexedDB is not supported, an iframe cannot be\n        // notified of the popup sign in result.\n        return isIndexedDBAvailable();\n      }\n      return true;\n    }\n  } catch (e) {\n    // localStorage is not available from a worker. Test availability of\n    // indexedDB.\n    return _isWorker() && isIndexedDBAvailable();\n  }\n  return false;\n}\n\n/**\n * @param {?Object=} global The optional global scope.\n * @return {boolean} Whether current environment is a worker.\n */\nexport function _isWorker(): boolean {\n  // WorkerGlobalScope only defined in worker environment.\n  return (\n    typeof global !== 'undefined' &&\n    'WorkerGlobalScope' in global &&\n    'importScripts' in global\n  );\n}\n\nexport function _isPopupRedirectSupported(): boolean {\n  return (\n    (_isHttpOrHttps() ||\n      isBrowserExtension() ||\n      _isAndroidOrIosCordovaScheme()) &&\n    // React Native with remote debugging reports its location.protocol as\n    // http.\n    !_isNativeEnvironment() &&\n    // Local storage has to be supported for browser popup and redirect\n    // operations to work.\n    _isWebStorageSupported() &&\n    // DOM, popups and redirects are not supported within a worker.\n    !_isWorker()\n  );\n}\n\n/** Quick check that indicates the platform *may* be Cordova */\nexport function _isLikelyCordova(): boolean {\n  return _isAndroidOrIosCordovaScheme() && typeof document !== 'undefined';\n}\n\nexport async function _isCordova(): Promise<boolean> {\n  if (!_isLikelyCordova()) {\n    return false;\n  }\n\n  return new Promise(resolve => {\n    const timeoutId = setTimeout(() => {\n      // We've waited long enough; the telltale Cordova event didn't happen\n      resolve(false);\n    }, CORDOVA_ONDEVICEREADY_TIMEOUT_MS);\n\n    document.addEventListener('deviceready', () => {\n      clearTimeout(timeoutId);\n      resolve(true);\n    });\n  });\n}\n\nexport function _getSelfWindow(): Window | null {\n  return typeof window !== 'undefined' ? window : null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as exp from '@firebase/auth/internal';\nimport { isIndexedDBAvailable, isNode, isReactNative } from '@firebase/util';\nimport { _getSelfWindow, _isWebStorageSupported, _isWorker } from './platform';\n\nexport const Persistence = {\n  LOCAL: 'local',\n  NONE: 'none',\n  SESSION: 'session'\n};\n\nconst _assert: typeof exp._assert = exp._assert;\n\nconst PERSISTENCE_KEY = 'persistence';\n\n/**\n * Validates that an argument is a valid persistence value. If an invalid type\n * is specified, an error is thrown synchronously.\n */\nexport function _validatePersistenceArgument(\n  auth: exp.Auth,\n  persistence: string\n): void {\n  _assert(\n    Object.values(Persistence).includes(persistence),\n    auth,\n    exp.AuthErrorCode.INVALID_PERSISTENCE\n  );\n  // Validate if the specified type is supported in the current environment.\n  if (isReactNative()) {\n    // This is only supported in a browser.\n    _assert(\n      persistence !== Persistence.SESSION,\n      auth,\n      exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE\n    );\n    return;\n  }\n  if (isNode()) {\n    // Only none is supported in Node.js.\n    _assert(\n      persistence === Persistence.NONE,\n      auth,\n      exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE\n    );\n    return;\n  }\n  if (_isWorker()) {\n    // In a worker environment, either LOCAL or NONE are supported.\n    // If indexedDB not supported and LOCAL provided, throw an error\n    _assert(\n      persistence === Persistence.NONE ||\n        (persistence === Persistence.LOCAL && isIndexedDBAvailable()),\n      auth,\n      exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE\n    );\n    return;\n  }\n  // This is restricted by what the browser supports.\n  _assert(\n    persistence === Persistence.NONE || _isWebStorageSupported(),\n    auth,\n    exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE\n  );\n}\n\nexport async function _savePersistenceForRedirect(\n  auth: exp.AuthInternal\n): Promise<void> {\n  await auth._initializationPromise;\n  const session = getSessionStorageIfAvailable();\n  const key = exp._persistenceKeyName(\n    PERSISTENCE_KEY,\n    auth.config.apiKey,\n    auth.name\n  );\n  if (session) {\n    session.setItem(key, auth._getPersistenceType());\n  }\n}\n\nexport function _getPersistencesFromRedirect(\n  apiKey: string,\n  appName: string\n): exp.Persistence[] {\n  const session = getSessionStorageIfAvailable();\n  if (!session) {\n    return [];\n  }\n\n  const key = exp._persistenceKeyName(PERSISTENCE_KEY, apiKey, appName);\n  const persistence = session.getItem(key);\n\n  switch (persistence) {\n    case Persistence.NONE:\n      return [exp.inMemoryPersistence];\n    case Persistence.LOCAL:\n      return [exp.indexedDBLocalPersistence, exp.browserSessionPersistence];\n    case Persistence.SESSION:\n      return [exp.browserSessionPersistence];\n    default:\n      return [];\n  }\n}\n\n/** Returns session storage, or null if the property access errors */\nfunction getSessionStorageIfAvailable(): Storage | null {\n  try {\n    return _getSelfWindow()?.sessionStorage || null;\n  } catch (e) {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as exp from '@firebase/auth/internal';\nimport { _isCordova, _isLikelyCordova } from './platform';\n\nconst _assert: typeof exp._assert = exp._assert;\n\n/** Platform-agnostic popup-redirect resolver */\nexport class CompatPopupRedirectResolver\n  implements exp.PopupRedirectResolverInternal\n{\n  // Create both resolvers for dynamic resolution later\n  private readonly browserResolver: exp.PopupRedirectResolverInternal =\n    exp._getInstance(exp.browserPopupRedirectResolver);\n  private readonly cordovaResolver: exp.PopupRedirectResolverInternal =\n    exp._getInstance(exp.cordovaPopupRedirectResolver);\n  // The actual resolver in use: either browserResolver or cordovaResolver.\n  private underlyingResolver: exp.PopupRedirectResolverInternal | null = null;\n  _redirectPersistence = exp.browserSessionPersistence;\n\n  _completeRedirectFn: (\n    auth: exp.Auth,\n    resolver: exp.PopupRedirectResolver,\n    bypassAuthState: boolean\n  ) => Promise<exp.UserCredential | null> = exp._getRedirectResult;\n  _overrideRedirectResult = exp._overrideRedirectResult;\n\n  async _initialize(auth: exp.AuthImpl): Promise<exp.EventManager> {\n    await this.selectUnderlyingResolver();\n    return this.assertedUnderlyingResolver._initialize(auth);\n  }\n\n  async _openPopup(\n    auth: exp.AuthImpl,\n    provider: exp.AuthProvider,\n    authType: exp.AuthEventType,\n    eventId?: string\n  ): Promise<exp.AuthPopup> {\n    await this.selectUnderlyingResolver();\n    return this.assertedUnderlyingResolver._openPopup(\n      auth,\n      provider,\n      authType,\n      eventId\n    );\n  }\n\n  async _openRedirect(\n    auth: exp.AuthImpl,\n    provider: exp.AuthProvider,\n    authType: exp.AuthEventType,\n    eventId?: string\n  ): Promise<void> {\n    await this.selectUnderlyingResolver();\n    return this.assertedUnderlyingResolver._openRedirect(\n      auth,\n      provider,\n      authType,\n      eventId\n    );\n  }\n\n  _isIframeWebStorageSupported(\n    auth: exp.AuthImpl,\n    cb: (support: boolean) => unknown\n  ): void {\n    this.assertedUnderlyingResolver._isIframeWebStorageSupported(auth, cb);\n  }\n\n  _originValidation(auth: exp.Auth): Promise<void> {\n    return this.assertedUnderlyingResolver._originValidation(auth);\n  }\n\n  get _shouldInitProactively(): boolean {\n    return _isLikelyCordova() || this.browserResolver._shouldInitProactively;\n  }\n\n  private get assertedUnderlyingResolver(): exp.PopupRedirectResolverInternal {\n    _assert(this.underlyingResolver, exp.AuthErrorCode.INTERNAL_ERROR);\n    return this.underlyingResolver;\n  }\n\n  private async selectUnderlyingResolver(): Promise<void> {\n    if (this.underlyingResolver) {\n      return;\n    }\n\n    // We haven't yet determined whether or not we're in Cordova; go ahead\n    // and determine that state now.\n    const isCordova = await _isCordova();\n    this.underlyingResolver = isCordova\n      ? this.cordovaResolver\n      : this.browserResolver;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Forward direction wrapper from Compat --unwrap-> Exp */\nexport interface Wrapper<T> {\n  unwrap(): T;\n}\n\n/** Reverse direction wrapper from Exp --wrapped--> Compat */\nexport interface ReverseWrapper<T> {\n  wrapped(): T;\n}\n\nexport function unwrap<T>(object: unknown): T {\n  return (object as Wrapper<T>).unwrap();\n}\n\nexport function wrapped<T>(object: unknown): T {\n  return (object as ReverseWrapper<T>).wrapped();\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as exp from '@firebase/auth/internal';\nimport * as compat from '@firebase/auth-types';\nimport { FirebaseError } from '@firebase/util';\nimport { Auth } from './auth';\nimport { User } from './user';\nimport { unwrap, wrapped } from './wrap';\n\nfunction credentialFromResponse(\n  userCredential: exp.UserCredentialInternal\n): exp.AuthCredential | null {\n  return credentialFromObject(userCredential);\n}\n\nfunction attachExtraErrorFields(auth: exp.Auth, e: FirebaseError): void {\n  // The response contains all fields from the server which may or may not\n  // actually match the underlying type\n  const response = (e.customData as exp.TaggedWithTokenResponse | undefined)\n    ?._tokenResponse as unknown as Record<string, string>;\n  if ((e as FirebaseError)?.code === 'auth/multi-factor-auth-required') {\n    const mfaErr = e as compat.MultiFactorError;\n    mfaErr.resolver = new MultiFactorResolver(\n      auth,\n      exp.getMultiFactorResolver(auth, e as exp.MultiFactorError)\n    );\n  } else if (response) {\n    const credential = credentialFromObject(e);\n    const credErr = e as compat.AuthError;\n    if (credential) {\n      credErr.credential = credential;\n      credErr.tenantId = response.tenantId || undefined;\n      credErr.email = response.email || undefined;\n      credErr.phoneNumber = response.phoneNumber || undefined;\n    }\n  }\n}\n\nfunction credentialFromObject(\n  object: FirebaseError | exp.UserCredential\n): exp.AuthCredential | null {\n  const { _tokenResponse } = (\n    object instanceof FirebaseError ? object.customData : object\n  ) as exp.TaggedWithTokenResponse;\n  if (!_tokenResponse) {\n    return null;\n  }\n\n  // Handle phone Auth credential responses, as they have a different format\n  // from other backend responses (i.e. no providerId). This is also only the\n  // case for user credentials (does not work for errors).\n  if (!(object instanceof FirebaseError)) {\n    if ('temporaryProof' in _tokenResponse && 'phoneNumber' in _tokenResponse) {\n      return exp.PhoneAuthProvider.credentialFromResult(object);\n    }\n  }\n\n  const providerId = _tokenResponse.providerId;\n\n  // Email and password is not supported as there is no situation where the\n  // server would return the password to the client.\n  if (!providerId || providerId === exp.ProviderId.PASSWORD) {\n    return null;\n  }\n\n  let provider: Pick<\n    typeof exp.OAuthProvider,\n    'credentialFromResult' | 'credentialFromError'\n  >;\n  switch (providerId) {\n    case exp.ProviderId.GOOGLE:\n      provider = exp.GoogleAuthProvider;\n      break;\n    case exp.ProviderId.FACEBOOK:\n      provider = exp.FacebookAuthProvider;\n      break;\n    case exp.ProviderId.GITHUB:\n      provider = exp.GithubAuthProvider;\n      break;\n    case exp.ProviderId.TWITTER:\n      provider = exp.TwitterAuthProvider;\n      break;\n    default:\n      const {\n        oauthIdToken,\n        oauthAccessToken,\n        oauthTokenSecret,\n        pendingToken,\n        nonce\n      } = _tokenResponse as exp.SignInWithIdpResponse;\n      if (\n        !oauthAccessToken &&\n        !oauthTokenSecret &&\n        !oauthIdToken &&\n        !pendingToken\n      ) {\n        return null;\n      }\n      // TODO(avolkovi): uncomment this and get it working with SAML & OIDC\n      if (pendingToken) {\n        if (providerId.startsWith('saml.')) {\n          return exp.SAMLAuthCredential._create(providerId, pendingToken);\n        } else {\n          // OIDC and non-default providers excluding Twitter.\n          return exp.OAuthCredential._fromParams({\n            providerId,\n            signInMethod: providerId,\n            pendingToken,\n            idToken: oauthIdToken,\n            accessToken: oauthAccessToken\n          });\n        }\n      }\n      return new exp.OAuthProvider(providerId).credential({\n        idToken: oauthIdToken,\n        accessToken: oauthAccessToken,\n        rawNonce: nonce\n      });\n  }\n\n  return object instanceof FirebaseError\n    ? provider.credentialFromError(object)\n    : provider.credentialFromResult(object);\n}\n\nexport function convertCredential(\n  auth: exp.Auth,\n  credentialPromise: Promise<exp.UserCredential>\n): Promise<compat.UserCredential> {\n  return credentialPromise\n    .catch(e => {\n      if (e instanceof FirebaseError) {\n        attachExtraErrorFields(auth, e);\n      }\n      throw e;\n    })\n    .then(credential => {\n      const operationType = credential.operationType;\n      const user = credential.user;\n\n      return {\n        operationType,\n        credential: credentialFromResponse(\n          credential as exp.UserCredentialInternal\n        ),\n        additionalUserInfo: exp.getAdditionalUserInfo(\n          credential as exp.UserCredential\n        ),\n        user: User.getOrCreate(user)\n      };\n    });\n}\n\nexport async function convertConfirmationResult(\n  auth: exp.Auth,\n  confirmationResultPromise: Promise<exp.ConfirmationResult>\n): Promise<compat.ConfirmationResult> {\n  const confirmationResultExp = await confirmationResultPromise;\n  return {\n    verificationId: confirmationResultExp.verificationId,\n    confirm: (verificationCode: string) =>\n      convertCredential(auth, confirmationResultExp.confirm(verificationCode))\n  };\n}\n\nclass MultiFactorResolver implements compat.MultiFactorResolver {\n  readonly auth: Auth;\n  constructor(\n    auth: exp.Auth,\n    private readonly resolver: exp.MultiFactorResolver\n  ) {\n    this.auth = wrapped(auth);\n  }\n\n  get session(): compat.MultiFactorSession {\n    return this.resolver.session;\n  }\n\n  get hints(): compat.MultiFactorInfo[] {\n    return this.resolver.hints;\n  }\n\n  resolveSignIn(\n    assertion: compat.MultiFactorAssertion\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      unwrap(this.auth),\n      this.resolver.resolveSignIn(assertion as exp.MultiFactorAssertion)\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as exp from '@firebase/auth/internal';\nimport * as compat from '@firebase/auth-types';\nimport { Compat } from '@firebase/util';\nimport { _savePersistenceForRedirect } from './persistence';\nimport { CompatPopupRedirectResolver } from './popup_redirect';\nimport {\n  convertConfirmationResult,\n  convertCredential\n} from './user_credential';\n\nexport class User implements compat.User, Compat<exp.User> {\n  // Maintain a map so that there's always a 1:1 mapping between new User and\n  // legacy compat users\n  private static readonly USER_MAP = new WeakMap<exp.User, User>();\n\n  readonly multiFactor: compat.MultiFactorUser;\n\n  private constructor(readonly _delegate: exp.User) {\n    this.multiFactor = exp.multiFactor(_delegate);\n  }\n\n  static getOrCreate(user: exp.User): User {\n    if (!User.USER_MAP.has(user)) {\n      User.USER_MAP.set(user, new User(user));\n    }\n\n    return User.USER_MAP.get(user)!;\n  }\n\n  delete(): Promise<void> {\n    return this._delegate.delete();\n  }\n  reload(): Promise<void> {\n    return this._delegate.reload();\n  }\n  toJSON(): object {\n    return this._delegate.toJSON();\n  }\n  getIdTokenResult(forceRefresh?: boolean): Promise<compat.IdTokenResult> {\n    return this._delegate.getIdTokenResult(forceRefresh);\n  }\n  getIdToken(forceRefresh?: boolean): Promise<string> {\n    return this._delegate.getIdToken(forceRefresh);\n  }\n  linkAndRetrieveDataWithCredential(\n    credential: compat.AuthCredential\n  ): Promise<compat.UserCredential> {\n    return this.linkWithCredential(credential);\n  }\n  async linkWithCredential(\n    credential: compat.AuthCredential\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this.auth,\n      exp.linkWithCredential(this._delegate, credential as exp.AuthCredential)\n    );\n  }\n  async linkWithPhoneNumber(\n    phoneNumber: string,\n    applicationVerifier: compat.ApplicationVerifier\n  ): Promise<compat.ConfirmationResult> {\n    return convertConfirmationResult(\n      this.auth,\n      exp.linkWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier)\n    );\n  }\n  async linkWithPopup(\n    provider: compat.AuthProvider\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this.auth,\n      exp.linkWithPopup(\n        this._delegate,\n        provider as exp.AuthProvider,\n        CompatPopupRedirectResolver\n      )\n    );\n  }\n  async linkWithRedirect(provider: compat.AuthProvider): Promise<void> {\n    await _savePersistenceForRedirect(exp._castAuth(this.auth));\n    return exp.linkWithRedirect(\n      this._delegate,\n      provider as exp.AuthProvider,\n      CompatPopupRedirectResolver\n    );\n  }\n  reauthenticateAndRetrieveDataWithCredential(\n    credential: compat.AuthCredential\n  ): Promise<compat.UserCredential> {\n    return this.reauthenticateWithCredential(credential);\n  }\n  async reauthenticateWithCredential(\n    credential: compat.AuthCredential\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this.auth as unknown as exp.Auth,\n      exp.reauthenticateWithCredential(\n        this._delegate,\n        credential as exp.AuthCredential\n      )\n    );\n  }\n  reauthenticateWithPhoneNumber(\n    phoneNumber: string,\n    applicationVerifier: compat.ApplicationVerifier\n  ): Promise<compat.ConfirmationResult> {\n    return convertConfirmationResult(\n      this.auth,\n      exp.reauthenticateWithPhoneNumber(\n        this._delegate,\n        phoneNumber,\n        applicationVerifier\n      )\n    );\n  }\n  reauthenticateWithPopup(\n    provider: compat.AuthProvider\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this.auth,\n      exp.reauthenticateWithPopup(\n        this._delegate,\n        provider as exp.AuthProvider,\n        CompatPopupRedirectResolver\n      )\n    );\n  }\n  async reauthenticateWithRedirect(\n    provider: compat.AuthProvider\n  ): Promise<void> {\n    await _savePersistenceForRedirect(exp._castAuth(this.auth));\n    return exp.reauthenticateWithRedirect(\n      this._delegate,\n      provider as exp.AuthProvider,\n      CompatPopupRedirectResolver\n    );\n  }\n  sendEmailVerification(\n    actionCodeSettings?: compat.ActionCodeSettings | null\n  ): Promise<void> {\n    return exp.sendEmailVerification(this._delegate, actionCodeSettings);\n  }\n  async unlink(providerId: string): Promise<compat.User> {\n    await exp.unlink(this._delegate, providerId);\n    return this;\n  }\n  updateEmail(newEmail: string): Promise<void> {\n    return exp.updateEmail(this._delegate, newEmail);\n  }\n  updatePassword(newPassword: string): Promise<void> {\n    return exp.updatePassword(this._delegate, newPassword);\n  }\n  updatePhoneNumber(phoneCredential: compat.AuthCredential): Promise<void> {\n    return exp.updatePhoneNumber(\n      this._delegate,\n      phoneCredential as exp.PhoneAuthCredential\n    );\n  }\n  updateProfile(profile: {\n    displayName?: string | null;\n    photoURL?: string | null;\n  }): Promise<void> {\n    return exp.updateProfile(this._delegate, profile);\n  }\n  verifyBeforeUpdateEmail(\n    newEmail: string,\n    actionCodeSettings?: compat.ActionCodeSettings | null\n  ): Promise<void> {\n    return exp.verifyBeforeUpdateEmail(\n      this._delegate,\n      newEmail,\n      actionCodeSettings\n    );\n  }\n  get emailVerified(): boolean {\n    return this._delegate.emailVerified;\n  }\n  get isAnonymous(): boolean {\n    return this._delegate.isAnonymous;\n  }\n  get metadata(): compat.UserMetadata {\n    return this._delegate.metadata;\n  }\n  get phoneNumber(): string | null {\n    return this._delegate.phoneNumber;\n  }\n  get providerData(): Array<compat.UserInfo | null> {\n    return this._delegate.providerData;\n  }\n  get refreshToken(): string {\n    return this._delegate.refreshToken;\n  }\n  get tenantId(): string | null {\n    return this._delegate.tenantId;\n  }\n  get displayName(): string | null {\n    return this._delegate.displayName;\n  }\n  get email(): string | null {\n    return this._delegate.email;\n  }\n  get photoURL(): string | null {\n    return this._delegate.photoURL;\n  }\n  get providerId(): string {\n    return this._delegate.providerId;\n  }\n  get uid(): string {\n    return this._delegate.uid;\n  }\n  private get auth(): exp.Auth {\n    return (this._delegate as exp.UserImpl).auth as unknown as exp.Auth;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport * as exp from '@firebase/auth/internal';\nimport * as compat from '@firebase/auth-types';\nimport { Provider } from '@firebase/component';\nimport { ErrorFn, Observer, Unsubscribe } from '@firebase/util';\n\nimport {\n  _validatePersistenceArgument,\n  Persistence,\n  _getPersistencesFromRedirect,\n  _savePersistenceForRedirect\n} from './persistence';\nimport { _isPopupRedirectSupported } from './platform';\nimport { CompatPopupRedirectResolver } from './popup_redirect';\nimport { User } from './user';\nimport {\n  convertConfirmationResult,\n  convertCredential\n} from './user_credential';\nimport { ReverseWrapper, Wrapper } from './wrap';\n\nconst _assert: typeof exp._assert = exp._assert;\n\nexport class Auth\n  implements compat.FirebaseAuth, Wrapper<exp.Auth>, _FirebaseService\n{\n  static Persistence = Persistence;\n  readonly _delegate: exp.AuthImpl;\n\n  constructor(readonly app: FirebaseApp, provider: Provider<'auth'>) {\n    if (provider.isInitialized()) {\n      this._delegate = provider.getImmediate() as exp.AuthImpl;\n      this.linkUnderlyingAuth();\n      return;\n    }\n\n    const { apiKey } = app.options;\n    // TODO: platform needs to be determined using heuristics\n    _assert(apiKey, exp.AuthErrorCode.INVALID_API_KEY, {\n      appName: app.name\n    });\n\n    // TODO: platform needs to be determined using heuristics\n    _assert(apiKey, exp.AuthErrorCode.INVALID_API_KEY, {\n      appName: app.name\n    });\n\n    // Only use a popup/redirect resolver in browser environments\n    const resolver =\n      typeof window !== 'undefined' ? CompatPopupRedirectResolver : undefined;\n    this._delegate = provider.initialize({\n      options: {\n        persistence: buildPersistenceHierarchy(apiKey, app.name),\n        popupRedirectResolver: resolver\n      }\n    }) as exp.AuthImpl;\n\n    this._delegate._updateErrorMap(exp.debugErrorMap);\n    this.linkUnderlyingAuth();\n  }\n\n  get emulatorConfig(): compat.EmulatorConfig | null {\n    return this._delegate.emulatorConfig;\n  }\n\n  get currentUser(): compat.User | null {\n    if (!this._delegate.currentUser) {\n      return null;\n    }\n\n    return User.getOrCreate(this._delegate.currentUser);\n  }\n  get languageCode(): string | null {\n    return this._delegate.languageCode;\n  }\n  set languageCode(languageCode: string | null) {\n    this._delegate.languageCode = languageCode;\n  }\n  get settings(): compat.AuthSettings {\n    return this._delegate.settings;\n  }\n  get tenantId(): string | null {\n    return this._delegate.tenantId;\n  }\n  set tenantId(tid: string | null) {\n    this._delegate.tenantId = tid;\n  }\n  useDeviceLanguage(): void {\n    this._delegate.useDeviceLanguage();\n  }\n  signOut(): Promise<void> {\n    return this._delegate.signOut();\n  }\n  useEmulator(url: string, options?: { disableWarnings: boolean }): void {\n    exp.connectAuthEmulator(this._delegate, url, options);\n  }\n  applyActionCode(code: string): Promise<void> {\n    return exp.applyActionCode(this._delegate, code);\n  }\n\n  checkActionCode(code: string): Promise<compat.ActionCodeInfo> {\n    return exp.checkActionCode(this._delegate, code);\n  }\n\n  confirmPasswordReset(code: string, newPassword: string): Promise<void> {\n    return exp.confirmPasswordReset(this._delegate, code, newPassword);\n  }\n\n  async createUserWithEmailAndPassword(\n    email: string,\n    password: string\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this._delegate,\n      exp.createUserWithEmailAndPassword(this._delegate, email, password)\n    );\n  }\n  fetchProvidersForEmail(email: string): Promise<string[]> {\n    return this.fetchSignInMethodsForEmail(email);\n  }\n  fetchSignInMethodsForEmail(email: string): Promise<string[]> {\n    return exp.fetchSignInMethodsForEmail(this._delegate, email);\n  }\n  isSignInWithEmailLink(emailLink: string): boolean {\n    return exp.isSignInWithEmailLink(this._delegate, emailLink);\n  }\n  async getRedirectResult(): Promise<compat.UserCredential> {\n    _assert(\n      _isPopupRedirectSupported(),\n      this._delegate,\n      exp.AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n    const credential = await exp.getRedirectResult(\n      this._delegate,\n      CompatPopupRedirectResolver\n    );\n    if (!credential) {\n      return {\n        credential: null,\n        user: null\n      };\n    }\n    return convertCredential(this._delegate, Promise.resolve(credential));\n  }\n\n  // This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n  // It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it\n  // out of autogenerated documentation pages to reduce accidental misuse.\n  addFrameworkForLogging(framework: string): void {\n    exp.addFrameworkForLogging(this._delegate, framework);\n  }\n\n  onAuthStateChanged(\n    nextOrObserver: Observer<unknown> | ((a: compat.User | null) => unknown),\n    errorFn?: (error: compat.Error) => unknown,\n    completed?: Unsubscribe\n  ): Unsubscribe {\n    const { next, error, complete } = wrapObservers(\n      nextOrObserver,\n      errorFn,\n      completed\n    );\n    return this._delegate.onAuthStateChanged(next!, error, complete);\n  }\n  onIdTokenChanged(\n    nextOrObserver: Observer<unknown> | ((a: compat.User | null) => unknown),\n    errorFn?: (error: compat.Error) => unknown,\n    completed?: Unsubscribe\n  ): Unsubscribe {\n    const { next, error, complete } = wrapObservers(\n      nextOrObserver,\n      errorFn,\n      completed\n    );\n    return this._delegate.onIdTokenChanged(next!, error, complete);\n  }\n  sendSignInLinkToEmail(\n    email: string,\n    actionCodeSettings: compat.ActionCodeSettings\n  ): Promise<void> {\n    return exp.sendSignInLinkToEmail(this._delegate, email, actionCodeSettings);\n  }\n  sendPasswordResetEmail(\n    email: string,\n    actionCodeSettings?: compat.ActionCodeSettings | null\n  ): Promise<void> {\n    return exp.sendPasswordResetEmail(\n      this._delegate,\n      email,\n      actionCodeSettings || undefined\n    );\n  }\n  async setPersistence(persistence: string): Promise<void> {\n    _validatePersistenceArgument(this._delegate, persistence);\n    let converted;\n    switch (persistence) {\n      case Persistence.SESSION:\n        converted = exp.browserSessionPersistence;\n        break;\n      case Persistence.LOCAL:\n        // Not using isIndexedDBAvailable() since it only checks if indexedDB is defined.\n        const isIndexedDBFullySupported = await exp\n          ._getInstance<exp.PersistenceInternal>(exp.indexedDBLocalPersistence)\n          ._isAvailable();\n        converted = isIndexedDBFullySupported\n          ? exp.indexedDBLocalPersistence\n          : exp.browserLocalPersistence;\n        break;\n      case Persistence.NONE:\n        converted = exp.inMemoryPersistence;\n        break;\n      default:\n        return exp._fail(exp.AuthErrorCode.ARGUMENT_ERROR, {\n          appName: this._delegate.name\n        });\n    }\n\n    return this._delegate.setPersistence(converted);\n  }\n\n  signInAndRetrieveDataWithCredential(\n    credential: compat.AuthCredential\n  ): Promise<compat.UserCredential> {\n    return this.signInWithCredential(credential);\n  }\n  signInAnonymously(): Promise<compat.UserCredential> {\n    return convertCredential(\n      this._delegate,\n      exp.signInAnonymously(this._delegate)\n    );\n  }\n  signInWithCredential(\n    credential: compat.AuthCredential\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this._delegate,\n      exp.signInWithCredential(this._delegate, credential as exp.AuthCredential)\n    );\n  }\n  signInWithCustomToken(token: string): Promise<compat.UserCredential> {\n    return convertCredential(\n      this._delegate,\n      exp.signInWithCustomToken(this._delegate, token)\n    );\n  }\n  signInWithEmailAndPassword(\n    email: string,\n    password: string\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this._delegate,\n      exp.signInWithEmailAndPassword(this._delegate, email, password)\n    );\n  }\n  signInWithEmailLink(\n    email: string,\n    emailLink?: string\n  ): Promise<compat.UserCredential> {\n    return convertCredential(\n      this._delegate,\n      exp.signInWithEmailLink(this._delegate, email, emailLink)\n    );\n  }\n  signInWithPhoneNumber(\n    phoneNumber: string,\n    applicationVerifier: compat.ApplicationVerifier\n  ): Promise<compat.ConfirmationResult> {\n    return convertConfirmationResult(\n      this._delegate,\n      exp.signInWithPhoneNumber(\n        this._delegate,\n        phoneNumber,\n        applicationVerifier\n      )\n    );\n  }\n  async signInWithPopup(\n    provider: compat.AuthProvider\n  ): Promise<compat.UserCredential> {\n    _assert(\n      _isPopupRedirectSupported(),\n      this._delegate,\n      exp.AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n    return convertCredential(\n      this._delegate,\n      exp.signInWithPopup(\n        this._delegate,\n        provider as exp.AuthProvider,\n        CompatPopupRedirectResolver\n      )\n    );\n  }\n  async signInWithRedirect(provider: compat.AuthProvider): Promise<void> {\n    _assert(\n      _isPopupRedirectSupported(),\n      this._delegate,\n      exp.AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n\n    await _savePersistenceForRedirect(this._delegate);\n    return exp.signInWithRedirect(\n      this._delegate,\n      provider as exp.AuthProvider,\n      CompatPopupRedirectResolver\n    );\n  }\n  updateCurrentUser(user: compat.User | null): Promise<void> {\n    // remove ts-ignore once overloads are defined for exp functions to accept compat objects\n    // @ts-ignore\n    return this._delegate.updateCurrentUser(user);\n  }\n  verifyPasswordResetCode(code: string): Promise<string> {\n    return exp.verifyPasswordResetCode(this._delegate, code);\n  }\n  unwrap(): exp.Auth {\n    return this._delegate;\n  }\n  _delete(): Promise<void> {\n    return this._delegate._delete();\n  }\n  private linkUnderlyingAuth(): void {\n    (this._delegate as unknown as ReverseWrapper<Auth>).wrapped = () => this;\n  }\n}\n\nfunction wrapObservers(\n  nextOrObserver: Observer<unknown> | ((a: compat.User | null) => unknown),\n  error?: (error: compat.Error) => unknown,\n  complete?: Unsubscribe\n): Partial<Observer<exp.User | null>> {\n  let next = nextOrObserver;\n  if (typeof nextOrObserver !== 'function') {\n    ({ next, error, complete } = nextOrObserver);\n  }\n\n  // We know 'next' is now a function\n  const oldNext = next as (a: compat.User | null) => unknown;\n\n  const newNext = (user: exp.User | null): unknown =>\n    oldNext(user && User.getOrCreate(user as exp.User));\n  return {\n    next: newNext,\n    error: error as ErrorFn,\n    complete\n  };\n}\n\nfunction buildPersistenceHierarchy(\n  apiKey: string,\n  appName: string\n): exp.Persistence[] {\n  // Note this is slightly different behavior: in this case, the stored\n  // persistence is checked *first* rather than last. This is because we want\n  // to prefer stored persistence type in the hierarchy. This is an empty\n  // array if window is not available or there is no pending redirect\n  const persistences = _getPersistencesFromRedirect(apiKey, appName);\n\n  // If \"self\" is available, add indexedDB\n  if (\n    typeof self !== 'undefined' &&\n    !persistences.includes(exp.indexedDBLocalPersistence)\n  ) {\n    persistences.push(exp.indexedDBLocalPersistence);\n  }\n\n  // If \"window\" is available, add HTML Storage persistences\n  if (typeof window !== 'undefined') {\n    for (const persistence of [\n      exp.browserLocalPersistence,\n      exp.browserSessionPersistence\n    ]) {\n      if (!persistences.includes(persistence)) {\n        persistences.push(persistence);\n      }\n    }\n  }\n\n  // Add in-memory as a final fallback\n  if (!persistences.includes(exp.inMemoryPersistence)) {\n    persistences.push(exp.inMemoryPersistence);\n  }\n\n  return persistences;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as exp from '@firebase/auth/internal';\nimport * as compat from '@firebase/auth-types';\nimport firebase from '@firebase/app-compat';\nimport { Compat } from '@firebase/util';\nimport { unwrap } from './wrap';\n\nexport class PhoneAuthProvider\n  implements compat.PhoneAuthProvider, Compat<exp.PhoneAuthProvider>\n{\n  providerId = 'phone';\n  readonly _delegate: exp.PhoneAuthProvider;\n\n  static PHONE_SIGN_IN_METHOD = exp.PhoneAuthProvider.PHONE_SIGN_IN_METHOD;\n  static PROVIDER_ID = exp.PhoneAuthProvider.PROVIDER_ID;\n\n  static credential(\n    verificationId: string,\n    verificationCode: string\n  ): compat.AuthCredential {\n    return exp.PhoneAuthProvider.credential(verificationId, verificationCode);\n  }\n\n  constructor() {\n    // TODO: remove ts-ignore when moving types from auth-types to auth-compat\n    // @ts-ignore\n    this._delegate = new exp.PhoneAuthProvider(unwrap(firebase.auth!()));\n  }\n\n  verifyPhoneNumber(\n    phoneInfoOptions:\n      | string\n      | compat.PhoneSingleFactorInfoOptions\n      | compat.PhoneMultiFactorEnrollInfoOptions\n      | compat.PhoneMultiFactorSignInInfoOptions,\n    applicationVerifier: compat.ApplicationVerifier\n  ): Promise<string> {\n    return this._delegate.verifyPhoneNumber(\n      // The implementation matches but the types are subtly incompatible\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      phoneInfoOptions as any,\n      applicationVerifier\n    );\n  }\n\n  unwrap(): exp.PhoneAuthProvider {\n    return this._delegate;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { FirebaseApp } from '@firebase/app-compat';\nimport * as exp from '@firebase/auth/internal';\nimport * as compat from '@firebase/auth-types';\nimport { Compat } from '@firebase/util';\n\nconst _assert: typeof exp._assert = exp._assert;\n\nexport class RecaptchaVerifier\n  implements compat.RecaptchaVerifier, Compat<exp.ApplicationVerifier>\n{\n  readonly _delegate: exp.RecaptchaVerifier;\n  type: string;\n  constructor(\n    container: HTMLElement | string,\n    parameters?: object | null,\n    app: FirebaseApp = firebase.app()\n  ) {\n    // API key is required for web client RPC calls.\n    _assert(app.options?.apiKey, exp.AuthErrorCode.INVALID_API_KEY, {\n      appName: app.name\n    });\n    this._delegate = new exp.RecaptchaVerifier(\n      // TODO: remove ts-ignore when moving types from auth-types to auth-compat\n      // @ts-ignore\n      app.auth!(),\n      container,\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      parameters as any\n    );\n    this.type = this._delegate.type;\n  }\n  clear(): void {\n    this._delegate.clear();\n  }\n  render(): Promise<number> {\n    return this._delegate.render();\n  }\n  verify(): Promise<string> {\n    return this._delegate.verify();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable camelcase */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport * as impl from '@firebase/auth/internal';\nimport {\n  Component,\n  ComponentType,\n  InstantiationMode\n} from '@firebase/component';\nimport { FirebaseError } from '@firebase/util';\n\nimport * as types from '@firebase/auth-types';\nimport { name, version } from './package.json';\nimport { Auth } from './src/auth';\nimport { PhoneAuthProvider as CompatAuthProvider } from './src/phone_auth_provider';\nimport { RecaptchaVerifier as CompatRecaptchaVerifier } from './src/recaptcha_verifier';\n\nconst AUTH_TYPE = 'auth-compat';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'auth-compat': types.FirebaseAuth;\n  }\n}\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    auth: {\n      (app?: FirebaseApp): types.FirebaseAuth;\n      Auth: typeof types.FirebaseAuth;\n      EmailAuthProvider: typeof types.EmailAuthProvider;\n      EmailAuthProvider_Instance: typeof types.EmailAuthProvider_Instance;\n      FacebookAuthProvider: typeof types.FacebookAuthProvider;\n      FacebookAuthProvider_Instance: typeof types.FacebookAuthProvider_Instance;\n      GithubAuthProvider: typeof types.GithubAuthProvider;\n      GithubAuthProvider_Instance: typeof types.GithubAuthProvider_Instance;\n      GoogleAuthProvider: typeof types.GoogleAuthProvider;\n      GoogleAuthProvider_Instance: typeof types.GoogleAuthProvider_Instance;\n      OAuthProvider: typeof types.OAuthProvider;\n      SAMLAuthProvider: typeof types.SAMLAuthProvider;\n      PhoneAuthProvider: typeof types.PhoneAuthProvider;\n      PhoneAuthProvider_Instance: typeof types.PhoneAuthProvider_Instance;\n      PhoneMultiFactorGenerator: typeof types.PhoneMultiFactorGenerator;\n      RecaptchaVerifier: typeof types.RecaptchaVerifier;\n      RecaptchaVerifier_Instance: typeof types.RecaptchaVerifier_Instance;\n      TwitterAuthProvider: typeof types.TwitterAuthProvider;\n      TwitterAuthProvider_Instance: typeof types.TwitterAuthProvider_Instance;\n    };\n  }\n  interface FirebaseApp {\n    auth?(): types.FirebaseAuth;\n  }\n}\n\n// Create auth components to register with firebase.\n// Provides Auth public APIs.\nfunction registerAuthCompat(instance: _FirebaseNamespace): void {\n  instance.INTERNAL.registerComponent(\n    new Component(\n      AUTH_TYPE,\n      container => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app-compat').getImmediate();\n        const authProvider = container.getProvider('auth');\n        return new Auth(app, authProvider);\n      },\n      ComponentType.PUBLIC\n    )\n      .setServiceProps({\n        ActionCodeInfo: {\n          Operation: {\n            EMAIL_SIGNIN: impl.ActionCodeOperation.EMAIL_SIGNIN,\n            PASSWORD_RESET: impl.ActionCodeOperation.PASSWORD_RESET,\n            RECOVER_EMAIL: impl.ActionCodeOperation.RECOVER_EMAIL,\n            REVERT_SECOND_FACTOR_ADDITION:\n              impl.ActionCodeOperation.REVERT_SECOND_FACTOR_ADDITION,\n            VERIFY_AND_CHANGE_EMAIL:\n              impl.ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL,\n            VERIFY_EMAIL: impl.ActionCodeOperation.VERIFY_EMAIL\n          }\n        },\n        EmailAuthProvider: impl.EmailAuthProvider,\n        FacebookAuthProvider: impl.FacebookAuthProvider,\n        GithubAuthProvider: impl.GithubAuthProvider,\n        GoogleAuthProvider: impl.GoogleAuthProvider,\n        OAuthProvider: impl.OAuthProvider,\n        SAMLAuthProvider: impl.SAMLAuthProvider,\n        PhoneAuthProvider: CompatAuthProvider,\n        PhoneMultiFactorGenerator: impl.PhoneMultiFactorGenerator,\n        RecaptchaVerifier: CompatRecaptchaVerifier,\n        TwitterAuthProvider: impl.TwitterAuthProvider,\n        Auth,\n        AuthCredential: impl.AuthCredential,\n        Error: FirebaseError\n      })\n      .setInstantiationMode(InstantiationMode.LAZY)\n      .setMultipleInstances(false)\n  );\n\n  instance.registerVersion(name, version);\n}\n\nregisterAuthCompat(firebase as _FirebaseNamespace);\n"], "names": ["impl", "_assert", "CompatAuthProvider", "CompatRecaptchaVerifier"], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAkBH,MAAM,gCAAgC,GAAG,IAAI,CAAC;AAE9C,SAAS,iBAAiB,GAAA;AACxB,IAAA,OAAO,IAAI,EAAE,QAAQ,EAAE,QAAQ,IAAI,IAAI,CAAC;AAC1C,CAAC;AAED;;AAEG;AACH,SAAS,cAAc,GAAA;IACrB,OAAO,iBAAiB,EAAE,KAAK,OAAO,IAAI,iBAAiB,EAAE,KAAK,QAAQ,CAAC;AAC7E,CAAC;AAED;;;;AAIG;AACa,SAAA,4BAA4B,CAAC,EAAA,GAAa,KAAK,EAAE,EAAA;AAC/D,IAAA,OAAO,CAAC,EACN,CAAC,iBAAiB,EAAE,KAAK,OAAO;QAC9B,iBAAiB,EAAE,KAAK,QAAQ;QAChC,iBAAiB,EAAE,KAAK,YAAY;QACtC,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,0BAA0B,CAAC,CACnD,CAAC;AACJ,CAAC;AAED;;;AAGG;AACH,SAAS,oBAAoB,GAAA;AAC3B,IAAA,OAAO,aAAa,EAAE,IAAI,MAAM,EAAE,CAAC;AACrC,CAAC;AAED;;;AAGG;AACH,SAAS,OAAO,GAAA;IACd,OAAO,IAAI,EAAE,IAAI,QAAQ,EAAE,YAAY,KAAK,EAAE,CAAC;AACjD,CAAC;AAED;;;;AAIG;AACH,SAAS,OAAO,CAAC,EAAa,GAAA,KAAK,EAAE,EAAA;AACnC,IAAA,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC;AAED;;;;AAIG;AACH,SAAS,8BAA8B,CAAC,EAAa,GAAA,KAAK,EAAE,EAAA;AAC1D,IAAA,OAAO,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC;AAClC,CAAC;AAED;SACgB,sBAAsB,GAAA;AACpC,IAAA,IAAI;AACF,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;AAClC,QAAA,MAAM,GAAG,GAAGA,GAAI,CAAC,gBAAgB,EAAE,CAAC;QACpC,IAAI,OAAO,EAAE;;;YAGX,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC7B,YAAA,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;;;;;;YAM3B,IAAI,8BAA8B,EAAE,EAAE;;;gBAGpC,OAAO,oBAAoB,EAAE,CAAC;aAC/B;AACD,YAAA,OAAO,IAAI,CAAC;SACb;KACF;IAAC,OAAO,CAAC,EAAE;;;AAGV,QAAA,OAAO,SAAS,EAAE,IAAI,oBAAoB,EAAE,CAAC;KAC9C;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;AAGG;SACa,SAAS,GAAA;;AAEvB,IAAA,QACE,OAAO,MAAM,KAAK,WAAW;AAC7B,QAAA,mBAAmB,IAAI,MAAM;QAC7B,eAAe,IAAI,MAAM,EACzB;AACJ,CAAC;SAEe,yBAAyB,GAAA;IACvC,QACE,CAAC,cAAc,EAAE;AACf,QAAA,kBAAkB,EAAE;AACpB,QAAA,4BAA4B,EAAE;;;AAGhC,QAAA,CAAC,oBAAoB,EAAE;;;AAGvB,QAAA,sBAAsB,EAAE;;QAExB,CAAC,SAAS,EAAE,EACZ;AACJ,CAAC;AAED;SACgB,gBAAgB,GAAA;AAC9B,IAAA,OAAO,4BAA4B,EAAE,IAAI,OAAO,QAAQ,KAAK,WAAW,CAAC;AAC3E,CAAC;AAEM,eAAe,UAAU,GAAA;AAC9B,IAAA,IAAI,CAAC,gBAAgB,EAAE,EAAE;AACvB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;AAC3B,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAK;;YAEhC,OAAO,CAAC,KAAK,CAAC,CAAC;SAChB,EAAE,gCAAgC,CAAC,CAAC;AAErC,QAAA,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,MAAK;YAC5C,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;SAEe,cAAc,GAAA;AAC5B,IAAA,OAAO,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC;AACvD;;AClLA;;;;;;;;;;;;;;;AAeG;AAMI,MAAM,WAAW,GAAG;AACzB,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,OAAO,EAAE,SAAS;CACnB,CAAC;AAEF,MAAMC,SAAO,GAAuB,GAAG,CAAC,OAAO,CAAC;AAEhD,MAAM,eAAe,GAAG,aAAa,CAAC;AAEtC;;;AAGG;AACa,SAAA,4BAA4B,CAC1C,IAAc,EACd,WAAmB,EAAA;AAEnB,IAAAA,SAAO,CACL,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAChD,IAAI,yEAEL,CAAC;;IAEF,IAAI,aAAa,EAAE,EAAE;;QAEnBA,SAAO,CACL,WAAW,KAAK,WAAW,CAAC,OAAO,EACnC,IAAI,EAAA,8BAAA,iDAEL,CAAC;QACF,OAAO;KACR;IACD,IAAI,MAAM,EAAE,EAAE;;QAEZA,SAAO,CACL,WAAW,KAAK,WAAW,CAAC,IAAI,EAChC,IAAI,EAAA,8BAAA,iDAEL,CAAC;QACF,OAAO;KACR;IACD,IAAI,SAAS,EAAE,EAAE;;;AAGf,QAAAA,SAAO,CACL,WAAW,KAAK,WAAW,CAAC,IAAI;AAC9B,aAAC,WAAW,KAAK,WAAW,CAAC,KAAK,IAAI,oBAAoB,EAAE,CAAC,EAC/D,IAAI,EAAA,8BAAA,iDAEL,CAAC;QACF,OAAO;KACR;;AAED,IAAAA,SAAO,CACL,WAAW,KAAK,WAAW,CAAC,IAAI,IAAI,sBAAsB,EAAE,EAC5D,IAAI,EAAA,8BAAA,iDAEL,CAAC;AACJ,CAAC;AAEM,eAAe,2BAA2B,CAC/C,IAAsB,EAAA;IAEtB,MAAM,IAAI,CAAC,sBAAsB,CAAC;AAClC,IAAA,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;AAC/C,IAAA,MAAM,GAAG,GAAG,GAAG,CAAC,mBAAmB,CACjC,eAAe,EACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB,IAAI,CAAC,IAAI,CACV,CAAC;IACF,IAAI,OAAO,EAAE;QACX,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;KAClD;AACH,CAAC;AAEe,SAAA,4BAA4B,CAC1C,MAAc,EACd,OAAe,EAAA;AAEf,IAAA,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;IAC/C,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,OAAO,EAAE,CAAC;KACX;AAED,IAAA,MAAM,GAAG,GAAG,GAAG,CAAC,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACtE,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAEzC,QAAQ,WAAW;QACjB,KAAK,WAAW,CAAC,IAAI;AACnB,YAAA,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACnC,KAAK,WAAW,CAAC,KAAK;YACpB,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACxE,KAAK,WAAW,CAAC,OAAO;AACtB,YAAA,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;AACzC,QAAA;AACE,YAAA,OAAO,EAAE,CAAC;KACb;AACH,CAAC;AAED;AACA,SAAS,4BAA4B,GAAA;AACnC,IAAA,IAAI;AACF,QAAA,OAAO,cAAc,EAAE,EAAE,cAAc,IAAI,IAAI,CAAC;KACjD;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,IAAI,CAAC;KACb;AACH;;AChIA;;;;;;;;;;;;;;;AAeG;AAKH,MAAMA,SAAO,GAAuB,GAAG,CAAC,OAAO,CAAC;AAEhD;MACa,2BAA2B,CAAA;AAAxC,IAAA,WAAA,GAAA;;QAImB,IAAe,CAAA,eAAA,GAC9B,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QACpC,IAAe,CAAA,eAAA,GAC9B,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;;QAE7C,IAAkB,CAAA,kBAAA,GAA6C,IAAI,CAAC;AAC5E,QAAA,IAAA,CAAA,oBAAoB,GAAG,GAAG,CAAC,yBAAyB,CAAC;AAErD,QAAA,IAAA,CAAA,mBAAmB,GAIuB,GAAG,CAAC,kBAAkB,CAAC;AACjE,QAAA,IAAA,CAAA,uBAAuB,GAAG,GAAG,CAAC,uBAAuB,CAAC;KAqEvD;IAnEC,MAAM,WAAW,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;KAC1D;IAED,MAAM,UAAU,CACd,IAAkB,EAClB,QAA0B,EAC1B,QAA2B,EAC3B,OAAgB,EAAA;AAEhB,QAAA,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACtC,QAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAC/C,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,OAAO,CACR,CAAC;KACH;IAED,MAAM,aAAa,CACjB,IAAkB,EAClB,QAA0B,EAC1B,QAA2B,EAC3B,OAAgB,EAAA;AAEhB,QAAA,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACtC,QAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAClD,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,OAAO,CACR,CAAC;KACH;IAED,4BAA4B,CAC1B,IAAkB,EAClB,EAAiC,EAAA;QAEjC,IAAI,CAAC,0BAA0B,CAAC,4BAA4B,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACxE;AAED,IAAA,iBAAiB,CAAC,IAAc,EAAA;QAC9B,OAAO,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAChE;AAED,IAAA,IAAI,sBAAsB,GAAA;QACxB,OAAO,gBAAgB,EAAE,IAAI,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC;KAC1E;AAED,IAAA,IAAY,0BAA0B,GAAA;AACpC,QAAAA,SAAO,CAAC,IAAI,CAAC,kBAAkB,0DAAmC,CAAC;QACnE,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAChC;AAEO,IAAA,MAAM,wBAAwB,GAAA;AACpC,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO;SACR;;;AAID,QAAA,MAAM,SAAS,GAAG,MAAM,UAAU,EAAE,CAAC;QACrC,IAAI,CAAC,kBAAkB,GAAG,SAAS;cAC/B,IAAI,CAAC,eAAe;AACtB,cAAE,IAAI,CAAC,eAAe,CAAC;KAC1B;AACF;;AC7GD;;;;;;;;;;;;;;;AAeG;AAYG,SAAU,MAAM,CAAI,MAAe,EAAA;AACvC,IAAA,OAAQ,MAAqB,CAAC,MAAM,EAAE,CAAC;AACzC,CAAC;AAEK,SAAU,OAAO,CAAI,MAAe,EAAA;AACxC,IAAA,OAAQ,MAA4B,CAAC,OAAO,EAAE,CAAC;AACjD;;ACjCA;;;;;;;;;;;;;;;AAeG;AASH,SAAS,sBAAsB,CAC7B,cAA0C,EAAA;AAE1C,IAAA,OAAO,oBAAoB,CAAC,cAAc,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAc,EAAE,CAAgB,EAAA;;;AAG9D,IAAA,MAAM,QAAQ,GAAI,CAAC,CAAC,UAAsD;AACxE,UAAE,cAAmD,CAAC;AACxD,IAAA,IAAK,CAAmB,EAAE,IAAI,KAAK,iCAAiC,EAAE;QACpE,MAAM,MAAM,GAAG,CAA4B,CAAC;AAC5C,QAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,mBAAmB,CACvC,IAAI,EACJ,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAyB,CAAC,CAC5D,CAAC;KACH;SAAM,IAAI,QAAQ,EAAE;AACnB,QAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,CAAqB,CAAC;QACtC,IAAI,UAAU,EAAE;AACd,YAAA,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;YAChC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,SAAS,CAAC;YAClD,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,SAAS,CAAC;YAC5C,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,IAAI,SAAS,CAAC;SACzD;KACF;AACH,CAAC;AAED,SAAS,oBAAoB,CAC3B,MAA0C,EAAA;AAE1C,IAAA,MAAM,EAAE,cAAc,EAAE,IACtB,MAAM,YAAY,aAAa,GAAG,MAAM,CAAC,UAAU,GAAG,MAAM,CAC9B,CAAC;IACjC,IAAI,CAAC,cAAc,EAAE;AACnB,QAAA,OAAO,IAAI,CAAC;KACb;;;;AAKD,IAAA,IAAI,EAAE,MAAM,YAAY,aAAa,CAAC,EAAE;QACtC,IAAI,gBAAgB,IAAI,cAAc,IAAI,aAAa,IAAI,cAAc,EAAE;YACzE,OAAO,GAAG,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SAC3D;KACF;AAED,IAAA,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;;;IAI7C,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE;AACzD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,IAAI,QAGH,CAAC;IACF,QAAQ,UAAU;AAChB,QAAA,KAAK,GAAG,CAAC,UAAU,CAAC,MAAM;AACxB,YAAA,QAAQ,GAAG,GAAG,CAAC,kBAAkB,CAAC;YAClC,MAAM;AACR,QAAA,KAAK,GAAG,CAAC,UAAU,CAAC,QAAQ;AAC1B,YAAA,QAAQ,GAAG,GAAG,CAAC,oBAAoB,CAAC;YACpC,MAAM;AACR,QAAA,KAAK,GAAG,CAAC,UAAU,CAAC,MAAM;AACxB,YAAA,QAAQ,GAAG,GAAG,CAAC,kBAAkB,CAAC;YAClC,MAAM;AACR,QAAA,KAAK,GAAG,CAAC,UAAU,CAAC,OAAO;AACzB,YAAA,QAAQ,GAAG,GAAG,CAAC,mBAAmB,CAAC;YACnC,MAAM;AACR,QAAA;AACE,YAAA,MAAM,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,KAAK,EACN,GAAG,cAA2C,CAAC;AAChD,YAAA,IACE,CAAC,gBAAgB;AACjB,gBAAA,CAAC,gBAAgB;AACjB,gBAAA,CAAC,YAAY;gBACb,CAAC,YAAY,EACb;AACA,gBAAA,OAAO,IAAI,CAAC;aACb;;YAED,IAAI,YAAY,EAAE;AAChB,gBAAA,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;oBAClC,OAAO,GAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;iBACjE;qBAAM;;AAEL,oBAAA,OAAO,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC;wBACrC,UAAU;AACV,wBAAA,YAAY,EAAE,UAAU;wBACxB,YAAY;AACZ,wBAAA,OAAO,EAAE,YAAY;AACrB,wBAAA,WAAW,EAAE,gBAAgB;AAC9B,qBAAA,CAAC,CAAC;iBACJ;aACF;YACD,OAAO,IAAI,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC;AAClD,gBAAA,OAAO,EAAE,YAAY;AACrB,gBAAA,WAAW,EAAE,gBAAgB;AAC7B,gBAAA,QAAQ,EAAE,KAAK;AAChB,aAAA,CAAC,CAAC;KACN;IAED,OAAO,MAAM,YAAY,aAAa;AACpC,UAAE,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC;AACtC,UAAE,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;AAEe,SAAA,iBAAiB,CAC/B,IAAc,EACd,iBAA8C,EAAA;AAE9C,IAAA,OAAO,iBAAiB;SACrB,KAAK,CAAC,CAAC,IAAG;AACT,QAAA,IAAI,CAAC,YAAY,aAAa,EAAE;AAC9B,YAAA,sBAAsB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SACjC;AACD,QAAA,MAAM,CAAC,CAAC;AACV,KAAC,CAAC;SACD,IAAI,CAAC,UAAU,IAAG;AACjB,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;AAC/C,QAAA,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,OAAO;YACL,aAAa;AACb,YAAA,UAAU,EAAE,sBAAsB,CAChC,UAAwC,CACzC;AACD,YAAA,kBAAkB,EAAE,GAAG,CAAC,qBAAqB,CAC3C,UAAgC,CACjC;AACD,YAAA,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;SAC7B,CAAC;AACJ,KAAC,CAAC,CAAC;AACP,CAAC;AAEM,eAAe,yBAAyB,CAC7C,IAAc,EACd,yBAA0D,EAAA;AAE1D,IAAA,MAAM,qBAAqB,GAAG,MAAM,yBAAyB,CAAC;IAC9D,OAAO;QACL,cAAc,EAAE,qBAAqB,CAAC,cAAc;AACpD,QAAA,OAAO,EAAE,CAAC,gBAAwB,KAChC,iBAAiB,CAAC,IAAI,EAAE,qBAAqB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;KAC3E,CAAC;AACJ,CAAC;AAED,MAAM,mBAAmB,CAAA;IAEvB,WACE,CAAA,IAAc,EACG,QAAiC,EAAA;QAAjC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAyB;AAElD,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;KAC3B;AAED,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;KAC9B;AAED,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;KAC5B;AAED,IAAA,aAAa,CACX,SAAsC,EAAA;AAEtC,QAAA,OAAO,iBAAiB,CACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EACjB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAqC,CAAC,CACnE,CAAC;KACH;AACF;;AC7MD;;;;;;;;;;;;;;;AAeG;MAYU,IAAI,CAAA;AAOf,IAAA,WAAA,CAA6B,SAAmB,EAAA;QAAnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QAC9C,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;KAC/C;IAED,OAAO,WAAW,CAAC,IAAc,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC5B,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACzC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;KACjC;IAED,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;IACD,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;IACD,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;AACD,IAAA,gBAAgB,CAAC,YAAsB,EAAA;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;KACtD;AACD,IAAA,UAAU,CAAC,YAAsB,EAAA;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;KAChD;AACD,IAAA,iCAAiC,CAC/B,UAAiC,EAAA;AAEjC,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;KAC5C;IACD,MAAM,kBAAkB,CACtB,UAAiC,EAAA;AAEjC,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,IAAI,EACT,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,UAAgC,CAAC,CACzE,CAAC;KACH;AACD,IAAA,MAAM,mBAAmB,CACvB,WAAmB,EACnB,mBAA+C,EAAA;AAE/C,QAAA,OAAO,yBAAyB,CAC9B,IAAI,CAAC,IAAI,EACT,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAC1E,CAAC;KACH;IACD,MAAM,aAAa,CACjB,QAA6B,EAAA;AAE7B,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,IAAI,EACT,GAAG,CAAC,aAAa,CACf,IAAI,CAAC,SAAS,EACd,QAA4B,EAC5B,2BAA2B,CAC5B,CACF,CAAC;KACH;IACD,MAAM,gBAAgB,CAAC,QAA6B,EAAA;QAClD,MAAM,2BAA2B,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D,QAAA,OAAO,GAAG,CAAC,gBAAgB,CACzB,IAAI,CAAC,SAAS,EACd,QAA4B,EAC5B,2BAA2B,CAC5B,CAAC;KACH;AACD,IAAA,2CAA2C,CACzC,UAAiC,EAAA;AAEjC,QAAA,OAAO,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;KACtD;IACD,MAAM,4BAA4B,CAChC,UAAiC,EAAA;AAEjC,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,IAA2B,EAChC,GAAG,CAAC,4BAA4B,CAC9B,IAAI,CAAC,SAAS,EACd,UAAgC,CACjC,CACF,CAAC;KACH;IACD,6BAA6B,CAC3B,WAAmB,EACnB,mBAA+C,EAAA;AAE/C,QAAA,OAAO,yBAAyB,CAC9B,IAAI,CAAC,IAAI,EACT,GAAG,CAAC,6BAA6B,CAC/B,IAAI,CAAC,SAAS,EACd,WAAW,EACX,mBAAmB,CACpB,CACF,CAAC;KACH;AACD,IAAA,uBAAuB,CACrB,QAA6B,EAAA;AAE7B,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,IAAI,EACT,GAAG,CAAC,uBAAuB,CACzB,IAAI,CAAC,SAAS,EACd,QAA4B,EAC5B,2BAA2B,CAC5B,CACF,CAAC;KACH;IACD,MAAM,0BAA0B,CAC9B,QAA6B,EAAA;QAE7B,MAAM,2BAA2B,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D,QAAA,OAAO,GAAG,CAAC,0BAA0B,CACnC,IAAI,CAAC,SAAS,EACd,QAA4B,EAC5B,2BAA2B,CAC5B,CAAC;KACH;AACD,IAAA,qBAAqB,CACnB,kBAAqD,EAAA;QAErD,OAAO,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;KACtE;IACD,MAAM,MAAM,CAAC,UAAkB,EAAA;QAC7B,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AAC7C,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,WAAW,CAAC,QAAgB,EAAA;QAC1B,OAAO,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KAClD;AACD,IAAA,cAAc,CAAC,WAAmB,EAAA;QAChC,OAAO,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;KACxD;AACD,IAAA,iBAAiB,CAAC,eAAsC,EAAA;QACtD,OAAO,GAAG,CAAC,iBAAiB,CAC1B,IAAI,CAAC,SAAS,EACd,eAA0C,CAC3C,CAAC;KACH;AACD,IAAA,aAAa,CAAC,OAGb,EAAA;QACC,OAAO,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;KACnD;IACD,uBAAuB,CACrB,QAAgB,EAChB,kBAAqD,EAAA;AAErD,QAAA,OAAO,GAAG,CAAC,uBAAuB,CAChC,IAAI,CAAC,SAAS,EACd,QAAQ,EACR,kBAAkB,CACnB,CAAC;KACH;AACD,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;KACrC;AACD,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;KACnC;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACD,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;KACnC;AACD,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;KACpC;AACD,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;KACpC;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACD,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;KACnC;AACD,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KAC7B;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;KAClC;AACD,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;KAC3B;AACD,IAAA,IAAY,IAAI,GAAA;AACd,QAAA,OAAQ,IAAI,CAAC,SAA0B,CAAC,IAA2B,CAAC;KACrE;;AAzMD;AACA;AACwB,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAkB;;AC9BlE;;;;;;;;;;;;;;;AAeG;AAuBH,MAAMA,SAAO,GAAuB,GAAG,CAAC,OAAO,CAAC;MAEnC,IAAI,CAAA;IAMf,WAAqB,CAAA,GAAgB,EAAE,QAA0B,EAAA;QAA5C,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;AACnC,QAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAkB,CAAC;YACzD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO;SACR;AAED,QAAA,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;;QAE/BA,SAAO,CAAC,MAAM,EAAqC,iBAAA,0CAAA;YACjD,OAAO,EAAE,GAAG,CAAC,IAAI;AAClB,SAAA,CAAC,CAAC;;QAGHA,SAAO,CAAC,MAAM,EAAqC,iBAAA,0CAAA;YACjD,OAAO,EAAE,GAAG,CAAC,IAAI;AAClB,SAAA,CAAC,CAAC;;AAGH,QAAA,MAAM,QAAQ,GACZ,OAAO,MAAM,KAAK,WAAW,GAAG,2BAA2B,GAAG,SAAS,CAAC;AAC1E,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC;AACnC,YAAA,OAAO,EAAE;gBACP,WAAW,EAAE,yBAAyB,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC;AACxD,gBAAA,qBAAqB,EAAE,QAAQ;AAChC,aAAA;AACF,SAAA,CAAiB,CAAC;QAEnB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAClD,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC3B;AAED,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;KACtC;AAED,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAC/B,YAAA,OAAO,IAAI,CAAC;SACb;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;KACrD;AACD,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;KACpC;IACD,IAAI,YAAY,CAAC,YAA2B,EAAA;AAC1C,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC;KAC5C;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;IACD,IAAI,QAAQ,CAAC,GAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC;KAC/B;IACD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;KACpC;IACD,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;KACjC;IACD,WAAW,CAAC,GAAW,EAAE,OAAsC,EAAA;QAC7D,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;KACvD;AACD,IAAA,eAAe,CAAC,IAAY,EAAA;QAC1B,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KAClD;AAED,IAAA,eAAe,CAAC,IAAY,EAAA;QAC1B,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KAClD;IAED,oBAAoB,CAAC,IAAY,EAAE,WAAmB,EAAA;AACpD,QAAA,OAAO,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;KACpE;AAED,IAAA,MAAM,8BAA8B,CAClC,KAAa,EACb,QAAgB,EAAA;AAEhB,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,8BAA8B,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CACpE,CAAC;KACH;AACD,IAAA,sBAAsB,CAAC,KAAa,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;KAC/C;AACD,IAAA,0BAA0B,CAAC,KAAa,EAAA;QACtC,OAAO,GAAG,CAAC,0BAA0B,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KAC9D;AACD,IAAA,qBAAqB,CAAC,SAAiB,EAAA;QACrC,OAAO,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;KAC7D;AACD,IAAA,MAAM,iBAAiB,GAAA;QACrBA,SAAO,CACL,yBAAyB,EAAE,EAC3B,IAAI,CAAC,SAAS,gGAEf,CAAC;AACF,QAAA,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,iBAAiB,CAC5C,IAAI,CAAC,SAAS,EACd,2BAA2B,CAC5B,CAAC;QACF,IAAI,CAAC,UAAU,EAAE;YACf,OAAO;AACL,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,IAAI,EAAE,IAAI;aACX,CAAC;SACH;AACD,QAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;KACvE;;;;AAKD,IAAA,sBAAsB,CAAC,SAAiB,EAAA;QACtC,GAAG,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;KACvD;AAED,IAAA,kBAAkB,CAChB,cAAwE,EACxE,OAA0C,EAC1C,SAAuB,EAAA;AAEvB,QAAA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,aAAa,CAC7C,cAAc,EACd,OAAO,EACP,SAAS,CACV,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;KAClE;AACD,IAAA,gBAAgB,CACd,cAAwE,EACxE,OAA0C,EAC1C,SAAuB,EAAA;AAEvB,QAAA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,aAAa,CAC7C,cAAc,EACd,OAAO,EACP,SAAS,CACV,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;KAChE;IACD,qBAAqB,CACnB,KAAa,EACb,kBAA6C,EAAA;AAE7C,QAAA,OAAO,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;KAC7E;IACD,sBAAsB,CACpB,KAAa,EACb,kBAAqD,EAAA;AAErD,QAAA,OAAO,GAAG,CAAC,sBAAsB,CAC/B,IAAI,CAAC,SAAS,EACd,KAAK,EACL,kBAAkB,IAAI,SAAS,CAChC,CAAC;KACH;IACD,MAAM,cAAc,CAAC,WAAmB,EAAA;AACtC,QAAA,4BAA4B,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC1D,QAAA,IAAI,SAAS,CAAC;QACd,QAAQ,WAAW;YACjB,KAAK,WAAW,CAAC,OAAO;AACtB,gBAAA,SAAS,GAAG,GAAG,CAAC,yBAAyB,CAAC;gBAC1C,MAAM;YACR,KAAK,WAAW,CAAC,KAAK;;gBAEpB,MAAM,yBAAyB,GAAG,MAAM,GAAG;AACxC,qBAAA,YAAY,CAA0B,GAAG,CAAC,yBAAyB,CAAC;AACpE,qBAAA,YAAY,EAAE,CAAC;AAClB,gBAAA,SAAS,GAAG,yBAAyB;sBACjC,GAAG,CAAC,yBAAyB;AAC/B,sBAAE,GAAG,CAAC,uBAAuB,CAAC;gBAChC,MAAM;YACR,KAAK,WAAW,CAAC,IAAI;AACnB,gBAAA,SAAS,GAAG,GAAG,CAAC,mBAAmB,CAAC;gBACpC,MAAM;AACR,YAAA;gBACE,OAAO,GAAG,CAAC,KAAK,CAAmC,gBAAA,yCAAA;AACjD,oBAAA,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;AAC7B,iBAAA,CAAC,CAAC;SACN;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;KACjD;AAED,IAAA,mCAAmC,CACjC,UAAiC,EAAA;AAEjC,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;KAC9C;IACD,iBAAiB,GAAA;AACf,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CACtC,CAAC;KACH;AACD,IAAA,oBAAoB,CAClB,UAAiC,EAAA;AAEjC,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,UAAgC,CAAC,CAC3E,CAAC;KACH;AACD,IAAA,qBAAqB,CAAC,KAAa,EAAA;AACjC,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CACjD,CAAC;KACH;IACD,0BAA0B,CACxB,KAAa,EACb,QAAgB,EAAA;AAEhB,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,0BAA0B,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAChE,CAAC;KACH;IACD,mBAAmB,CACjB,KAAa,EACb,SAAkB,EAAA;AAElB,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAC1D,CAAC;KACH;IACD,qBAAqB,CACnB,WAAmB,EACnB,mBAA+C,EAAA;AAE/C,QAAA,OAAO,yBAAyB,CAC9B,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,qBAAqB,CACvB,IAAI,CAAC,SAAS,EACd,WAAW,EACX,mBAAmB,CACpB,CACF,CAAC;KACH;IACD,MAAM,eAAe,CACnB,QAA6B,EAAA;QAE7BA,SAAO,CACL,yBAAyB,EAAE,EAC3B,IAAI,CAAC,SAAS,gGAEf,CAAC;AACF,QAAA,OAAO,iBAAiB,CACtB,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,eAAe,CACjB,IAAI,CAAC,SAAS,EACd,QAA4B,EAC5B,2BAA2B,CAC5B,CACF,CAAC;KACH;IACD,MAAM,kBAAkB,CAAC,QAA6B,EAAA;QACpDA,SAAO,CACL,yBAAyB,EAAE,EAC3B,IAAI,CAAC,SAAS,gGAEf,CAAC;AAEF,QAAA,MAAM,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAClD,QAAA,OAAO,GAAG,CAAC,kBAAkB,CAC3B,IAAI,CAAC,SAAS,EACd,QAA4B,EAC5B,2BAA2B,CAC5B,CAAC;KACH;AACD,IAAA,iBAAiB,CAAC,IAAwB,EAAA;;;QAGxC,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAC/C;AACD,IAAA,uBAAuB,CAAC,IAAY,EAAA;QAClC,OAAO,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KAC1D;IACD,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;KACjC;IACO,kBAAkB,GAAA;QACvB,IAAI,CAAC,SAA6C,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC;KAC1E;;AAzSM,IAAW,CAAA,WAAA,GAAG,WAAW,CAAC;AA4SnC,SAAS,aAAa,CACpB,cAAwE,EACxE,KAAwC,EACxC,QAAsB,EAAA;IAEtB,IAAI,IAAI,GAAG,cAAc,CAAC;AAC1B,IAAA,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;QACxC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,cAAc,EAAE;KAC9C;;IAGD,MAAM,OAAO,GAAG,IAA0C,CAAC;AAE3D,IAAA,MAAM,OAAO,GAAG,CAAC,IAAqB,KACpC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAgB,CAAC,CAAC,CAAC;IACtD,OAAO;AACL,QAAA,IAAI,EAAE,OAAO;AACb,QAAA,KAAK,EAAE,KAAgB;QACvB,QAAQ;KACT,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAChC,MAAc,EACd,OAAe,EAAA;;;;;IAMf,MAAM,YAAY,GAAG,4BAA4B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;;IAGnE,IACE,OAAO,IAAI,KAAK,WAAW;QAC3B,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,yBAAyB,CAAC,EACrD;AACA,QAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;KAClD;;AAGD,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,KAAK,MAAM,WAAW,IAAI;AACxB,YAAA,GAAG,CAAC,uBAAuB;AAC3B,YAAA,GAAG,CAAC,yBAAyB;AAC9B,SAAA,EAAE;YACD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AACvC,gBAAA,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAChC;SACF;KACF;;IAGD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;AACnD,QAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;KAC5C;AAED,IAAA,OAAO,YAAY,CAAC;AACtB;;ACjZA;;;;;;;;;;;;;;;AAeG;MAQU,iBAAiB,CAAA;AAS5B,IAAA,OAAO,UAAU,CACf,cAAsB,EACtB,gBAAwB,EAAA;QAExB,OAAO,GAAG,CAAC,iBAAiB,CAAC,UAAU,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;KAC3E;AAED,IAAA,WAAA,GAAA;QAbA,IAAU,CAAA,UAAA,GAAG,OAAO,CAAC;;;AAgBnB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAK,EAAE,CAAC,CAAC,CAAC;KACtE;IAED,iBAAiB,CACf,gBAI4C,EAC5C,mBAA+C,EAAA;AAE/C,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB;;;QAGrC,gBAAuB,EACvB,mBAAmB,CACpB,CAAC;KACH;IAED,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;AAlCM,iBAAA,CAAA,oBAAoB,GAAG,GAAG,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;AAClE,iBAAA,CAAA,WAAW,GAAG,GAAG,CAAC,iBAAiB,CAAC,WAAW;;AC9BxD;;;;;;;;;;;;;;;AAeG;AAOH,MAAM,OAAO,GAAuB,GAAG,CAAC,OAAO,CAAC;MAEnC,iBAAiB,CAAA;IAK5B,WACE,CAAA,SAA+B,EAC/B,UAA0B,EAC1B,MAAmB,QAAQ,CAAC,GAAG,EAAE,EAAA;;AAGjC,QAAA,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,EAAqC,iBAAA,0CAAA;YAC9D,OAAO,EAAE,GAAG,CAAC,IAAI;AAClB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,iBAAiB;;;AAGxC,QAAA,GAAG,CAAC,IAAK,EAAE,EACX,SAAS;;AAET,QAAA,UAAiB,CAClB,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KACjC;IACD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;KACxB;IACD,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;IACD,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;AACF;;ACzDD;;;;;;;;;;;;;;;AAeG;AAmBH,MAAM,SAAS,GAAG,aAAa,CAAC;AAqChC;AACA;AACA,SAAS,kBAAkB,CAAC,QAA4B,EAAA;AACtD,IAAA,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CACjC,IAAI,SAAS,CACX,SAAS,EACT,SAAS,IAAG;;QAEV,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;QAC/D,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACnD,QAAA,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;AACrC,KAAC,EAEF,QAAA,4BAAA;AACE,SAAA,eAAe,CAAC;AACf,QAAA,cAAc,EAAE;AACd,YAAA,SAAS,EAAE;AACT,gBAAA,YAAY,EAAED,GAAI,CAAC,mBAAmB,CAAC,YAAY;AACnD,gBAAA,cAAc,EAAEA,GAAI,CAAC,mBAAmB,CAAC,cAAc;AACvD,gBAAA,aAAa,EAAEA,GAAI,CAAC,mBAAmB,CAAC,aAAa;AACrD,gBAAA,6BAA6B,EAC3BA,GAAI,CAAC,mBAAmB,CAAC,6BAA6B;AACxD,gBAAA,uBAAuB,EACrBA,GAAI,CAAC,mBAAmB,CAAC,uBAAuB;AAClD,gBAAA,YAAY,EAAEA,GAAI,CAAC,mBAAmB,CAAC,YAAY;AACpD,aAAA;AACF,SAAA;QACD,iBAAiB,EAAEA,GAAI,CAAC,iBAAiB;QACzC,oBAAoB,EAAEA,GAAI,CAAC,oBAAoB;QAC/C,kBAAkB,EAAEA,GAAI,CAAC,kBAAkB;QAC3C,kBAAkB,EAAEA,GAAI,CAAC,kBAAkB;QAC3C,aAAa,EAAEA,GAAI,CAAC,aAAa;QACjC,gBAAgB,EAAEA,GAAI,CAAC,gBAAgB;AACvC,QAAA,iBAAiB,EAAEE,iBAAkB;QACrC,yBAAyB,EAAEF,GAAI,CAAC,yBAAyB;AACzD,QAAA,iBAAiB,EAAEG,iBAAuB;QAC1C,mBAAmB,EAAEH,GAAI,CAAC,mBAAmB;QAC7C,IAAI;QACJ,cAAc,EAAEA,GAAI,CAAC,cAAc;AACnC,QAAA,KAAK,EAAE,aAAa;KACrB,CAAC;AACD,SAAA,oBAAoB,CAAwB,MAAA,8BAAA;AAC5C,SAAA,oBAAoB,CAAC,KAAK,CAAC,CAC/B,CAAC;AAEF,IAAA,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED,kBAAkB,CAAC,QAA8B,CAAC"}