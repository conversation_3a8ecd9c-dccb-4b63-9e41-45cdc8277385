{"version": 3, "file": "internal.js", "sources": ["../../src/core/util/event_id.ts", "../../src/platform_browser/util/popup.ts", "../../src/core/util/resolver.ts", "../../src/core/strategies/idp.ts", "../../src/core/strategies/abstract_popup_redirect_operation.ts", "../../src/core/strategies/redirect.ts", "../../src/platform_browser/strategies/redirect.ts", "../../src/core/persistence/index.ts", "../../src/platform_browser/persistence/browser.ts", "../../src/platform_browser/persistence/session_storage.ts", "../../src/core/util/handler.ts", "../../src/platform_cordova/plugins.ts", "../../src/api/project_config/get_project_config.ts", "../../src/platform_cordova/popup_redirect/utils.ts", "../../src/core/auth/auth_event_manager.ts", "../../src/platform_browser/persistence/local_storage.ts", "../../src/platform_cordova/popup_redirect/events.ts", "../../src/platform_cordova/popup_redirect/popup_redirect.ts", "../../internal/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function _generateEventId(prefix = '', digits = 10): string {\n  let random = '';\n  for (let i = 0; i < digits; i++) {\n    random += Math.floor(Math.random() * 10);\n  }\n  return prefix + random;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getUA } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport {\n  _isChromeIOS,\n  _isFirefox,\n  _isIOSStandalone\n} from '../../core/util/browser';\nimport { AuthInternal } from '../../model/auth';\n\nconst BASE_POPUP_OPTIONS = {\n  location: 'yes',\n  resizable: 'yes',\n  statusbar: 'yes',\n  toolbar: 'no'\n};\n\nconst DEFAULT_WIDTH = 500;\nconst DEFAULT_HEIGHT = 600;\nconst TARGET_BLANK = '_blank';\n\nconst FIREFOX_EMPTY_URL = 'http://localhost';\n\nexport class AuthPopup {\n  associatedEvent: string | null = null;\n\n  constructor(readonly window: Window | null) {}\n\n  close(): void {\n    if (this.window) {\n      try {\n        this.window.close();\n      } catch (e) {}\n    }\n  }\n}\n\nexport function _open(\n  auth: AuthInternal,\n  url?: string,\n  name?: string,\n  width = DEFAULT_WIDTH,\n  height = DEFAULT_HEIGHT\n): AuthPopup {\n  const top = Math.max((window.screen.availHeight - height) / 2, 0).toString();\n  const left = Math.max((window.screen.availWidth - width) / 2, 0).toString();\n  let target = '';\n\n  const options: { [key: string]: string } = {\n    ...BASE_POPUP_OPTIONS,\n    width: width.toString(),\n    height: height.toString(),\n    top,\n    left\n  };\n\n  // Chrome iOS 7 and 8 is returning an undefined popup win when target is\n  // specified, even though the popup is not necessarily blocked.\n  const ua = getUA().toLowerCase();\n\n  if (name) {\n    target = _isChromeIOS(ua) ? TARGET_BLANK : name;\n  }\n\n  if (_isFirefox(ua)) {\n    // Firefox complains when invalid URLs are popped out. Hacky way to bypass.\n    url = url || FIREFOX_EMPTY_URL;\n    // Firefox disables by default scrolling on popup windows, which can create\n    // issues when the user has many Google accounts, for instance.\n    options.scrollbars = 'yes';\n  }\n\n  const optionsString = Object.entries(options).reduce(\n    (accum, [key, value]) => `${accum}${key}=${value},`,\n    ''\n  );\n\n  if (_isIOSStandalone(ua) && target !== '_self') {\n    openAsNewWindowIOS(url || '', target);\n    return new AuthPopup(null);\n  }\n\n  // about:blank getting sanitized causing browsers like IE/Edge to display\n  // brief error message before redirecting to handler.\n  const newWin = window.open(url || '', target, optionsString);\n  _assert(newWin, auth, AuthErrorCode.POPUP_BLOCKED);\n\n  // Flaky on IE edge, encapsulate with a try and catch.\n  try {\n    newWin.focus();\n  } catch (e) {}\n\n  return new AuthPopup(newWin);\n}\n\nfunction openAsNewWindowIOS(url: string, target: string): void {\n  const el = document.createElement('a');\n  el.href = url;\n  el.target = target;\n  const click = document.createEvent('MouseEvent');\n  click.initMouseEvent(\n    'click',\n    true,\n    true,\n    window,\n    1,\n    0,\n    0,\n    0,\n    0,\n    false,\n    false,\n    false,\n    false,\n    1,\n    null\n  );\n  el.dispatchEvent(click);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { PopupRedirectResolver } from '../../model/public_types';\nimport { AuthInternal } from '../../model/auth';\nimport { PopupRedirectResolverInternal } from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { _assert } from './assert';\nimport { _getInstance } from './instantiator';\n\n/**\n * Chooses a popup/redirect resolver to use. This prefers the override (which\n * is directly passed in), and falls back to the property set on the auth\n * object. If neither are available, this function errors w/ an argument error.\n */\nexport function _withDefaultResolver(\n  auth: AuthInternal,\n  resolverOverride: PopupRedirectResolver | undefined\n): PopupRedirectResolverInternal {\n  if (resolverOverride) {\n    return _getInstance(resolverOverride);\n  }\n\n  _assert(auth._popupRedirectResolver, auth, AuthErrorCode.ARGUMENT_ERROR);\n\n  return auth._popupRedirectResolver;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  signInWithIdp,\n  SignInWithIdpRequest\n} from '../../api/authentication/idp';\nimport { PhoneOrOauthTokenResponse } from '../../api/authentication/mfa';\nimport { AuthInternal } from '../../model/auth';\nimport { IdTokenResponse } from '../../model/id_token';\nimport { UserInternal, UserCredentialInternal } from '../../model/user';\nimport { AuthCredential } from '../credentials';\nimport { _link as _linkUser } from '../user/link_unlink';\nimport { _reauthenticate } from '../user/reauthenticate';\nimport { _assert } from '../util/assert';\nimport { _signInWithCredential } from './credential';\nimport { AuthErrorCode } from '../errors';\nimport { ProviderId } from '../../model/enums';\n\nexport interface IdpTaskParams {\n  auth: AuthInternal;\n  requestUri: string;\n  sessionId?: string;\n  tenantId?: string;\n  postBody?: string;\n  pendingToken?: string;\n  user?: UserInternal;\n  bypassAuthState?: boolean;\n}\n\nexport type IdpTask = (\n  params: IdpTaskParams\n) => Promise<UserCredentialInternal>;\n\nclass IdpCredential extends AuthCredential {\n  constructor(readonly params: IdpTaskParams) {\n    super(ProviderId.CUSTOM, ProviderId.CUSTOM);\n  }\n\n  _getIdTokenResponse(auth: AuthInternal): Promise<PhoneOrOauthTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  _linkToIdToken(\n    auth: AuthInternal,\n    idToken: string\n  ): Promise<IdTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest(idToken));\n  }\n\n  _getReauthenticationResolver(auth: AuthInternal): Promise<IdTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  private _buildIdpRequest(idToken?: string): SignInWithIdpRequest {\n    const request: SignInWithIdpRequest = {\n      requestUri: this.params.requestUri,\n      sessionId: this.params.sessionId,\n      postBody: this.params.postBody,\n      tenantId: this.params.tenantId,\n      pendingToken: this.params.pendingToken,\n      returnSecureToken: true,\n      returnIdpCredential: true\n    };\n\n    if (idToken) {\n      request.idToken = idToken;\n    }\n\n    return request;\n  }\n}\n\nexport function _signIn(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  return _signInWithCredential(\n    params.auth,\n    new IdpCredential(params),\n    params.bypassAuthState\n  ) as Promise<UserCredentialInternal>;\n}\n\nexport function _reauth(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  const { auth, user } = params;\n  _assert(user, auth, AuthErrorCode.INTERNAL_ERROR);\n  return _reauthenticate(\n    user,\n    new IdpCredential(params),\n    params.bypassAuthState\n  );\n}\n\nexport async function _link(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  const { auth, user } = params;\n  _assert(user, auth, AuthErrorCode.INTERNAL_ERROR);\n  return _linkUser(user, new IdpCredential(params), params.bypassAuthState);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport {\n  AuthEvent,\n  AuthEventConsumer,\n  AuthEventType,\n  EventManager,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserInternal, UserCredentialInternal } from '../../model/user';\nimport { AuthErrorCode } from '../errors';\nimport { debugAssert, _fail } from '../util/assert';\nimport {\n  _link,\n  _reauth,\n  _signIn,\n  IdpTask,\n  IdpTaskParams\n} from '../strategies/idp';\nimport { AuthInternal } from '../../model/auth';\n\ninterface PendingPromise {\n  resolve: (cred: UserCredentialInternal | null) => void;\n  reject: (error: Error) => void;\n}\n\n/**\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\n * events\n */\nexport abstract class AbstractPopupRedirectOperation\n  implements AuthEventConsumer\n{\n  private pendingPromise: PendingPromise | null = null;\n  private eventManager: EventManager | null = null;\n  readonly filter: AuthEventType[];\n\n  abstract eventId: string | null;\n\n  constructor(\n    protected readonly auth: AuthInternal,\n    filter: AuthEventType | AuthEventType[],\n    protected readonly resolver: PopupRedirectResolverInternal,\n    protected user?: UserInternal,\n    protected readonly bypassAuthState = false\n  ) {\n    this.filter = Array.isArray(filter) ? filter : [filter];\n  }\n\n  abstract onExecution(): Promise<void>;\n\n  execute(): Promise<UserCredentialInternal | null> {\n    return new Promise<UserCredentialInternal | null>(\n      async (resolve, reject) => {\n        this.pendingPromise = { resolve, reject };\n\n        try {\n          this.eventManager = await this.resolver._initialize(this.auth);\n          await this.onExecution();\n          this.eventManager.registerConsumer(this);\n        } catch (e) {\n          this.reject(e as Error);\n        }\n      }\n    );\n  }\n\n  async onAuthEvent(event: AuthEvent): Promise<void> {\n    const { urlResponse, sessionId, postBody, tenantId, error, type } = event;\n    if (error) {\n      this.reject(error);\n      return;\n    }\n\n    const params: IdpTaskParams = {\n      auth: this.auth,\n      requestUri: urlResponse!,\n      sessionId: sessionId!,\n      tenantId: tenantId || undefined,\n      postBody: postBody || undefined,\n      user: this.user,\n      bypassAuthState: this.bypassAuthState\n    };\n\n    try {\n      this.resolve(await this.getIdpTask(type)(params));\n    } catch (e) {\n      this.reject(e as Error);\n    }\n  }\n\n  onError(error: FirebaseError): void {\n    this.reject(error);\n  }\n\n  private getIdpTask(type: AuthEventType): IdpTask {\n    switch (type) {\n      case AuthEventType.SIGN_IN_VIA_POPUP:\n      case AuthEventType.SIGN_IN_VIA_REDIRECT:\n        return _signIn;\n      case AuthEventType.LINK_VIA_POPUP:\n      case AuthEventType.LINK_VIA_REDIRECT:\n        return _link;\n      case AuthEventType.REAUTH_VIA_POPUP:\n      case AuthEventType.REAUTH_VIA_REDIRECT:\n        return _reauth;\n      default:\n        _fail(this.auth, AuthErrorCode.INTERNAL_ERROR);\n    }\n  }\n\n  protected resolve(cred: UserCredentialInternal | null): void {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.resolve(cred);\n    this.unregisterAndCleanUp();\n  }\n\n  protected reject(error: Error): void {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.reject(error);\n    this.unregisterAndCleanUp();\n  }\n\n  private unregisterAndCleanUp(): void {\n    if (this.eventManager) {\n      this.eventManager.unregisterConsumer(this);\n    }\n\n    this.pendingPromise = null;\n    this.cleanUp();\n  }\n\n  abstract cleanUp(): void;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEvent,\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserCredentialInternal } from '../../model/user';\nimport { PersistenceInternal } from '../persistence';\nimport { _persistenceKeyName } from '../persistence/persistence_user_manager';\nimport { _getInstance } from '../util/instantiator';\nimport { AbstractPopupRedirectOperation } from './abstract_popup_redirect_operation';\n\nconst PENDING_REDIRECT_KEY = 'pendingRedirect';\n\n// We only get one redirect outcome for any one auth, so just store it\n// in here.\nconst redirectOutcomeMap: Map<\n  string,\n  () => Promise<UserCredentialInternal | null>\n> = new Map();\n\nexport class RedirectAction extends AbstractPopupRedirectOperation {\n  eventId = null;\n\n  constructor(\n    auth: AuthInternal,\n    resolver: PopupRedirectResolverInternal,\n    bypassAuthState = false\n  ) {\n    super(\n      auth,\n      [\n        AuthEventType.SIGN_IN_VIA_REDIRECT,\n        AuthEventType.LINK_VIA_REDIRECT,\n        AuthEventType.REAUTH_VIA_REDIRECT,\n        AuthEventType.UNKNOWN\n      ],\n      resolver,\n      undefined,\n      bypassAuthState\n    );\n  }\n\n  /**\n   * Override the execute function; if we already have a redirect result, then\n   * just return it.\n   */\n  async execute(): Promise<UserCredentialInternal | null> {\n    let readyOutcome = redirectOutcomeMap.get(this.auth._key());\n    if (!readyOutcome) {\n      try {\n        const hasPendingRedirect = await _getAndClearPendingRedirectStatus(\n          this.resolver,\n          this.auth\n        );\n        const result = hasPendingRedirect ? await super.execute() : null;\n        readyOutcome = () => Promise.resolve(result);\n      } catch (e) {\n        readyOutcome = () => Promise.reject(e);\n      }\n\n      redirectOutcomeMap.set(this.auth._key(), readyOutcome);\n    }\n\n    // If we're not bypassing auth state, the ready outcome should be set to\n    // null.\n    if (!this.bypassAuthState) {\n      redirectOutcomeMap.set(this.auth._key(), () => Promise.resolve(null));\n    }\n\n    return readyOutcome();\n  }\n\n  async onAuthEvent(event: AuthEvent): Promise<void> {\n    if (event.type === AuthEventType.SIGN_IN_VIA_REDIRECT) {\n      return super.onAuthEvent(event);\n    } else if (event.type === AuthEventType.UNKNOWN) {\n      // This is a sentinel value indicating there's no pending redirect\n      this.resolve(null);\n      return;\n    }\n\n    if (event.eventId) {\n      const user = await this.auth._redirectUserForId(event.eventId);\n      if (user) {\n        this.user = user;\n        return super.onAuthEvent(event);\n      } else {\n        this.resolve(null);\n      }\n    }\n  }\n\n  async onExecution(): Promise<void> {}\n\n  cleanUp(): void {}\n}\n\nexport async function _getAndClearPendingRedirectStatus(\n  resolver: PopupRedirectResolverInternal,\n  auth: AuthInternal\n): Promise<boolean> {\n  const key = pendingRedirectKey(auth);\n  const persistence = resolverPersistence(resolver);\n  if (!(await persistence._isAvailable())) {\n    return false;\n  }\n  const hasPendingRedirect = (await persistence._get(key)) === 'true';\n  await persistence._remove(key);\n  return hasPendingRedirect;\n}\n\nexport async function _setPendingRedirectStatus(\n  resolver: PopupRedirectResolverInternal,\n  auth: AuthInternal\n): Promise<void> {\n  return resolverPersistence(resolver)._set(pendingRedirectKey(auth), 'true');\n}\n\nexport function _clearRedirectOutcomes(): void {\n  redirectOutcomeMap.clear();\n}\n\nexport function _overrideRedirectResult(\n  auth: AuthInternal,\n  result: () => Promise<UserCredentialInternal | null>\n): void {\n  redirectOutcomeMap.set(auth._key(), result);\n}\n\nfunction resolverPersistence(\n  resolver: PopupRedirectResolverInternal\n): PersistenceInternal {\n  return _getInstance(resolver._redirectPersistence);\n}\n\nfunction pendingRedirectKey(auth: AuthInternal): string {\n  return _persistenceKeyName(\n    PENDING_REDIRECT_KEY,\n    auth.config.apiKey,\n    auth.name\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { _assertLinkedStatus } from '../../core/user/link_unlink';\nimport {\n  _assertInstanceOf,\n  _serverAppCurrentUserOperationNotSupportedError\n} from '../../core/util/assert';\nimport { _generateEventId } from '../../core/util/event_id';\nimport { AuthEventType } from '../../model/popup_redirect';\nimport { UserInternal } from '../../model/user';\nimport { _withDefaultResolver } from '../../core/util/resolver';\nimport {\n  RedirectAction,\n  _setPendingRedirectStatus\n} from '../../core/strategies/redirect';\nimport { FederatedAuthProvider } from '../../core/providers/federated';\nimport { getModularInstance } from '@firebase/util';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/**\n * Authenticates a Firebase client using a full-page redirect flow.\n *\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link signInWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * // You can add additional scopes to the provider:\n * provider.addScope('user_birthday');\n * // Start a sign in process for an unauthenticated user.\n * await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * if (result) {\n *   // This is the signed-in user\n *   const user = result.user;\n *   // This gives you a Facebook Access Token.\n *   const credential = provider.credentialFromResult(auth, result);\n *   const token = credential.accessToken;\n * }\n * // As this API can be used for sign-in, linking and reauthentication,\n * // check the operationType to determine what triggered this redirect\n * // operation.\n * const operationType = result.operationType;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function signInWithRedirect(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _signInWithRedirect(auth, provider, resolver) as Promise<never>;\n}\n\nexport async function _signInWithRedirect(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  _assertInstanceOf(auth, provider, FederatedAuthProvider);\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await authInternal._initializationPromise;\n  const resolverInternal = _withDefaultResolver(authInternal, resolver);\n  await _setPendingRedirectStatus(resolverInternal, authInternal);\n\n  return resolverInternal._openRedirect(\n    authInternal,\n    provider,\n    AuthEventType.SIGN_IN_VIA_REDIRECT\n  );\n}\n\n/**\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a full-page redirect flow.\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link reauthenticateWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances\n * created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * // Reauthenticate using a redirect.\n * await reauthenticateWithRedirect(result.user, provider);\n * // This will again trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function reauthenticateWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _reauthenticateWithRedirect(\n    user,\n    provider,\n    resolver\n  ) as Promise<never>;\n}\nexport async function _reauthenticateWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await userInternal.auth._initializationPromise;\n  // Allow the resolver to error before persisting the redirect user\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  await _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n\n  const eventId = await prepareUserForRedirect(userInternal);\n  return resolverInternal._openRedirect(\n    userInternal.auth,\n    provider,\n    AuthEventType.REAUTH_VIA_REDIRECT,\n    eventId\n  );\n}\n\n/**\n * Links the {@link OAuthProvider} to the user account using a full-page redirect flow.\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link linkWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances\n * created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using some other provider.\n * const result = await signInWithEmailAndPassword(auth, email, password);\n * // Link using a redirect.\n * const provider = new FacebookAuthProvider();\n * await linkWithRedirect(result.user, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function linkWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _linkWithRedirect(user, provider, resolver) as Promise<never>;\n}\nexport async function _linkWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await userInternal.auth._initializationPromise;\n  // Allow the resolver to error before persisting the redirect user\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  await _assertLinkedStatus(false, userInternal, provider.providerId);\n  await _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n\n  const eventId = await prepareUserForRedirect(userInternal);\n  return resolverInternal._openRedirect(\n    userInternal.auth,\n    provider,\n    AuthEventType.LINK_VIA_REDIRECT,\n    eventId\n  );\n}\n\n/**\n * Returns a {@link UserCredential} from the redirect-based sign-in flow.\n *\n * @remarks\n * If sign-in succeeded, returns the signed in user. If sign-in was unsuccessful, fails with an\n * error. If no redirect operation was called, returns `null`.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * // You can add additional scopes to the provider:\n * provider.addScope('user_birthday');\n * // Start a sign in process for an unauthenticated user.\n * await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * if (result) {\n *   // This is the signed-in user\n *   const user = result.user;\n *   // This gives you a Facebook Access Token.\n *   const credential = provider.credentialFromResult(auth, result);\n *   const token = credential.accessToken;\n * }\n * // As this API can be used for sign-in, linking and reauthentication,\n * // check the operationType to determine what triggered this redirect\n * // operation.\n * const operationType = result.operationType;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function getRedirectResult(\n  auth: Auth,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential | null> {\n  await _castAuth(auth)._initializationPromise;\n  return _getRedirectResult(auth, resolver, false);\n}\n\nexport async function _getRedirectResult(\n  auth: Auth,\n  resolverExtern?: PopupRedirectResolver,\n  bypassAuthState = false\n): Promise<UserCredential | null> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  const resolver = _withDefaultResolver(authInternal, resolverExtern);\n  const action = new RedirectAction(authInternal, resolver, bypassAuthState);\n  const result = await action.execute();\n\n  if (result && !bypassAuthState) {\n    delete result.user._redirectEventId;\n    await authInternal._persistUserIfCurrent(result.user as UserInternal);\n    await authInternal._setRedirectUser(null, resolverExtern);\n  }\n\n  return result;\n}\n\nasync function prepareUserForRedirect(user: UserInternal): Promise<string> {\n  const eventId = _generateEventId(`${user.uid}:::`);\n  user._redirectEventId = eventId;\n  await user.auth._setRedirectUser(user);\n  await user.auth._persistUserIfCurrent(user);\n  return eventId;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Persistence } from '../../model/public_types';\n\nexport const enum PersistenceType {\n  SESSION = 'SESSION',\n  LOCAL = 'LOCAL',\n  NONE = 'NONE',\n  COOKIE = 'COOKIE'\n}\n\nexport type PersistedBlob = Record<string, unknown>;\n\nexport interface Instantiator<T> {\n  (blob: PersistedBlob): T;\n}\n\nexport type PersistenceValue = PersistedBlob | string;\n\nexport const STORAGE_AVAILABLE_KEY = '__sak';\n\nexport interface StorageEventListener {\n  (value: PersistenceValue | null): void;\n}\n\nexport interface PersistenceInternal extends Persistence {\n  type: PersistenceType;\n  _isAvailable(): Promise<boolean>;\n  _set(key: string, value: PersistenceValue): Promise<void>;\n  _get<T extends PersistenceValue>(key: string): Promise<T | null>;\n  _remove(key: string): Promise<void>;\n  _addListener(key: string, listener: StorageEventListener): void;\n  _removeListener(key: string, listener: StorageEventListener): void;\n  // Should this persistence allow migration up the chosen hierarchy?\n  _shouldAllowMigration?: boolean;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  PersistenceValue,\n  STORAGE_AVAILABLE_KEY,\n  PersistenceType\n} from '../../core/persistence';\n\n// There are two different browser persistence types: local and session.\n// Both have the same implementation but use a different underlying storage\n// object.\n\nexport abstract class BrowserPersistenceClass {\n  protected constructor(\n    protected readonly storageRetriever: () => Storage,\n    readonly type: PersistenceType\n  ) {}\n\n  _isAvailable(): Promise<boolean> {\n    try {\n      if (!this.storage) {\n        return Promise.resolve(false);\n      }\n      this.storage.setItem(STORAGE_AVAILABLE_KEY, '1');\n      this.storage.removeItem(STORAGE_AVAILABLE_KEY);\n      return Promise.resolve(true);\n    } catch {\n      return Promise.resolve(false);\n    }\n  }\n\n  _set(key: string, value: PersistenceValue): Promise<void> {\n    this.storage.setItem(key, JSON.stringify(value));\n    return Promise.resolve();\n  }\n\n  _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    const json = this.storage.getItem(key);\n    return Promise.resolve(json ? JSON.parse(json) : null);\n  }\n\n  _remove(key: string): Promise<void> {\n    this.storage.removeItem(key);\n    return Promise.resolve();\n  }\n\n  protected get storage(): Storage {\n    return this.storageRetriever();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\n\nimport {\n  PersistenceInternal as InternalPersistence,\n  PersistenceType,\n  StorageEventListener\n} from '../../core/persistence';\nimport { BrowserPersistenceClass } from './browser';\n\nclass BrowserSessionPersistence\n  extends BrowserPersistenceClass\n  implements InternalPersistence\n{\n  static type: 'SESSION' = 'SESSION';\n\n  constructor() {\n    super(() => window.sessionStorage, PersistenceType.SESSION);\n  }\n\n  _addListener(_key: string, _listener: StorageEventListener): void {\n    // Listeners are not supported for session storage since it cannot be shared across windows\n    return;\n  }\n\n  _removeListener(_key: string, _listener: StorageEventListener): void {\n    // Listeners are not supported for session storage since it cannot be shared across windows\n    return;\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of `SESSION` using `sessionStorage`\n * for the underlying storage.\n *\n * @public\n */\nexport const browserSessionPersistence: Persistence = BrowserSessionPersistence;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SDK_VERSION } from '@firebase/app';\nimport { AuthProvider } from '../../model/public_types';\nimport { ApiKey, AppName, AuthInternal } from '../../model/auth';\nimport { AuthEventType } from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { _assert } from './assert';\nimport { isEmpty, querystring } from '@firebase/util';\nimport { _emulatorUrl } from './emulator';\nimport { FederatedAuthProvider } from '../providers/federated';\nimport { BaseOAuthProvider } from '../providers/oauth';\n\n/**\n * URL for Authentication widget which will initiate the OAuth handshake\n *\n * @internal\n */\nconst WIDGET_PATH = '__/auth/handler';\n\n/**\n * URL for emulated environment\n *\n * @internal\n */\nconst EMULATOR_WIDGET_PATH = 'emulator/auth/handler';\n\n/**\n * Fragment name for the App Check token that gets passed to the widget\n *\n * @internal\n */\nconst FIREBASE_APP_CHECK_FRAGMENT_ID = encodeURIComponent('fac');\n\n// eslint-disable-next-line @typescript-eslint/consistent-type-definitions\ntype WidgetParams = {\n  apiKey: ApiKey;\n  appName: AppName;\n  authType: AuthEventType;\n  redirectUrl?: string;\n  v: string;\n  providerId?: string;\n  scopes?: string;\n  customParameters?: string;\n  eventId?: string;\n  tid?: string;\n} & { [key: string]: string | undefined };\n\nexport async function _getRedirectUrl(\n  auth: AuthInternal,\n  provider: AuthProvider,\n  authType: AuthEventType,\n  redirectUrl?: string,\n  eventId?: string,\n  additionalParams?: Record<string, string>\n): Promise<string> {\n  _assert(auth.config.authDomain, auth, AuthErrorCode.MISSING_AUTH_DOMAIN);\n  _assert(auth.config.apiKey, auth, AuthErrorCode.INVALID_API_KEY);\n\n  const params: WidgetParams = {\n    apiKey: auth.config.apiKey,\n    appName: auth.name,\n    authType,\n    redirectUrl,\n    v: SDK_VERSION,\n    eventId\n  };\n\n  if (provider instanceof FederatedAuthProvider) {\n    provider.setDefaultLanguage(auth.languageCode);\n    params.providerId = provider.providerId || '';\n    if (!isEmpty(provider.getCustomParameters())) {\n      params.customParameters = JSON.stringify(provider.getCustomParameters());\n    }\n\n    // TODO set additionalParams from the provider as well?\n    for (const [key, value] of Object.entries(additionalParams || {})) {\n      params[key] = value;\n    }\n  }\n\n  if (provider instanceof BaseOAuthProvider) {\n    const scopes = provider.getScopes().filter(scope => scope !== '');\n    if (scopes.length > 0) {\n      params.scopes = scopes.join(',');\n    }\n  }\n\n  if (auth.tenantId) {\n    params.tid = auth.tenantId;\n  }\n\n  // TODO: maybe set eid as endpointId\n  // TODO: maybe set fw as Frameworks.join(\",\")\n\n  const paramsDict = params as Record<string, string | number>;\n  for (const key of Object.keys(paramsDict)) {\n    if (paramsDict[key] === undefined) {\n      delete paramsDict[key];\n    }\n  }\n\n  // Sets the App Check token to pass to the widget\n  const appCheckToken = await auth._getAppCheckToken();\n  const appCheckTokenFragment = appCheckToken\n    ? `#${FIREBASE_APP_CHECK_FRAGMENT_ID}=${encodeURIComponent(appCheckToken)}`\n    : '';\n\n  // Start at index 1 to skip the leading '&' in the query string\n  return `${getHandlerBase(auth)}?${querystring(paramsDict).slice(\n    1\n  )}${appCheckTokenFragment}`;\n}\n\nfunction getHandlerBase({ config }: AuthInternal): string {\n  if (!config.emulator) {\n    return `https://${config.authDomain}/${WIDGET_PATH}`;\n  }\n\n  return _emulatorUrl(config, EMULATOR_WIDGET_PATH);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface CordovaWindow extends Window {\n  cordova: {\n    plugins: {\n      browsertab: {\n        isAvailable(cb: (available: boolean) => void): void;\n        openUrl(url: string): void;\n        close(): void;\n      };\n    };\n\n    InAppBrowser: {\n      open(url: string, target: string, options: string): InAppBrowserRef;\n    };\n  };\n\n  universalLinks: {\n    subscribe(\n      n: null,\n      cb: (event: Record<string, string> | null) => void\n    ): void;\n  };\n\n  BuildInfo: {\n    readonly packageName: string;\n    readonly displayName: string;\n  };\n\n  handleOpenURL(url: string): void;\n}\n\nexport interface InAppBrowserRef {\n  close?: () => void;\n}\n\nexport function _cordovaWindow(): CordovaWindow {\n  return window as unknown as CordovaWindow;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _performApiRequest, Endpoint, HttpMethod } from '../index';\nimport { Auth } from '../../model/public_types';\n\nexport interface GetProjectConfigRequest {\n  androidPackageName?: string;\n  iosBundleId?: string;\n}\n\nexport interface GetProjectConfigResponse {\n  authorizedDomains: string[];\n}\n\nexport async function _getProjectConfig(\n  auth: Auth,\n  request: GetProjectConfigRequest = {}\n): Promise<GetProjectConfigResponse> {\n  return _performApiRequest<GetProjectConfigRequest, GetProjectConfigResponse>(\n    auth,\n    HttpMethod.GET,\n    Endpoint.GET_PROJECT_CONFIG,\n    request\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider } from '../../model/public_types';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  debugAssert,\n  _assert,\n  _createError,\n  _fail\n} from '../../core/util/assert';\nimport { _isAndroid, _isIOS, _isIOS7Or8 } from '../../core/util/browser';\nimport { _getRedirectUrl } from '../../core/util/handler';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent } from '../../model/popup_redirect';\nimport { InAppBrowserRef, _cordovaWindow } from '../plugins';\nimport {\n  GetProjectConfigRequest,\n  _getProjectConfig\n} from '../../api/project_config/get_project_config';\n\n/**\n * How long to wait after the app comes back into focus before concluding that\n * the user closed the sign in tab.\n */\nconst REDIRECT_TIMEOUT_MS = 2000;\n\n/**\n * Generates the URL for the OAuth handler.\n */\nexport async function _generateHandlerUrl(\n  auth: AuthInternal,\n  event: AuthEvent,\n  provider: AuthProvider\n): Promise<string> {\n  // Get the cordova plugins\n  const { BuildInfo } = _cordovaWindow();\n  debugAssert(event.sessionId, 'AuthEvent did not contain a session ID');\n  const sessionDigest = await computeSha256(event.sessionId);\n\n  const additionalParams: Record<string, string> = {};\n  if (_isIOS()) {\n    // iOS app identifier\n    additionalParams['ibi'] = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    // Android app identifier\n    additionalParams['apn'] = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Add the display name if available\n  if (BuildInfo.displayName) {\n    additionalParams['appDisplayName'] = BuildInfo.displayName;\n  }\n\n  // Attached the hashed session ID\n  additionalParams['sessionId'] = sessionDigest;\n  return _getRedirectUrl(\n    auth,\n    provider,\n    event.type,\n    undefined,\n    event.eventId ?? undefined,\n    additionalParams\n  );\n}\n\n/**\n * Validates that this app is valid for this project configuration\n */\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  const { BuildInfo } = _cordovaWindow();\n  const request: GetProjectConfigRequest = {};\n  if (_isIOS()) {\n    request.iosBundleId = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    request.androidPackageName = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Will fail automatically if package name is not authorized\n  await _getProjectConfig(auth, request);\n}\n\nexport function _performRedirect(\n  handlerUrl: string\n): Promise<InAppBrowserRef | null> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  return new Promise(resolve => {\n    cordova.plugins.browsertab.isAvailable(browserTabIsAvailable => {\n      let iabRef: InAppBrowserRef | null = null;\n      if (browserTabIsAvailable) {\n        cordova.plugins.browsertab.openUrl(handlerUrl);\n      } else {\n        // TODO: Return the inappbrowser ref that's returned from the open call\n        iabRef = cordova.InAppBrowser.open(\n          handlerUrl,\n          _isIOS7Or8() ? '_blank' : '_system',\n          'location=yes'\n        );\n      }\n      resolve(iabRef);\n    });\n  });\n}\n\n// Thin interface wrapper to avoid circular dependency with ./events module\ninterface PassiveAuthEventListener {\n  addPassiveListener(cb: () => void): void;\n  removePassiveListener(cb: () => void): void;\n}\n\n/**\n * This function waits for app activity to be seen before resolving. It does\n * this by attaching listeners to various dom events. Once the app is determined\n * to be visible, this promise resolves. AFTER that resolution, the listeners\n * are detached and any browser tabs left open will be closed.\n */\nexport async function _waitForAppResume(\n  auth: AuthInternal,\n  eventListener: PassiveAuthEventListener,\n  iabRef: InAppBrowserRef | null\n): Promise<void> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  let cleanup = (): void => {};\n  try {\n    await new Promise<void>((resolve, reject) => {\n      let onCloseTimer: number | null = null;\n\n      // DEFINE ALL THE CALLBACKS =====\n      function authEventSeen(): void {\n        // Auth event was detected. Resolve this promise and close the extra\n        // window if it's still open.\n        resolve();\n        const closeBrowserTab = cordova.plugins.browsertab?.close;\n        if (typeof closeBrowserTab === 'function') {\n          closeBrowserTab();\n        }\n        // Close inappbrowser embedded webview in iOS7 and 8 case if still\n        // open.\n        if (typeof iabRef?.close === 'function') {\n          iabRef.close();\n        }\n      }\n\n      function resumed(): void {\n        if (onCloseTimer) {\n          // This code already ran; do not rerun.\n          return;\n        }\n\n        onCloseTimer = window.setTimeout(() => {\n          // Wait two seconds after resume then reject.\n          reject(_createError(auth, AuthErrorCode.REDIRECT_CANCELLED_BY_USER));\n        }, REDIRECT_TIMEOUT_MS);\n      }\n\n      function visibilityChanged(): void {\n        if (document?.visibilityState === 'visible') {\n          resumed();\n        }\n      }\n\n      // ATTACH ALL THE LISTENERS =====\n      // Listen for the auth event\n      eventListener.addPassiveListener(authEventSeen);\n\n      // Listen for resume and visibility events\n      document.addEventListener('resume', resumed, false);\n      if (_isAndroid()) {\n        document.addEventListener('visibilitychange', visibilityChanged, false);\n      }\n\n      // SETUP THE CLEANUP FUNCTION =====\n      cleanup = () => {\n        eventListener.removePassiveListener(authEventSeen);\n        document.removeEventListener('resume', resumed, false);\n        document.removeEventListener(\n          'visibilitychange',\n          visibilityChanged,\n          false\n        );\n        if (onCloseTimer) {\n          window.clearTimeout(onCloseTimer);\n        }\n      };\n    });\n  } finally {\n    cleanup();\n  }\n}\n\n/**\n * Checks the configuration of the Cordova environment. This has no side effect\n * if the configuration is correct; otherwise it throws an error with the\n * missing plugin.\n */\nexport function _checkCordovaConfiguration(auth: AuthInternal): void {\n  const win = _cordovaWindow();\n  // Check all dependencies installed.\n  // https://github.com/nordnet/cordova-universal-links-plugin\n  // Note that cordova-universal-links-plugin has been abandoned.\n  // A fork with latest fixes is available at:\n  // https://www.npmjs.com/package/cordova-universal-links-plugin-fix\n  _assert(\n    typeof win?.universalLinks?.subscribe === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-universal-links-plugin-fix'\n    }\n  );\n\n  // https://www.npmjs.com/package/cordova-plugin-buildinfo\n  _assert(\n    typeof win?.BuildInfo?.packageName !== 'undefined',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-buildInfo'\n    }\n  );\n\n  // https://github.com/google/cordova-plugin-browsertab\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.openUrl === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.isAvailable === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n\n  // https://cordova.apache.org/docs/en/latest/reference/cordova-plugin-inappbrowser/\n  _assert(\n    typeof win?.cordova?.InAppBrowser?.open === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-inappbrowser'\n    }\n  );\n}\n\n/**\n * Computes the SHA-256 of a session ID. The SubtleCrypto interface is only\n * available in \"secure\" contexts, which covers Cordova (which is served on a file\n * protocol).\n */\nasync function computeSha256(sessionId: string): Promise<string> {\n  const bytes = stringToArrayBuffer(sessionId);\n\n  // TODO: For IE11 crypto has a different name and this operation comes back\n  //       as an object, not a promise. This is the old proposed standard that\n  //       is used by IE11:\n  // https://www.w3.org/TR/2013/WD-WebCryptoAPI-20130108/#cryptooperation-interface\n  const buf = await crypto.subtle.digest('SHA-256', bytes);\n  const arr = Array.from(new Uint8Array(buf));\n  return arr.map(num => num.toString(16).padStart(2, '0')).join('');\n}\n\nfunction stringToArrayBuffer(str: string): Uint8Array {\n  // This function is only meant to deal with an ASCII charset and makes\n  // certain simplifying assumptions.\n  debugAssert(\n    /[0-9a-zA-Z]+/.test(str),\n    'Can only convert alpha-numeric strings'\n  );\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(str);\n  }\n\n  const buff = new ArrayBuffer(str.length);\n  const view = new Uint8Array(buff);\n  for (let i = 0; i < str.length; i++) {\n    view[i] = str.charCodeAt(i);\n  }\n  return view;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AuthEvent,\n  AuthEventConsumer,\n  AuthEventType,\n  EventManager\n} from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { AuthInternal } from '../../model/auth';\nimport { _createError } from '../util/assert';\n\n// The amount of time to store the UIDs of seen events; this is\n// set to 10 min by default\nconst EVENT_DUPLICATION_CACHE_DURATION_MS = 10 * 60 * 1000;\n\nexport class AuthEventManager implements EventManager {\n  private readonly cachedEventUids: Set<string> = new Set();\n  private readonly consumers: Set<AuthEventConsumer> = new Set();\n  protected queuedRedirectEvent: AuthEvent | null = null;\n  protected hasHandledPotentialRedirect = false;\n  private lastProcessedEventTime = Date.now();\n\n  constructor(private readonly auth: AuthInternal) {}\n\n  registerConsumer(authEventConsumer: AuthEventConsumer): void {\n    this.consumers.add(authEventConsumer);\n\n    if (\n      this.queuedRedirectEvent &&\n      this.isEventForConsumer(this.queuedRedirectEvent, authEventConsumer)\n    ) {\n      this.sendToConsumer(this.queuedRedirectEvent, authEventConsumer);\n      this.saveEventToCache(this.queuedRedirectEvent);\n      this.queuedRedirectEvent = null;\n    }\n  }\n\n  unregisterConsumer(authEventConsumer: AuthEventConsumer): void {\n    this.consumers.delete(authEventConsumer);\n  }\n\n  onEvent(event: AuthEvent): boolean {\n    // Check if the event has already been handled\n    if (this.hasEventBeenHandled(event)) {\n      return false;\n    }\n\n    let handled = false;\n    this.consumers.forEach(consumer => {\n      if (this.isEventForConsumer(event, consumer)) {\n        handled = true;\n        this.sendToConsumer(event, consumer);\n        this.saveEventToCache(event);\n      }\n    });\n\n    if (this.hasHandledPotentialRedirect || !isRedirectEvent(event)) {\n      // If we've already seen a redirect before, or this is a popup event,\n      // bail now\n      return handled;\n    }\n\n    this.hasHandledPotentialRedirect = true;\n\n    // If the redirect wasn't handled, hang on to it\n    if (!handled) {\n      this.queuedRedirectEvent = event;\n      handled = true;\n    }\n\n    return handled;\n  }\n\n  private sendToConsumer(event: AuthEvent, consumer: AuthEventConsumer): void {\n    if (event.error && !isNullRedirectEvent(event)) {\n      const code =\n        (event.error.code?.split('auth/')[1] as AuthErrorCode) ||\n        AuthErrorCode.INTERNAL_ERROR;\n      consumer.onError(_createError(this.auth, code));\n    } else {\n      consumer.onAuthEvent(event);\n    }\n  }\n\n  private isEventForConsumer(\n    event: AuthEvent,\n    consumer: AuthEventConsumer\n  ): boolean {\n    const eventIdMatches =\n      consumer.eventId === null ||\n      (!!event.eventId && event.eventId === consumer.eventId);\n    return consumer.filter.includes(event.type) && eventIdMatches;\n  }\n\n  private hasEventBeenHandled(event: AuthEvent): boolean {\n    if (\n      Date.now() - this.lastProcessedEventTime >=\n      EVENT_DUPLICATION_CACHE_DURATION_MS\n    ) {\n      this.cachedEventUids.clear();\n    }\n\n    return this.cachedEventUids.has(eventUid(event));\n  }\n\n  private saveEventToCache(event: AuthEvent): void {\n    this.cachedEventUids.add(eventUid(event));\n    this.lastProcessedEventTime = Date.now();\n  }\n}\n\nfunction eventUid(e: AuthEvent): string {\n  return [e.type, e.eventId, e.sessionId, e.tenantId].filter(v => v).join('-');\n}\n\nfunction isNullRedirectEvent({ type, error }: AuthEvent): boolean {\n  return (\n    type === AuthEventType.UNKNOWN &&\n    error?.code === `auth/${AuthErrorCode.NO_AUTH_EVENT}`\n  );\n}\n\nfunction isRedirectEvent(event: AuthEvent): boolean {\n  switch (event.type) {\n    case AuthEventType.SIGN_IN_VIA_REDIRECT:\n    case AuthEventType.LINK_VIA_REDIRECT:\n    case AuthEventType.REAUTH_VIA_REDIRECT:\n      return true;\n    case AuthEventType.UNKNOWN:\n      return isNullRedirectEvent(event);\n    default:\n      return false;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\n\nimport { _isMobileBrowser, _isIE10 } from '../../core/util/browser';\nimport {\n  PersistenceInternal as InternalPersistence,\n  PersistenceType,\n  PersistenceValue,\n  StorageEventListener\n} from '../../core/persistence';\nimport { BrowserPersistenceClass } from './browser';\n\n// The polling period in case events are not supported\nexport const _POLLING_INTERVAL_MS = 1000;\n\n// The IE 10 localStorage cross tab synchronization delay in milliseconds\nconst IE10_LOCAL_STORAGE_SYNC_DELAY = 10;\n\nclass BrowserLocalPersistence\n  extends BrowserPersistenceClass\n  implements InternalPersistence\n{\n  static type: 'LOCAL' = 'LOCAL';\n\n  constructor() {\n    super(() => window.localStorage, PersistenceType.LOCAL);\n  }\n\n  private readonly boundEventHandler = (\n    event: StorageEvent,\n    poll?: boolean\n  ): void => this.onStorageEvent(event, poll);\n  private readonly listeners: Record<string, Set<StorageEventListener>> = {};\n  private readonly localCache: Record<string, string | null> = {};\n  // setTimeout return value is platform specific\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private pollTimer: any | null = null;\n\n  // Whether to use polling instead of depending on window events\n  private readonly fallbackToPolling = _isMobileBrowser();\n  readonly _shouldAllowMigration = true;\n\n  private forAllChangedKeys(\n    cb: (key: string, oldValue: string | null, newValue: string | null) => void\n  ): void {\n    // Check all keys with listeners on them.\n    for (const key of Object.keys(this.listeners)) {\n      // Get value from localStorage.\n      const newValue = this.storage.getItem(key);\n      const oldValue = this.localCache[key];\n      // If local map value does not match, trigger listener with storage event.\n      // Differentiate this simulated event from the real storage event.\n      if (newValue !== oldValue) {\n        cb(key, oldValue, newValue);\n      }\n    }\n  }\n\n  private onStorageEvent(event: StorageEvent, poll = false): void {\n    // Key would be null in some situations, like when localStorage is cleared\n    if (!event.key) {\n      this.forAllChangedKeys(\n        (key: string, _oldValue: string | null, newValue: string | null) => {\n          this.notifyListeners(key, newValue);\n        }\n      );\n      return;\n    }\n\n    const key = event.key;\n\n    // Check the mechanism how this event was detected.\n    // The first event will dictate the mechanism to be used.\n    if (poll) {\n      // Environment detects storage changes via polling.\n      // Remove storage event listener to prevent possible event duplication.\n      this.detachListener();\n    } else {\n      // Environment detects storage changes via storage event listener.\n      // Remove polling listener to prevent possible event duplication.\n      this.stopPolling();\n    }\n\n    const triggerListeners = (): void => {\n      // Keep local map up to date in case storage event is triggered before\n      // poll.\n      const storedValue = this.storage.getItem(key);\n      if (!poll && this.localCache[key] === storedValue) {\n        // Real storage event which has already been detected, do nothing.\n        // This seems to trigger in some IE browsers for some reason.\n        return;\n      }\n      this.notifyListeners(key, storedValue);\n    };\n\n    const storedValue = this.storage.getItem(key);\n    if (\n      _isIE10() &&\n      storedValue !== event.newValue &&\n      event.newValue !== event.oldValue\n    ) {\n      // IE 10 has this weird bug where a storage event would trigger with the\n      // correct key, oldValue and newValue but localStorage.getItem(key) does\n      // not yield the updated value until a few milliseconds. This ensures\n      // this recovers from that situation.\n      setTimeout(triggerListeners, IE10_LOCAL_STORAGE_SYNC_DELAY);\n    } else {\n      triggerListeners();\n    }\n  }\n\n  private notifyListeners(key: string, value: string | null): void {\n    this.localCache[key] = value;\n    const listeners = this.listeners[key];\n    if (listeners) {\n      for (const listener of Array.from(listeners)) {\n        listener(value ? JSON.parse(value) : value);\n      }\n    }\n  }\n\n  private startPolling(): void {\n    this.stopPolling();\n\n    this.pollTimer = setInterval(() => {\n      this.forAllChangedKeys(\n        (key: string, oldValue: string | null, newValue: string | null) => {\n          this.onStorageEvent(\n            new StorageEvent('storage', {\n              key,\n              oldValue,\n              newValue\n            }),\n            /* poll */ true\n          );\n        }\n      );\n    }, _POLLING_INTERVAL_MS);\n  }\n\n  private stopPolling(): void {\n    if (this.pollTimer) {\n      clearInterval(this.pollTimer);\n      this.pollTimer = null;\n    }\n  }\n\n  private attachListener(): void {\n    window.addEventListener('storage', this.boundEventHandler);\n  }\n\n  private detachListener(): void {\n    window.removeEventListener('storage', this.boundEventHandler);\n  }\n\n  _addListener(key: string, listener: StorageEventListener): void {\n    if (Object.keys(this.listeners).length === 0) {\n      // Whether browser can detect storage event when it had already been pushed to the background.\n      // This may happen in some mobile browsers. A localStorage change in the foreground window\n      // will not be detected in the background window via the storage event.\n      // This was detected in iOS 7.x mobile browsers\n      if (this.fallbackToPolling) {\n        this.startPolling();\n      } else {\n        this.attachListener();\n      }\n    }\n    if (!this.listeners[key]) {\n      this.listeners[key] = new Set();\n      // Populate the cache to avoid spuriously triggering on first poll.\n      this.localCache[key] = this.storage.getItem(key);\n    }\n    this.listeners[key].add(listener);\n  }\n\n  _removeListener(key: string, listener: StorageEventListener): void {\n    if (this.listeners[key]) {\n      this.listeners[key].delete(listener);\n\n      if (this.listeners[key].size === 0) {\n        delete this.listeners[key];\n      }\n    }\n\n    if (Object.keys(this.listeners).length === 0) {\n      this.detachListener();\n      this.stopPolling();\n    }\n  }\n\n  // Update local cache on base operations:\n\n  async _set(key: string, value: PersistenceValue): Promise<void> {\n    await super._set(key, value);\n    this.localCache[key] = JSON.stringify(value);\n  }\n\n  async _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    const value = await super._get<T>(key);\n    this.localCache[key] = JSON.stringify(value);\n    return value;\n  }\n\n  async _remove(key: string): Promise<void> {\n    await super._remove(key);\n    delete this.localCache[key];\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of type `LOCAL` using `localStorage`\n * for the underlying storage.\n *\n * @public\n */\nexport const browserLocalPersistence: Persistence = BrowserLocalPersistence;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { querystringDecode } from '@firebase/util';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../../core/errors';\nimport { PersistedBlob, PersistenceInternal } from '../../core/persistence';\nimport {\n  KeyName,\n  _persistenceKeyName\n} from '../../core/persistence/persistence_user_manager';\nimport { _createError } from '../../core/util/assert';\nimport { _getInstance } from '../../core/util/instantiator';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent, AuthEventType } from '../../model/popup_redirect';\nimport { browserLocalPersistence } from '../../platform_browser/persistence/local_storage';\n\nconst SESSION_ID_LENGTH = 20;\n\n/** Custom AuthEventManager that adds passive listeners to events */\nexport class CordovaAuthEventManager extends AuthEventManager {\n  private readonly passiveListeners = new Set<(e: AuthEvent) => void>();\n  private resolveInitialized!: () => void;\n  private initPromise = new Promise<void>(resolve => {\n    this.resolveInitialized = resolve;\n  });\n\n  addPassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.add(cb);\n  }\n\n  removePassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.delete(cb);\n  }\n\n  // In a Cordova environment, this manager can live through multiple redirect\n  // operations\n  resetRedirect(): void {\n    this.queuedRedirectEvent = null;\n    this.hasHandledPotentialRedirect = false;\n  }\n\n  /** Override the onEvent method */\n  onEvent(event: AuthEvent): boolean {\n    this.resolveInitialized();\n    this.passiveListeners.forEach(cb => cb(event));\n    return super.onEvent(event);\n  }\n\n  async initialized(): Promise<void> {\n    await this.initPromise;\n  }\n}\n\n/**\n * Generates a (partial) {@link AuthEvent}.\n */\nexport function _generateNewEvent(\n  auth: AuthInternal,\n  type: AuthEventType,\n  eventId: string | null = null\n): AuthEvent {\n  return {\n    type,\n    eventId,\n    urlResponse: null,\n    sessionId: generateSessionId(),\n    postBody: null,\n    tenantId: auth.tenantId,\n    error: _createError(auth, AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n\nexport function _savePartialEvent(\n  auth: AuthInternal,\n  event: AuthEvent\n): Promise<void> {\n  return storage()._set(persistenceKey(auth), event as object as PersistedBlob);\n}\n\nexport async function _getAndRemoveEvent(\n  auth: AuthInternal\n): Promise<AuthEvent | null> {\n  const event = (await storage()._get(\n    persistenceKey(auth)\n  )) as AuthEvent | null;\n  if (event) {\n    await storage()._remove(persistenceKey(auth));\n  }\n  return event;\n}\n\nexport function _eventFromPartialAndUrl(\n  partialEvent: AuthEvent,\n  url: string\n): AuthEvent | null {\n  // Parse the deep link within the dynamic link URL.\n  const callbackUrl = _getDeepLinkFromCallback(url);\n  // Confirm it is actually a callback URL.\n  // Currently the universal link will be of this format:\n  // https://<AUTH_DOMAIN>/__/auth/callback<OAUTH_RESPONSE>\n  // This is a fake URL but is not intended to take the user anywhere\n  // and just redirect to the app.\n  if (callbackUrl.includes('/__/auth/callback')) {\n    // Check if there is an error in the URL.\n    // This mechanism is also used to pass errors back to the app:\n    // https://<AUTH_DOMAIN>/__/auth/callback?firebaseError=<STRINGIFIED_ERROR>\n    const params = searchParamsOrEmpty(callbackUrl);\n    // Get the error object corresponding to the stringified error if found.\n    const errorObject = params['firebaseError']\n      ? parseJsonOrNull(decodeURIComponent(params['firebaseError']))\n      : null;\n    const code = errorObject?.['code']?.split('auth/')?.[1];\n    const error = code ? _createError(code) : null;\n    if (error) {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        error,\n        urlResponse: null,\n        sessionId: null,\n        postBody: null\n      };\n    } else {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        sessionId: partialEvent.sessionId,\n        urlResponse: callbackUrl,\n        postBody: null\n      };\n    }\n  }\n\n  return null;\n}\n\nfunction generateSessionId(): string {\n  const chars = [];\n  const allowedChars =\n    '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  for (let i = 0; i < SESSION_ID_LENGTH; i++) {\n    const idx = Math.floor(Math.random() * allowedChars.length);\n    chars.push(allowedChars.charAt(idx));\n  }\n  return chars.join('');\n}\n\nfunction storage(): PersistenceInternal {\n  return _getInstance(browserLocalPersistence);\n}\n\nfunction persistenceKey(auth: AuthInternal): string {\n  return _persistenceKeyName(KeyName.AUTH_EVENT, auth.config.apiKey, auth.name);\n}\n\nfunction parseJsonOrNull(json: string): ReturnType<typeof JSON.parse> | null {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return null;\n  }\n}\n\n// Exported for testing\nexport function _getDeepLinkFromCallback(url: string): string {\n  const params = searchParamsOrEmpty(url);\n  const link = params['link'] ? decodeURIComponent(params['link']) : undefined;\n  // Double link case (automatic redirect)\n  const doubleDeepLink = searchParamsOrEmpty(link)['link'];\n  // iOS custom scheme links.\n  const iOSDeepLink = params['deep_link_id']\n    ? decodeURIComponent(params['deep_link_id'])\n    : undefined;\n  const iOSDoubleDeepLink = searchParamsOrEmpty(iOSDeepLink)['link'];\n  return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\n}\n\n/**\n * Optimistically tries to get search params from a string, or else returns an\n * empty search params object.\n */\nfunction searchParamsOrEmpty(url: string | undefined): Record<string, string> {\n  if (!url?.includes('?')) {\n    return {};\n  }\n\n  const [_, ...rest] = url.split('?');\n  return querystringDecode(rest.join('?')) as Record<string, string>;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../../model/public_types';\nimport { browserSessionPersistence } from '../../platform_browser/persistence/session_storage';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEvent,\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { AuthPopup } from '../../platform_browser/util/popup';\nimport { _createError, _fail } from '../../core/util/assert';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _checkCordovaConfiguration,\n  _generateHandlerUrl,\n  _performRedirect,\n  _validateOrigin,\n  _waitForAppResume\n} from './utils';\nimport {\n  CordovaAuthEventManager,\n  _eventFromPartialAndUrl,\n  _generateNewEvent,\n  _getAndRemoveEvent,\n  _savePartialEvent\n} from './events';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { _getRedirectResult } from '../../platform_browser/strategies/redirect';\nimport {\n  _clearRedirectOutcomes,\n  _overrideRedirectResult\n} from '../../core/strategies/redirect';\nimport { _cordovaWindow } from '../plugins';\n\n/**\n * How long to wait for the initial auth event before concluding no\n * redirect pending\n */\nconst INITIAL_EVENT_TIMEOUT_MS = 500;\n\nclass CordovaPopupRedirectResolver implements PopupRedirectResolverInternal {\n  readonly _redirectPersistence = browserSessionPersistence;\n  readonly _shouldInitProactively = true; // This is lightweight for Cordova\n  private readonly eventManagers = new Map<string, CordovaAuthEventManager>();\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  _completeRedirectFn = _getRedirectResult;\n  _overrideRedirectResult = _overrideRedirectResult;\n\n  async _initialize(auth: AuthInternal): Promise<CordovaAuthEventManager> {\n    const key = auth._key();\n    let manager = this.eventManagers.get(key);\n    if (!manager) {\n      manager = new CordovaAuthEventManager(auth);\n      this.eventManagers.set(key, manager);\n      this.attachCallbackListeners(auth, manager);\n    }\n    return manager;\n  }\n\n  _openPopup(auth: AuthInternal): Promise<AuthPopup> {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<void> {\n    _checkCordovaConfiguration(auth);\n    const manager = await this._initialize(auth);\n    await manager.initialized();\n\n    // Reset the persisted redirect states. This does not matter on Web where\n    // the redirect always blows away application state entirely. On Cordova,\n    // the app maintains control flow through the redirect.\n    manager.resetRedirect();\n    _clearRedirectOutcomes();\n\n    await this._originValidation(auth);\n\n    const event = _generateNewEvent(auth, authType, eventId);\n    await _savePartialEvent(auth, event);\n    const url = await _generateHandlerUrl(auth, event, provider);\n    const iabRef = await _performRedirect(url);\n    return _waitForAppResume(auth, manager, iabRef);\n  }\n\n  _isIframeWebStorageSupported(\n    _auth: AuthInternal,\n    _cb: (support: boolean) => unknown\n  ): void {\n    throw new Error('Method not implemented.');\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  private attachCallbackListeners(\n    auth: AuthInternal,\n    manager: AuthEventManager\n  ): void {\n    // Get the global plugins\n    const { universalLinks, handleOpenURL, BuildInfo } = _cordovaWindow();\n\n    const noEventTimeout = setTimeout(async () => {\n      // We didn't see that initial event. Clear any pending object and\n      // dispatch no event\n      await _getAndRemoveEvent(auth);\n      manager.onEvent(generateNoEvent());\n    }, INITIAL_EVENT_TIMEOUT_MS);\n\n    const universalLinksCb = async (\n      eventData: Record<string, string> | null\n    ): Promise<void> => {\n      // We have an event so we can clear the no event timeout\n      clearTimeout(noEventTimeout);\n\n      const partialEvent = await _getAndRemoveEvent(auth);\n      let finalEvent: AuthEvent | null = null;\n      if (partialEvent && eventData?.['url']) {\n        finalEvent = _eventFromPartialAndUrl(partialEvent, eventData['url']);\n      }\n\n      // If finalEvent is never filled, trigger with no event\n      manager.onEvent(finalEvent || generateNoEvent());\n    };\n\n    // Universal links subscriber doesn't exist for iOS, so we need to check\n    if (\n      typeof universalLinks !== 'undefined' &&\n      typeof universalLinks.subscribe === 'function'\n    ) {\n      universalLinks.subscribe(null, universalLinksCb);\n    }\n\n    // iOS 7 or 8 custom URL schemes.\n    // This is also the current default behavior for iOS 9+.\n    // For this to work, cordova-plugin-customurlscheme needs to be installed.\n    // https://github.com/EddyVerbruggen/Custom-URL-scheme\n    // Do not overwrite the existing developer's URL handler.\n    const existingHandleOpenURL = handleOpenURL;\n    const packagePrefix = `${BuildInfo.packageName.toLowerCase()}://`;\n    _cordovaWindow().handleOpenURL = async url => {\n      if (url.toLowerCase().startsWith(packagePrefix)) {\n        // We want this intentionally to float\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        universalLinksCb({ url });\n      }\n      // Call the developer's handler if it is present.\n      if (typeof existingHandleOpenURL === 'function') {\n        try {\n          existingHandleOpenURL(url);\n        } catch (e) {\n          // This is a developer error. Don't stop the flow of the SDK.\n          console.error(e);\n        }\n      }\n    };\n  }\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for Cordova\n * based applications.\n *\n * @public\n */\nexport const cordovaPopupRedirectResolver: PopupRedirectResolver =\n  CordovaPopupRedirectResolver;\n\nfunction generateNoEvent(): AuthEvent {\n  return {\n    type: AuthEventType.UNKNOWN,\n    eventId: null,\n    sessionId: null,\n    urlResponse: null,\n    postBody: null,\n    tenantId: null,\n    error: _createError(AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _castAuth } from '../src/core/auth/auth_impl';\nimport { Auth } from '../src/model/public_types';\n\n/**\n * This interface is intended only for use by @firebase/auth-compat, do not use directly\n */\nexport * from '../index';\n\nexport { SignInWithIdpResponse } from '../src/api/authentication/idp';\nexport { AuthErrorCode } from '../src/core/errors';\nexport { PersistenceInternal } from '../src/core/persistence';\nexport { _persistenceKeyName } from '../src/core/persistence/persistence_user_manager';\nexport { UserImpl } from '../src/core/user/user_impl';\nexport { _getInstance } from '../src/core/util/instantiator';\nexport {\n  PopupRedirectResolverInternal,\n  EventManager,\n  AuthEventType\n} from '../src/model/popup_redirect';\nexport { UserCredentialInternal, UserParameters } from '../src/model/user';\nexport { AuthInternal, ConfigInternal } from '../src/model/auth';\nexport { DefaultConfig, AuthImpl, _castAuth } from '../src/core/auth/auth_impl';\n\nexport { ClientPlatform, _getClientVersion } from '../src/core/util/version';\n\nexport { _generateEventId } from '../src/core/util/event_id';\nexport { TaggedWithTokenResponse } from '../src/model/id_token';\nexport { _fail, _assert } from '../src/core/util/assert';\nexport { AuthPopup } from '../src/platform_browser/util/popup';\nexport { _getRedirectResult } from '../src/platform_browser/strategies/redirect';\nexport { _overrideRedirectResult } from '../src/core/strategies/redirect';\nexport { cordovaPopupRedirectResolver } from '../src/platform_cordova/popup_redirect/popup_redirect';\nexport { FetchProvider } from '../src/core/util/fetch_provider';\nexport { SAMLAuthCredential } from '../src/core/credentials/saml';\n\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nexport function addFrameworkForLogging(auth: Auth, framework: string): void {\n  _castAuth(auth)._logFramework(framework);\n}\n"], "names": ["_getInstance", "_assert", "AuthCredential", "signInWithIdp", "_signInWithCredential", "_reauthenticate", "_linkUser", "_fail", "debugAssert", "_persistenceKeyName", "_isFirebaseServerApp", "_serverAppCurrentUserOperationNotSupportedError", "_castAuth", "SDK_VERSION", "FederatedAuthProvider", "isEmpty", "BaseOAuthProvider", "querystring", "_emulatorUrl", "_performApiRequest", "_isIOS", "_isAndroid", "_isIOS7Or8", "_createError", "_isMobileBrowser", "_isIE10", "querystringDecode"], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEG,SAAU,gBAAgB,CAAC,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,EAAA;IACvD,IAAI,MAAM,GAAG,EAAE,CAAC;AAChB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/B,QAAA,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1C;IACD,OAAO,MAAM,GAAG,MAAM,CAAC;AACzB;;ACvBA;;;;;;;;;;;;;;;AAeG;MA0BU,SAAS,CAAA;AAGpB,IAAA,WAAA,CAAqB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAF1C,IAAe,CAAA,eAAA,GAAkB,IAAI,CAAC;KAEQ;IAE9C,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;aACrB;AAAC,YAAA,OAAO,CAAC,EAAE,GAAE;SACf;KACF;AACF;;ACrDD;;;;;;;;;;;;;;;AAeG;AASH;;;;AAIG;AACa,SAAA,oBAAoB,CAClC,IAAkB,EAClB,gBAAmD,EAAA;IAEnD,IAAI,gBAAgB,EAAE;AACpB,QAAA,OAAOA,iBAAY,CAAC,gBAAgB,CAAC,CAAC;KACvC;AAED,IAAAC,YAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,sDAA+B,CAAC;IAEzE,OAAO,IAAI,CAAC,sBAAsB,CAAC;AACrC;;ACxCA;;;;;;;;;;;;;;;AAeG;AAiCH,MAAM,aAAc,SAAQC,mBAAc,CAAA;AACxC,IAAA,WAAA,CAAqB,MAAqB,EAAA;AACxC,QAAA,KAAK,oEAAsC,CAAC;QADzB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAEzC;AAED,IAAA,mBAAmB,CAAC,IAAkB,EAAA;QACpC,OAAOC,kBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;KACrD;IAED,cAAc,CACZ,IAAkB,EAClB,OAAe,EAAA;QAEf,OAAOA,kBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;KAC5D;AAED,IAAA,4BAA4B,CAAC,IAAkB,EAAA;QAC7C,OAAOA,kBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;KACrD;AAEO,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AACvC,QAAA,MAAM,OAAO,GAAyB;AACpC,YAAA,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;AAClC,YAAA,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;AAChC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;AAC9B,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;AAC9B,YAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;AACtC,YAAA,iBAAiB,EAAE,IAAI;AACvB,YAAA,mBAAmB,EAAE,IAAI;SAC1B,CAAC;QAEF,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;SAC3B;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AACF,CAAA;AAEK,SAAU,OAAO,CACrB,MAAqB,EAAA;AAErB,IAAA,OAAOC,0BAAqB,CAC1B,MAAM,CAAC,IAAI,EACX,IAAI,aAAa,CAAC,MAAM,CAAC,EACzB,MAAM,CAAC,eAAe,CACY,CAAC;AACvC,CAAC;AAEK,SAAU,OAAO,CACrB,MAAqB,EAAA;AAErB,IAAA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;AAC9B,IAAAH,YAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;AAClD,IAAA,OAAOI,oBAAe,CACpB,IAAI,EACJ,IAAI,aAAa,CAAC,MAAM,CAAC,EACzB,MAAM,CAAC,eAAe,CACvB,CAAC;AACJ,CAAC;AAEM,eAAe,KAAK,CACzB,MAAqB,EAAA;AAErB,IAAA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;AAC9B,IAAAJ,YAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;AAClD,IAAA,OAAOK,UAAS,CAAC,IAAI,EAAE,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;AAC5E;;ACnHA;;;;;;;;;;;;;;;AAeG;AA4BH;;;AAGG;MACmB,8BAA8B,CAAA;IASlD,WACqB,CAAA,IAAkB,EACrC,MAAuC,EACpB,QAAuC,EAChD,IAAmB,EACV,eAAA,GAAkB,KAAK,EAAA;QAJvB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;QAElB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA+B;QAChD,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAe;QACV,IAAe,CAAA,eAAA,GAAf,eAAe,CAAQ;QAXpC,IAAc,CAAA,cAAA,GAA0B,IAAI,CAAC;QAC7C,IAAY,CAAA,YAAA,GAAwB,IAAI,CAAC;AAY/C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;KACzD;IAID,OAAO,GAAA;QACL,OAAO,IAAI,OAAO,CAChB,OAAO,OAAO,EAAE,MAAM,KAAI;YACxB,IAAI,CAAC,cAAc,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AAE1C,YAAA,IAAI;AACF,gBAAA,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/D,gBAAA,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACzB,gBAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;aAC1C;YAAC,OAAO,CAAC,EAAE;AACV,gBAAA,IAAI,CAAC,MAAM,CAAC,CAAU,CAAC,CAAC;aACzB;AACH,SAAC,CACF,CAAC;KACH;IAED,MAAM,WAAW,CAAC,KAAgB,EAAA;AAChC,QAAA,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QAC1E,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnB,OAAO;SACR;AAED,QAAA,MAAM,MAAM,GAAkB;YAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,YAAA,UAAU,EAAE,WAAY;AACxB,YAAA,SAAS,EAAE,SAAU;YACrB,QAAQ,EAAE,QAAQ,IAAI,SAAS;YAC/B,QAAQ,EAAE,QAAQ,IAAI,SAAS;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;AAEF,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;SACnD;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IAAI,CAAC,MAAM,CAAC,CAAU,CAAC,CAAC;SACzB;KACF;AAED,IAAA,OAAO,CAAC,KAAoB,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACpB;AAEO,IAAA,UAAU,CAAC,IAAmB,EAAA;QACpC,QAAQ,IAAI;YACV,KAAqC,gBAAA,uCAAA;AACrC,YAAA,KAAA,mBAAA;AACE,gBAAA,OAAO,OAAO,CAAC;YACjB,KAAkC,cAAA,oCAAA;AAClC,YAAA,KAAA,iBAAA;AACE,gBAAA,OAAO,KAAK,CAAC;YACf,KAAoC,gBAAA,sCAAA;AACpC,YAAA,KAAA,mBAAA;AACE,gBAAA,OAAO,OAAO,CAAC;AACjB,YAAA;AACE,gBAAAC,UAAK,CAAC,IAAI,CAAC,IAAI,sDAA+B,CAAC;SAClD;KACF;AAES,IAAA,OAAO,CAAC,IAAmC,EAAA;AACnD,QAAAC,gBAAW,CAAC,IAAI,CAAC,cAAc,EAAE,+BAA+B,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;AAES,IAAA,MAAM,CAAC,KAAY,EAAA;AAC3B,QAAAA,gBAAW,CAAC,IAAI,CAAC,cAAc,EAAE,+BAA+B,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;IAEO,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC5C;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;KAChB;AAGF;;ACtJD;;;;;;;;;;;;;;;AAeG;AAcH,MAAM,oBAAoB,GAAG,iBAAiB,CAAC;AAE/C;AACA;AACA,MAAM,kBAAkB,GAGpB,IAAI,GAAG,EAAE,CAAC;AAER,MAAO,cAAe,SAAQ,8BAA8B,CAAA;AAGhE,IAAA,WAAA,CACE,IAAkB,EAClB,QAAuC,EACvC,eAAe,GAAG,KAAK,EAAA;QAEvB,KAAK,CACH,IAAI,EACJ;;;;;AAKC,SAAA,EACD,QAAQ,EACR,SAAS,EACT,eAAe,CAChB,CAAC;QAlBJ,IAAO,CAAA,OAAA,GAAG,IAAI,CAAC;KAmBd;AAED;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,YAAY,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,IAAI;AACF,gBAAA,MAAM,kBAAkB,GAAG,MAAM,iCAAiC,CAChE,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,CACV,CAAC;AACF,gBAAA,MAAM,MAAM,GAAG,kBAAkB,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;gBACjE,YAAY,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC9C;YAAC,OAAO,CAAC,EAAE;gBACV,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACxC;AAED,YAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;SACxD;;;AAID,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SACvE;QAED,OAAO,YAAY,EAAE,CAAC;KACvB;IAED,MAAM,WAAW,CAAC,KAAgB,EAAA;AAChC,QAAA,IAAI,KAAK,CAAC,IAAI,KAAA,mBAAA,2CAAyC;AACrD,YAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACjC;AAAM,aAAA,IAAI,KAAK,CAAC,IAAI,KAAA,SAAA,8BAA4B;;AAE/C,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,OAAO;SACR;AAED,QAAA,IAAI,KAAK,CAAC,OAAO,EAAE;AACjB,YAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/D,IAAI,IAAI,EAAE;AACR,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,gBAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACjC;iBAAM;AACL,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aACpB;SACF;KACF;IAED,MAAM,WAAW,GAAA,GAAoB;AAErC,IAAA,OAAO,MAAW;AACnB,CAAA;AAEM,eAAe,iCAAiC,CACrD,QAAuC,EACvC,IAAkB,EAAA;AAElB,IAAA,MAAM,GAAG,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,MAAM,WAAW,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAClD,IAAI,EAAE,MAAM,WAAW,CAAC,YAAY,EAAE,CAAC,EAAE;AACvC,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,MAAM,kBAAkB,GAAG,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC;AACpE,IAAA,MAAM,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B,IAAA,OAAO,kBAAkB,CAAC;AAC5B,CAAC;SASe,sBAAsB,GAAA;IACpC,kBAAkB,CAAC,KAAK,EAAE,CAAC;AAC7B,CAAC;AAEe,SAAA,uBAAuB,CACrC,IAAkB,EAClB,MAAoD,EAAA;IAEpD,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,mBAAmB,CAC1B,QAAuC,EAAA;AAEvC,IAAA,OAAOR,iBAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAkB,EAAA;AAC5C,IAAA,OAAOS,wBAAmB,CACxB,oBAAoB,EACpB,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB,IAAI,CAAC,IAAI,CACV,CAAC;AACJ;;AC/JA;;;;;;;;;;;;;;;AAeG;AAqSI,eAAe,kBAAkB,CACtC,IAAU,EACV,cAAsC,EACtC,eAAe,GAAG,KAAK,EAAA;AAEvB,IAAA,IAAIC,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,MAAM,CACnBC,oDAA+C,CAAC,IAAI,CAAC,CACtD,CAAC;KACH;AACD,IAAA,MAAM,YAAY,GAAGC,cAAS,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IACpE,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;AAC3E,IAAA,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;AAEtC,IAAA,IAAI,MAAM,IAAI,CAAC,eAAe,EAAE;AAC9B,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACpC,MAAM,YAAY,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAoB,CAAC,CAAC;QACtE,MAAM,YAAY,CAAC,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAC3D;AAED,IAAA,OAAO,MAAM,CAAC;AAChB;;ACzSO,MAAM,qBAAqB,GAAG,OAAO;;ACjC5C;;;;;;;;;;;;;;;AAeG;AAQH;AACA;AACA;MAEsB,uBAAuB,CAAA;IAC3C,WACqB,CAAA,gBAA+B,EACzC,IAAqB,EAAA;QADX,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAe;QACzC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAiB;KAC5B;IAEJ,YAAY,GAAA;AACV,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;AAC/C,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC9B;AAAC,QAAA,MAAM;AACN,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;KACF;IAED,IAAI,CAAC,GAAW,EAAE,KAAuB,EAAA;AACvC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AAED,IAAA,IAAI,CAA6B,GAAW,EAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACvC,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACxD;AAED,IAAA,OAAO,CAAC,GAAW,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7B,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AAED,IAAA,IAAc,OAAO,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;KAChC;AACF;;AChED;;;;;;;;;;;;;;;AAeG;AAWH,MAAM,yBACJ,SAAQ,uBAAuB,CAAA;AAK/B,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,MAAM,MAAM,CAAC,cAAc,0CAA0B,CAAC;KAC7D;IAED,YAAY,CAAC,IAAY,EAAE,SAA+B,EAAA;;QAExD,OAAO;KACR;IAED,eAAe,CAAC,IAAY,EAAE,SAA+B,EAAA;;QAE3D,OAAO;KACR;;AAdM,yBAAI,CAAA,IAAA,GAAc,SAAS,CAAC;AAiBrC;;;;;AAKG;AACI,MAAM,yBAAyB,GAAgB,yBAAyB;;ACrD/E;;;;;;;;;;;;;;;AAeG;AAaH;;;;AAIG;AACH,MAAM,WAAW,GAAG,iBAAiB,CAAC;AAEtC;;;;AAIG;AACH,MAAM,oBAAoB,GAAG,uBAAuB,CAAC;AAErD;;;;AAIG;AACH,MAAM,8BAA8B,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAgB1D,eAAe,eAAe,CACnC,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,WAAoB,EACpB,OAAgB,EAChB,gBAAyC,EAAA;IAEzCX,YAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,EAAA,6BAAA,yCAAoC,CAAC;IACzEA,YAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAA,iBAAA,qCAAgC,CAAC;AAEjE,IAAA,MAAM,MAAM,GAAiB;AAC3B,QAAA,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;QAC1B,OAAO,EAAE,IAAI,CAAC,IAAI;QAClB,QAAQ;QACR,WAAW;AACX,QAAA,CAAC,EAAEY,eAAW;QACd,OAAO;KACR,CAAC;AAEF,IAAA,IAAI,QAAQ,YAAYC,0BAAqB,EAAE;AAC7C,QAAA,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/C,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,IAAI,CAACC,YAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,EAAE;AAC5C,YAAA,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,CAAC;SAC1E;;AAGD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE;AACjE,YAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;KACF;AAED,IAAA,IAAI,QAAQ,YAAYC,sBAAiB,EAAE;AACzC,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AAClE,QAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClC;KACF;AAED,IAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,QAAA,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;KAC5B;;;IAKD,MAAM,UAAU,GAAG,MAAyC,CAAC;IAC7D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzC,QAAA,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;AACjC,YAAA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;SACxB;KACF;;AAGD,IAAA,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACrD,MAAM,qBAAqB,GAAG,aAAa;UACvC,IAAI,8BAA8B,CAAA,CAAA,EAAI,kBAAkB,CAAC,aAAa,CAAC,CAAE,CAAA;UACzE,EAAE,CAAC;;AAGP,IAAA,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA,CAAA,EAAIC,gBAAW,CAAC,UAAU,CAAC,CAAC,KAAK,CAC7D,CAAC,CACF,CAAG,EAAA,qBAAqB,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,cAAc,CAAC,EAAE,MAAM,EAAgB,EAAA;AAC9C,IAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACpB,QAAA,OAAO,WAAW,MAAM,CAAC,UAAU,CAAI,CAAA,EAAA,WAAW,EAAE,CAAC;KACtD;AAED,IAAA,OAAOC,iBAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;AACpD;;ACvIA;;;;;;;;;;;;;;;AAeG;SAoCa,cAAc,GAAA;AAC5B,IAAA,OAAO,MAAkC,CAAC;AAC5C;;ACrDA;;;;;;;;;;;;;;;AAeG;AAcI,eAAe,iBAAiB,CACrC,IAAU,EACV,UAAmC,EAAE,EAAA;AAErC,IAAA,OAAOC,uBAAkB,CACvB,IAAI,EAGJ,KAAA,uBAAA,cAAA,oCAAA,OAAO,CACR,CAAC;AACJ;;ACvCA;;;;;;;;;;;;;;;AAeG;AAoBH;;;AAGG;AACH,MAAM,mBAAmB,GAAG,IAAI,CAAC;AAEjC;;AAEG;AACI,eAAe,mBAAmB,CACvC,IAAkB,EAClB,KAAgB,EAChB,QAAsB,EAAA;;AAGtB,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;AACvC,IAAAX,gBAAW,CAAC,KAAK,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAC;IACvE,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAE3D,MAAM,gBAAgB,GAA2B,EAAE,CAAC;IACpD,IAAIY,WAAM,EAAE,EAAE;;AAEZ,QAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;KACjD;SAAM,IAAIC,eAAU,EAAE,EAAE;;AAEvB,QAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;KACjD;SAAM;QACLd,UAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD;;AAGD,IAAA,IAAI,SAAS,CAAC,WAAW,EAAE;AACzB,QAAA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;KAC5D;;AAGD,IAAA,gBAAgB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;IAC9C,OAAO,eAAe,CACpB,IAAI,EACJ,QAAQ,EACR,KAAK,CAAC,IAAI,EACV,SAAS,EACT,KAAK,CAAC,OAAO,IAAI,SAAS,EAC1B,gBAAgB,CACjB,CAAC;AACJ,CAAC;AAED;;AAEG;AACI,eAAe,eAAe,CAAC,IAAkB,EAAA;AACtD,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;IACvC,MAAM,OAAO,GAA4B,EAAE,CAAC;IAC5C,IAAIa,WAAM,EAAE,EAAE;AACZ,QAAA,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;KAC7C;SAAM,IAAIC,eAAU,EAAE,EAAE;AACvB,QAAA,OAAO,CAAC,kBAAkB,GAAG,SAAS,CAAC,WAAW,CAAC;KACpD;SAAM;QACLd,UAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD;;AAGD,IAAA,MAAM,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACzC,CAAC;AAEK,SAAU,gBAAgB,CAC9B,UAAkB,EAAA;;AAGlB,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,cAAc,EAAE,CAAC;AAErC,IAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;QAC3B,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,qBAAqB,IAAG;YAC7D,IAAI,MAAM,GAA2B,IAAI,CAAC;YAC1C,IAAI,qBAAqB,EAAE;gBACzB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;aAChD;iBAAM;;gBAEL,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAChC,UAAU,EACVe,eAAU,EAAE,GAAG,QAAQ,GAAG,SAAS,EACnC,cAAc,CACf,CAAC;aACH;YACD,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAQD;;;;;AAKG;AACI,eAAe,iBAAiB,CACrC,IAAkB,EAClB,aAAuC,EACvC,MAA8B,EAAA;;AAG9B,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,cAAc,EAAE,CAAC;AAErC,IAAA,IAAI,OAAO,GAAG,MAAW,GAAG,CAAC;AAC7B,IAAA,IAAI;QACF,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;YAC1C,IAAI,YAAY,GAAkB,IAAI,CAAC;;AAGvC,YAAA,SAAS,aAAa,GAAA;;;AAGpB,gBAAA,OAAO,EAAE,CAAC;gBACV,MAAM,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC;AAC1D,gBAAA,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AACzC,oBAAA,eAAe,EAAE,CAAC;iBACnB;;;AAGD,gBAAA,IAAI,OAAO,MAAM,EAAE,KAAK,KAAK,UAAU,EAAE;oBACvC,MAAM,CAAC,KAAK,EAAE,CAAC;iBAChB;aACF;AAED,YAAA,SAAS,OAAO,GAAA;gBACd,IAAI,YAAY,EAAE;;oBAEhB,OAAO;iBACR;AAED,gBAAA,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;;AAEpC,oBAAA,MAAM,CAACC,iBAAY,CAAC,IAAI,EAAA,4BAAA,gDAA2C,CAAC,CAAC;iBACtE,EAAE,mBAAmB,CAAC,CAAC;aACzB;AAED,YAAA,SAAS,iBAAiB,GAAA;AACxB,gBAAA,IAAI,QAAQ,EAAE,eAAe,KAAK,SAAS,EAAE;AAC3C,oBAAA,OAAO,EAAE,CAAC;iBACX;aACF;;;AAID,YAAA,aAAa,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;;YAGhD,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACpD,IAAIF,eAAU,EAAE,EAAE;gBAChB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;aACzE;;YAGD,OAAO,GAAG,MAAK;AACb,gBAAA,aAAa,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gBACnD,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBACvD,QAAQ,CAAC,mBAAmB,CAC1B,kBAAkB,EAClB,iBAAiB,EACjB,KAAK,CACN,CAAC;gBACF,IAAI,YAAY,EAAE;AAChB,oBAAA,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;iBACnC;AACH,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;YAAS;AACR,QAAA,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAED;;;;AAIG;AACG,SAAU,0BAA0B,CAAC,IAAkB,EAAA;AAC3D,IAAA,MAAM,GAAG,GAAG,cAAc,EAAE,CAAC;;;;;;IAM7BpB,YAAO,CACL,OAAO,GAAG,EAAE,cAAc,EAAE,SAAS,KAAK,UAAU,EACpD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,oCAAoC;AACpD,KAAA,CACF,CAAC;;IAGFA,YAAO,CACL,OAAO,GAAG,EAAE,SAAS,EAAE,WAAW,KAAK,WAAW,EAClD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,0BAA0B;AAC1C,KAAA,CACF,CAAC;;AAGF,IAAAA,YAAO,CACL,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,KAAK,UAAU,EAChE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;AACF,IAAAA,YAAO,CACL,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,KAAK,UAAU,EACpE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;;AAGF,IAAAA,YAAO,CACL,OAAO,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,KAAK,UAAU,EACtD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,6BAA6B;AAC7C,KAAA,CACF,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACH,eAAe,aAAa,CAAC,SAAiB,EAAA;AAC5C,IAAA,MAAM,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;;;;;AAM7C,IAAA,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACzD,IAAA,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAW,EAAA;;;IAGtCO,gBAAW,CACT,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EACxB,wCAAwC,CACzC,CAAC;AACF,IAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACtC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACtC;IAED,MAAM,IAAI,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAA,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KAC7B;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AClTA;;;;;;;;;;;;;;;AAeG;AAYH;AACA;AACA,MAAM,mCAAmC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAE9C,gBAAgB,CAAA;AAO3B,IAAA,WAAA,CAA6B,IAAkB,EAAA;QAAlB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;AAN9B,QAAA,IAAA,CAAA,eAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;AACzC,QAAA,IAAA,CAAA,SAAS,GAA2B,IAAI,GAAG,EAAE,CAAC;QACrD,IAAmB,CAAA,mBAAA,GAAqB,IAAI,CAAC;QAC7C,IAA2B,CAAA,2BAAA,GAAG,KAAK,CAAC;AACtC,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAEO;AAEnD,IAAA,gBAAgB,CAAC,iBAAoC,EAAA;AACnD,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEtC,IACE,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,EACpE;YACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;AACjE,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;KACF;AAED,IAAA,kBAAkB,CAAC,iBAAoC,EAAA;AACrD,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAC1C;AAED,IAAA,OAAO,CAAC,KAAgB,EAAA;;AAEtB,QAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AACnC,YAAA,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;YAChC,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAC5C,OAAO,GAAG,IAAI,CAAC;AACf,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACrC,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;aAC9B;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,2BAA2B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;;;AAG/D,YAAA,OAAO,OAAO,CAAC;SAChB;AAED,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;;QAGxC,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACjC,OAAO,GAAG,IAAI,CAAC;SAChB;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;IAEO,cAAc,CAAC,KAAgB,EAAE,QAA2B,EAAA;QAClE,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AAC9C,YAAA,MAAM,IAAI,GACP,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAmB;oEACzB;AAC/B,YAAA,QAAQ,CAAC,OAAO,CAACe,iBAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SACjD;aAAM;AACL,YAAA,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC7B;KACF;IAEO,kBAAkB,CACxB,KAAgB,EAChB,QAA2B,EAAA;AAE3B,QAAA,MAAM,cAAc,GAClB,QAAQ,CAAC,OAAO,KAAK,IAAI;AACzB,aAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC1D,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC;KAC/D;AAEO,IAAA,mBAAmB,CAAC,KAAgB,EAAA;AAC1C,QAAA,IACE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,sBAAsB;AACxC,YAAA,mCAAmC,EACnC;AACA,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;KAClD;AAEO,IAAA,gBAAgB,CAAC,KAAgB,EAAA;QACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAC1C;AACF,CAAA;AAED,SAAS,QAAQ,CAAC,CAAY,EAAA;AAC5B,IAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAa,EAAA;IACrD,QACE,IAAI,KAA0B,SAAA;AAC9B,QAAA,KAAK,EAAE,IAAI,KAAK,QAAQ,eAA2B,mCAAA,CAAE,EACrD;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,KAAgB,EAAA;AACvC,IAAA,QAAQ,KAAK,CAAC,IAAI;QAChB,KAAwC,mBAAA,0CAAA;QACxC,KAAqC,iBAAA,uCAAA;AACrC,QAAA,KAAA,mBAAA;AACE,YAAA,OAAO,IAAI,CAAC;AACd,QAAA,KAAA,SAAA;AACE,YAAA,OAAO,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACpC,QAAA;AACE,YAAA,OAAO,KAAK,CAAC;KAChB;AACH;;ACrJA;;;;;;;;;;;;;;;AAeG;AAaH;AACO,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAEzC;AACA,MAAM,6BAA6B,GAAG,EAAE,CAAC;AAEzC,MAAM,uBACJ,SAAQ,uBAAuB,CAAA;AAK/B,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,MAAM,MAAM,CAAC,YAAY,sCAAwB,CAAC;AAGzC,QAAA,IAAA,CAAA,iBAAiB,GAAG,CACnC,KAAmB,EACnB,IAAc,KACL,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3B,IAAS,CAAA,SAAA,GAA8C,EAAE,CAAC;QAC1D,IAAU,CAAA,UAAA,GAAkC,EAAE,CAAC;;;QAGxD,IAAS,CAAA,SAAA,GAAe,IAAI,CAAC;;QAGpB,IAAiB,CAAA,iBAAA,GAAGC,qBAAgB,EAAE,CAAC;QAC/C,IAAqB,CAAA,qBAAA,GAAG,IAAI,CAAC;KAdrC;AAgBO,IAAA,iBAAiB,CACvB,EAA2E,EAAA;;AAG3E,QAAA,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;YAE7C,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;;AAGtC,YAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,gBAAA,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC7B;SACF;KACF;AAEO,IAAA,cAAc,CAAC,KAAmB,EAAE,IAAI,GAAG,KAAK,EAAA;;AAEtD,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,iBAAiB,CACpB,CAAC,GAAW,EAAE,SAAwB,EAAE,QAAuB,KAAI;AACjE,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAC,CACF,CAAC;YACF,OAAO;SACR;AAED,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;;;QAItB,IAAI,IAAI,EAAE;;;YAGR,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;aAAM;;;YAGL,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;QAED,MAAM,gBAAgB,GAAG,MAAW;;;YAGlC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;;;gBAGjD,OAAO;aACR;AACD,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACzC,SAAC,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IACEC,YAAO,EAAE;YACT,WAAW,KAAK,KAAK,CAAC,QAAQ;AAC9B,YAAA,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,EACjC;;;;;AAKA,YAAA,UAAU,CAAC,gBAAgB,EAAE,6BAA6B,CAAC,CAAC;SAC7D;aAAM;AACL,YAAA,gBAAgB,EAAE,CAAC;SACpB;KACF;IAEO,eAAe,CAAC,GAAW,EAAE,KAAoB,EAAA;AACvD,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,SAAS,EAAE;YACb,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC5C,gBAAA,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;aAC7C;SACF;KACF;IAEO,YAAY,GAAA;QAClB,IAAI,CAAC,WAAW,EAAE,CAAC;AAEnB,QAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,MAAK;YAChC,IAAI,CAAC,iBAAiB,CACpB,CAAC,GAAW,EAAE,QAAuB,EAAE,QAAuB,KAAI;AAChE,gBAAA,IAAI,CAAC,cAAc,CACjB,IAAI,YAAY,CAAC,SAAS,EAAE;oBAC1B,GAAG;oBACH,QAAQ;oBACR,QAAQ;iBACT,CAAC;2BACS,IAAI,CAChB,CAAC;AACJ,aAAC,CACF,CAAC;SACH,EAAE,oBAAoB,CAAC,CAAC;KAC1B;IAEO,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;KACF;IAEO,cAAc,GAAA;QACpB,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC5D;IAEO,cAAc,GAAA;QACpB,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC/D;IAED,YAAY,CAAC,GAAW,EAAE,QAA8B,EAAA;AACtD,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;;;;;AAK5C,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;aACrB;iBAAM;gBACL,IAAI,CAAC,cAAc,EAAE,CAAC;aACvB;SACF;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;;AAEhC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;KACnC;IAED,eAAe,CAAC,GAAW,EAAE,QAA8B,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAErC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aAC5B;SACF;AAED,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;KACF;;AAID,IAAA,MAAM,IAAI,CAAC,GAAW,EAAE,KAAuB,EAAA;QAC7C,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KAC9C;IAED,MAAM,IAAI,CAA6B,GAAW,EAAA;QAChD,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI,CAAI,GAAG,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7C,QAAA,OAAO,KAAK,CAAC;KACd;IAED,MAAM,OAAO,CAAC,GAAW,EAAA;AACvB,QAAA,MAAM,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACzB,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KAC7B;;AAxLM,uBAAI,CAAA,IAAA,GAAY,OAAZ,CAAoB;AA2LjC;;;;;AAKG;AACI,MAAM,uBAAuB,GAAgB,uBAAuB;;ACvO3E;;;;;;;;;;;;;;;AAeG;AAgBH,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAE7B;AACM,MAAO,uBAAwB,SAAQ,gBAAgB,CAAA;AAA7D,IAAA,WAAA,GAAA;;AACmB,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAA0B,CAAC;AAE9D,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,OAAO,CAAO,OAAO,IAAG;AAChD,YAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;AACpC,SAAC,CAAC,CAAC;KA2BJ;AAzBC,IAAA,kBAAkB,CAAC,EAA0B,EAAA;AAC3C,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;KAC/B;AAED,IAAA,qBAAqB,CAAC,EAA0B,EAAA;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KAClC;;;IAID,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;KAC1C;;AAGD,IAAA,OAAO,CAAC,KAAgB,EAAA;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/C,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,MAAM,WAAW,GAAA;QACf,MAAM,IAAI,CAAC,WAAW,CAAC;KACxB;AACF,CAAA;AAED;;AAEG;AACG,SAAU,iBAAiB,CAC/B,IAAkB,EAClB,IAAmB,EACnB,UAAyB,IAAI,EAAA;IAE7B,OAAO;QACL,IAAI;QACJ,OAAO;AACP,QAAA,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,iBAAiB,EAAE;AAC9B,QAAA,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,QAAA,KAAK,EAAEF,iBAAY,CAAC,IAAI,EAA8B,eAAA,mCAAA;KACvD,CAAC;AACJ,CAAC;AAEe,SAAA,iBAAiB,CAC/B,IAAkB,EAClB,KAAgB,EAAA;AAEhB,IAAA,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAgC,CAAC,CAAC;AAChF,CAAC;AAEM,eAAe,kBAAkB,CACtC,IAAkB,EAAA;AAElB,IAAA,MAAM,KAAK,IAAI,MAAM,OAAO,EAAE,CAAC,IAAI,CACjC,cAAc,CAAC,IAAI,CAAC,CACrB,CAAqB,CAAC;IACvB,IAAI,KAAK,EAAE;QACT,MAAM,OAAO,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;KAC/C;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEe,SAAA,uBAAuB,CACrC,YAAuB,EACvB,GAAW,EAAA;;AAGX,IAAA,MAAM,WAAW,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;;;;;;AAMlD,IAAA,IAAI,WAAW,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;;;;AAI7C,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;;AAEhD,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC;cACvC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;cAC5D,IAAI,CAAC;AACT,QAAA,MAAM,IAAI,GAAG,WAAW,GAAG,MAAM,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AACxD,QAAA,MAAM,KAAK,GAAG,IAAI,GAAGA,iBAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC/C,IAAI,KAAK,EAAE;YACT,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,KAAK;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;SACH;aAAM;YACL,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,SAAS,EAAE,YAAY,CAAC,SAAS;AACjC,gBAAA,WAAW,EAAE,WAAW;AACxB,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;SACH;KACF;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,GAAA;IACxB,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,MAAM,YAAY,GAChB,gEAAgE,CAAC;AACnE,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5D,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KACtC;AACD,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,OAAO,GAAA;AACd,IAAA,OAAOvB,iBAAY,CAAC,uBAAuB,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,cAAc,CAAC,IAAkB,EAAA;AACxC,IAAA,OAAOS,wBAAmB,CAAA,WAAA,2BAAqB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC;AAED,SAAS,eAAe,CAAC,IAAY,EAAA;AACnC,IAAA,IAAI;AACF,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACzB;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAED;AACM,SAAU,wBAAwB,CAAC,GAAW,EAAA;AAClD,IAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC;;IAE7E,MAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;;AAEzD,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;AACxC,UAAE,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;UAC1C,SAAS,CAAC;IACd,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;IACnE,OAAO,iBAAiB,IAAI,WAAW,IAAI,cAAc,IAAI,IAAI,IAAI,GAAG,CAAC;AAC3E,CAAC;AAED;;;AAGG;AACH,SAAS,mBAAmB,CAAC,GAAuB,EAAA;IAClD,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE;AACvB,QAAA,OAAO,EAAE,CAAC;KACX;AAED,IAAA,MAAM,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACpC,OAAOiB,sBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2B,CAAC;AACrE;;AC7MA;;;;;;;;;;;;;;;AAeG;AAmCH;;;AAGG;AACH,MAAM,wBAAwB,GAAG,GAAG,CAAC;AAErC,MAAM,4BAA4B,CAAA;AAAlC,IAAA,WAAA,GAAA;QACW,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;AACjD,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,CAAC;AACtB,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAmC,CAAC;QAC3D,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAE9E,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QACzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KAwHnD;IAtHC,MAAM,WAAW,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,OAAO,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SAC7C;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED,IAAA,UAAU,CAAC,IAAkB,EAAA;QAC3BnB,UAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD;IAED,MAAM,aAAa,CACjB,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;QAEhB,0BAA0B,CAAC,IAAI,CAAC,CAAC;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC7C,QAAA,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;;;;QAK5B,OAAO,CAAC,aAAa,EAAE,CAAC;AACxB,QAAA,sBAAsB,EAAE,CAAC;AAEzB,QAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAEnC,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzD,QAAA,MAAM,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7D,QAAA,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KACjD;IAED,4BAA4B,CAC1B,KAAmB,EACnB,GAAkC,EAAA;AAElC,QAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC5C;AAED,IAAA,iBAAiB,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;SAC5D;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C;IAEO,uBAAuB,CAC7B,IAAkB,EAClB,OAAyB,EAAA;;QAGzB,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;AAEtE,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,YAAW;;;AAG3C,YAAA,MAAM,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/B,YAAA,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;SACpC,EAAE,wBAAwB,CAAC,CAAC;AAE7B,QAAA,MAAM,gBAAgB,GAAG,OACvB,SAAwC,KACvB;;YAEjB,YAAY,CAAC,cAAc,CAAC,CAAC;AAE7B,YAAA,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,UAAU,GAAqB,IAAI,CAAC;YACxC,IAAI,YAAY,IAAI,SAAS,GAAG,KAAK,CAAC,EAAE;gBACtC,UAAU,GAAG,uBAAuB,CAAC,YAAY,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aACtE;;YAGD,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,eAAe,EAAE,CAAC,CAAC;AACnD,SAAC,CAAC;;QAGF,IACE,OAAO,cAAc,KAAK,WAAW;AACrC,YAAA,OAAO,cAAc,CAAC,SAAS,KAAK,UAAU,EAC9C;AACA,YAAA,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;SAClD;;;;;;QAOD,MAAM,qBAAqB,GAAG,aAAa,CAAC;QAC5C,MAAM,aAAa,GAAG,CAAA,EAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,CAAA,GAAA,CAAK,CAAC;QAClE,cAAc,EAAE,CAAC,aAAa,GAAG,OAAM,GAAG,KAAG;YAC3C,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;;;AAG/C,gBAAA,gBAAgB,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;aAC3B;;AAED,YAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;AAC/C,gBAAA,IAAI;oBACF,qBAAqB,CAAC,GAAG,CAAC,CAAC;iBAC5B;gBAAC,OAAO,CAAC,EAAE;;AAEV,oBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAClB;aACF;AACH,SAAC,CAAC;KACH;AACF,CAAA;AAED;;;;;AAKG;AACI,MAAM,4BAA4B,GACvC,6BAA6B;AAE/B,SAAS,eAAe,GAAA;IACtB,OAAO;AACL,QAAA,IAAI,EAAuB,SAAA;AAC3B,QAAA,OAAO,EAAE,IAAI;AACb,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,WAAW,EAAE,IAAI;AACjB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,QAAQ,EAAE,IAAI;QACd,KAAK,EAAEgB,iBAAY,CAA6B,eAAA,mCAAA;KACjD,CAAC;AACJ;;AC5MA;;;;;;;;;;;;;;;AAeG;AAqCH;AACA;AACA;AACgB,SAAA,sBAAsB,CAAC,IAAU,EAAE,SAAiB,EAAA;IAClEX,cAAS,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}