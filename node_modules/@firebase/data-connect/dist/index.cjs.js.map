{"version": 3, "file": "index.cjs.js", "sources": ["../src/core/version.ts", "../src/core/AppCheckTokenProvider.ts", "../src/core/error.ts", "../src/logger.ts", "../src/core/FirebaseAuthProvider.ts", "../src/api/Reference.ts", "../src/util/encoder.ts", "../src/util/map.ts", "../src/core/QueryManager.ts", "../src/network/transport/index.ts", "../src/util/url.ts", "../src/network/fetch.ts", "../src/network/transport/rest.ts", "../src/api/Mutation.ts", "../src/api/DataConnect.ts", "../src/register.ts", "../src/api/query.ts", "../src/util/validateArgs.ts", "../src/api.browser.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** The semver (www.semver.org) version of the SDK. */\nexport let SDK_VERSION = '';\n\n/**\n * SDK_VERSION should be set before any database instance is created\n * @internal\n */\nexport function setSDKVersion(version: string): void {\n  SDK_VERSION = version;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _isFirebaseServerApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  AppCheckTokenListener,\n  AppCheckTokenResult,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\n\n/**\n * @internal\n * Abstraction around AppCheck's token fetching capabilities.\n */\nexport class AppCheckTokenProvider {\n  private appCheck?: FirebaseAppCheckInternal;\n  private serverAppAppCheckToken?: string;\n  constructor(\n    app: FirebaseApp,\n    private appCheckProvider?: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.appCheck = appCheckProvider?.getImmediate({ optional: true });\n    if (!this.appCheck) {\n      void appCheckProvider\n        ?.get()\n        .then(appCheck => (this.appCheck = appCheck))\n        .catch();\n    }\n  }\n\n  getToken(): Promise<AppCheckTokenResult> {\n    if (this.serverAppAppCheckToken) {\n      return Promise.resolve({ token: this.serverAppAppCheckToken });\n    }\n\n    if (!this.appCheck) {\n      return new Promise<AppCheckTokenResult>((resolve, reject) => {\n        // Support delayed initialization of FirebaseAppCheck. This allows our\n        // customers to initialize the RTDB SDK before initializing Firebase\n        // AppCheck and ensures that all requests are authenticated if a token\n        // becomes available before the timoeout below expires.\n        setTimeout(() => {\n          if (this.appCheck) {\n            this.getToken().then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this.appCheck.getToken();\n  }\n\n  addTokenChangeListener(listener: AppCheckTokenListener): void {\n    void this.appCheckProvider\n      ?.get()\n      .then(appCheck => appCheck.addTokenListener(listener));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nexport type DataConnectErrorCode =\n  | 'other'\n  | 'already-initialized'\n  | 'not-initialized'\n  | 'not-supported'\n  | 'invalid-argument'\n  | 'partial-error'\n  | 'unauthorized';\n\nexport type Code = DataConnectErrorCode;\n\nexport const Code = {\n  OTHER: 'other' as DataConnectErrorCode,\n  ALREADY_INITIALIZED: 'already-initialized' as DataConnectErrorCode,\n  NOT_INITIALIZED: 'not-initialized' as DataConnect<PERSON><PERSON>rCode,\n  NOT_SUPPORTED: 'not-supported' as DataConnect<PERSON>rrorCode,\n  INVALID_ARGUMENT: 'invalid-argument' as DataConnectErrorCode,\n  PARTIAL_ERROR: 'partial-error' as DataConnectErrorCode,\n  UNAUTHORIZED: 'unauthorized' as DataConnectErrorCode\n};\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectError extends FirebaseError {\n  /** @internal */\n  readonly name: string = 'DataConnectError';\n\n  constructor(code: Code, message: string) {\n    super(code, message);\n\n    // Ensure the instanceof operator works as expected on subclasses of Error.\n    // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#custom_error_types\n    // and https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    Object.setPrototypeOf(this, DataConnectError.prototype);\n  }\n\n  /** @internal */\n  toString(): string {\n    return `${this.name}[code=${this.code}]: ${this.message}`;\n  }\n}\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectOperationError extends DataConnectError {\n  /** @internal */\n  readonly name: string = 'DataConnectOperationError';\n\n  /** The response received from the backend. */\n  readonly response: DataConnectOperationFailureResponse;\n\n  /** @hideconstructor */\n  constructor(message: string, response: DataConnectOperationFailureResponse) {\n    super(Code.PARTIAL_ERROR, message);\n    this.response = response;\n  }\n}\n\nexport interface DataConnectOperationFailureResponse {\n  // The \"data\" provided by the backend in the response message.\n  //\n  // Will be `undefined` if no \"data\" was provided in the response message.\n  // Otherwise, will be `null` if `null` was explicitly specified as the \"data\"\n  // in the response message. Otherwise, will be the value of the \"data\"\n  // specified as the \"data\" in the response message\n  readonly data?: Record<string, unknown> | null;\n\n  // The list of errors provided by the backend in the response message.\n  readonly errors: DataConnectOperationFailureResponseErrorInfo[];\n}\n\n// Information about the error, as provided in the response from the backend.\n// See https://spec.graphql.org/draft/#sec-Errors\nexport interface DataConnectOperationFailureResponseErrorInfo {\n  // The error message.\n  readonly message: string;\n\n  // The path of the field in the response data to which this error relates.\n  // String values in this array refer to field names. Numeric values in this\n  // array always satisfy `Number.isInteger()` and refer to the index in an\n  // array.\n  readonly path: Array<string | number>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Logger, LogLevelString } from '@firebase/logger';\n\nimport { SDK_VERSION } from './core/version';\n\nconst logger = new Logger('@firebase/data-connect');\nexport function setLogLevel(logLevel: LogLevelString): void {\n  logger.setLogLevel(logLevel);\n}\nexport function logDebug(msg: string): void {\n  logger.debug(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n\nexport function logError(msg: string): void {\n  logger.error(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from '@firebase/app-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName,\n  FirebaseAuthTokenData\n} from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { logDebug, logError } from '../logger';\n\n// @internal\nexport interface AuthTokenProvider {\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null>;\n  addTokenChangeListener(listener: AuthTokenListener): void;\n}\nexport type AuthTokenListener = (token: string | null) => void;\n\n// @internal\nexport class FirebaseAuthProvider implements AuthTokenProvider {\n  private _auth: FirebaseAuthInternal;\n  constructor(\n    private _appName: string,\n    private _options: FirebaseOptions,\n    private _authProvider: Provider<FirebaseAuthInternalName>\n  ) {\n    this._auth = _authProvider.getImmediate({ optional: true })!;\n    if (!this._auth) {\n      _authProvider.onInit(auth => (this._auth = auth));\n    }\n  }\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null> {\n    if (!this._auth) {\n      return new Promise((resolve, reject) => {\n        setTimeout(() => {\n          if (this._auth) {\n            this.getToken(forceRefresh).then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this._auth.getToken(forceRefresh).catch(error => {\n      if (error && error.code === 'auth/token-not-initialized') {\n        logDebug(\n          'Got auth/token-not-initialized error.  Treating as null token.'\n        );\n        return null;\n      } else {\n        logError(\n          'Error received when attempting to retrieve token: ' +\n            JSON.stringify(error)\n        );\n        return Promise.reject(error);\n      }\n    });\n  }\n  addTokenChangeListener(listener: AuthTokenListener): void {\n    this._auth?.addAuthTokenListener(listener);\n  }\n  removeTokenChangeListener(listener: (token: string | null) => void): void {\n    this._authProvider\n      .get()\n      .then(auth => auth.removeAuthTokenListener(listener))\n      .catch(err => logError(err));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnect, DataConnectOptions } from './DataConnect';\nexport const QUERY_STR = 'query';\nexport const MUTATION_STR = 'mutation';\nexport type ReferenceType = typeof QUERY_STR | typeof MUTATION_STR;\n\nexport const SOURCE_SERVER = 'SERVER';\nexport const SOURCE_CACHE = 'CACHE';\nexport type DataSource = typeof SOURCE_CACHE | typeof SOURCE_SERVER;\n\nexport interface OpResult<Data> {\n  data: Data;\n  source: DataSource;\n  fetchTime: string;\n}\n\nexport interface OperationRef<_Data, Variables> {\n  name: string;\n  variables: Variables;\n  refType: ReferenceType;\n  dataConnect: DataConnect;\n}\n\nexport interface DataConnectResult<Data, Variables> extends OpResult<Data> {\n  ref: OperationRef<Data, Variables>;\n  // future metadata\n}\n\n/**\n * Serialized RefInfo as a result of `QueryResult.toJSON().refInfo`\n */\nexport interface RefInfo<Variables> {\n  name: string;\n  variables: Variables;\n  connectorConfig: DataConnectOptions;\n}\n/**\n * Serialized Ref as a result of `QueryResult.toJSON()`\n */\nexport interface SerializedRef<Data, Variables> extends OpResult<Data> {\n  refInfo: RefInfo<Variables>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type HmacImpl = (obj: unknown) => string;\nexport let encoderImpl: HmacImpl;\nexport function setEncoder(encoder: HmacImpl): void {\n  encoderImpl = encoder;\n}\nsetEncoder(o => JSON.stringify(o));\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function setIfNotExists<T>(\n  map: Map<string, T>,\n  key: string,\n  val: T\n): void {\n  if (!map.has(key)) {\n    map.set(key, val);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DataConnectSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryPromise,\n  QueryRef,\n  QueryResult\n} from '../api/query';\nimport {\n  OperationRef,\n  QUERY_STR,\n  OpResult,\n  SerializedRef,\n  SOURCE_SERVER,\n  DataSource,\n  SOURCE_CACHE\n} from '../api/Reference';\nimport { logDebug } from '../logger';\nimport { DataConnectTransport } from '../network';\nimport { encoderImpl } from '../util/encoder';\nimport { setIfNotExists } from '../util/map';\n\nimport { Code, DataConnectError } from './error';\n\ninterface TrackedQuery<Data, Variables> {\n  ref: Omit<OperationRef<Data, Variables>, 'dataConnect'>;\n  subscriptions: Array<DataConnectSubscription<Data, Variables>>;\n  currentCache: OpResult<Data> | null;\n  lastError: DataConnectError | null;\n}\n\nfunction getRefSerializer<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>,\n  data: Data,\n  source: DataSource\n) {\n  return function toJSON(): SerializedRef<Data, Variables> {\n    return {\n      data,\n      refInfo: {\n        name: queryRef.name,\n        variables: queryRef.variables,\n        connectorConfig: {\n          projectId: queryRef.dataConnect.app.options.projectId!,\n          ...queryRef.dataConnect.getSettings()\n        }\n      },\n      fetchTime: Date.now().toLocaleString(),\n      source\n    };\n  };\n}\n\nexport class QueryManager {\n  _queries: Map<string, TrackedQuery<unknown, unknown>>;\n  constructor(private transport: DataConnectTransport) {\n    this._queries = new Map();\n  }\n  track<Data, Variables>(\n    queryName: string,\n    variables: Variables,\n    initialCache?: OpResult<Data>\n  ): TrackedQuery<Data, Variables> {\n    const ref: TrackedQuery<Data, Variables>['ref'] = {\n      name: queryName,\n      variables,\n      refType: QUERY_STR\n    };\n    const key = encoderImpl(ref);\n    const newTrackedQuery: TrackedQuery<Data, Variables> = {\n      ref,\n      subscriptions: [],\n      currentCache: initialCache || null,\n      lastError: null\n    };\n    // @ts-ignore\n    setIfNotExists(this._queries, key, newTrackedQuery);\n    return this._queries.get(key) as TrackedQuery<Data, Variables>;\n  }\n  addSubscription<Data, Variables>(\n    queryRef: OperationRef<Data, Variables>,\n    onResultCallback: OnResultSubscription<Data, Variables>,\n    onErrorCallback?: OnErrorSubscription,\n    initialCache?: OpResult<Data>\n  ): () => void {\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key) as TrackedQuery<\n      Data,\n      Variables\n    >;\n    const subscription = {\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback\n    };\n    const unsubscribe = (): void => {\n      const trackedQuery = this._queries.get(key)!;\n      trackedQuery.subscriptions = trackedQuery.subscriptions.filter(\n        sub => sub !== subscription\n      );\n    };\n    if (initialCache && trackedQuery.currentCache !== initialCache) {\n      logDebug('Initial cache found. Comparing dates.');\n      if (\n        !trackedQuery.currentCache ||\n        (trackedQuery.currentCache &&\n          compareDates(\n            trackedQuery.currentCache.fetchTime,\n            initialCache.fetchTime\n          ))\n      ) {\n        trackedQuery.currentCache = initialCache;\n      }\n    }\n    if (trackedQuery.currentCache !== null) {\n      const cachedData = trackedQuery.currentCache.data;\n      onResultCallback({\n        data: cachedData,\n        source: SOURCE_CACHE,\n        ref: queryRef as QueryRef<Data, Variables>,\n        toJSON: getRefSerializer(\n          queryRef as QueryRef<Data, Variables>,\n          trackedQuery.currentCache.data,\n          SOURCE_CACHE\n        ),\n        fetchTime: trackedQuery.currentCache.fetchTime\n      });\n      if (trackedQuery.lastError !== null && onErrorCallback) {\n        onErrorCallback(undefined);\n      }\n    }\n\n    trackedQuery.subscriptions.push({\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback,\n      unsubscribe\n    });\n    if (!trackedQuery.currentCache) {\n      logDebug(\n        `No cache available for query ${\n          queryRef.name\n        } with variables ${JSON.stringify(\n          queryRef.variables\n        )}. Calling executeQuery.`\n      );\n      const promise = this.executeQuery(queryRef as QueryRef<Data, Variables>);\n      // We want to ignore the error and let subscriptions handle it\n      promise.then(undefined, err => {});\n    }\n    return unsubscribe;\n  }\n  executeQuery<Data, Variables>(\n    queryRef: QueryRef<Data, Variables>\n  ): QueryPromise<Data, Variables> {\n    if (queryRef.refType !== QUERY_STR) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        `ExecuteQuery can only execute query operation`\n      );\n    }\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key)!;\n    const result = this.transport.invokeQuery<Data, Variables>(\n      queryRef.name,\n      queryRef.variables\n    );\n    const newR = result.then(\n      res => {\n        const fetchTime = new Date().toString();\n        const result: QueryResult<Data, Variables> = {\n          ...res,\n          source: SOURCE_SERVER,\n          ref: queryRef,\n          toJSON: getRefSerializer(queryRef, res.data, SOURCE_SERVER),\n          fetchTime\n        };\n        trackedQuery.subscriptions.forEach(subscription => {\n          subscription.userCallback(result);\n        });\n        trackedQuery.currentCache = {\n          data: res.data,\n          source: SOURCE_CACHE,\n          fetchTime\n        };\n        return result;\n      },\n      err => {\n        trackedQuery.lastError = err;\n        trackedQuery.subscriptions.forEach(subscription => {\n          if (subscription.errCallback) {\n            subscription.errCallback(err);\n          }\n        });\n        throw err;\n      }\n    );\n\n    return newR;\n  }\n  enableEmulator(host: string, port: number): void {\n    this.transport.useEmulator(host, port);\n  }\n}\nfunction compareDates(str1: string, str2: string): boolean {\n  const date1 = new Date(str1);\n  const date2 = new Date(str2);\n  return date1.getTime() < date2.getTime();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\n\n/**\n * enum representing different flavors of the SDK used by developers\n * use the CallerSdkType for type-checking, and the CallerSdkTypeEnum for value-checking/assigning\n */\nexport type CallerSdkType =\n  | 'Base' // Core JS SDK\n  | 'Generated' // Generated JS SDK\n  | 'TanstackReactCore' // Tanstack non-generated React SDK\n  | 'GeneratedReact' // Generated React SDK\n  | 'TanstackAngularCore' // Tanstack non-generated Angular SDK\n  | 'GeneratedAngular'; // Generated Angular SDK\nexport const CallerSdkTypeEnum = {\n  Base: 'Base', // Core JS SDK\n  Generated: 'Generated', // Generated JS SDK\n  TanstackReactCore: 'TanstackReactCore', // Tanstack non-generated React SDK\n  GeneratedReact: 'GeneratedReact', // Tanstack non-generated Angular SDK\n  TanstackAngularCore: 'TanstackAngularCore', // Tanstack non-generated Angular SDK\n  GeneratedAngular: 'GeneratedAngular' // Generated Angular SDK\n} as const;\n\n/**\n * @internal\n */\nexport interface DataConnectTransport {\n  invokeQuery<T, U>(\n    queryName: string,\n    body?: U\n  ): Promise<{ data: T; errors: Error[] }>;\n  invokeMutation<T, U>(\n    queryName: string,\n    body?: U\n  ): Promise<{ data: T; errors: Error[] }>;\n  useEmulator(host: string, port?: number, sslEnabled?: boolean): void;\n  onTokenChanged: (token: string | null) => void;\n  _setCallerSdkType(callerSdkType: CallerSdkType): void;\n}\n\n/**\n * @internal\n */\nexport type TransportClass = new (\n  options: DataConnectOptions,\n  apiKey?: string,\n  appId?: string,\n  authProvider?: AuthTokenProvider,\n  appCheckProvider?: AppCheckTokenProvider,\n  transportOptions?: TransportOptions,\n  _isUsingGen?: boolean,\n  _callerSdkType?: CallerSdkType\n) => DataConnectTransport;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\nimport { logError } from '../logger';\n\nexport function urlBuilder(\n  projectConfig: DataConnectOptions,\n  transportOptions: TransportOptions\n): string {\n  const { connector, location, projectId: project, service } = projectConfig;\n  const { host, sslEnabled, port } = transportOptions;\n  const protocol = sslEnabled ? 'https' : 'http';\n  const realHost = host || `firebasedataconnect.googleapis.com`;\n  let baseUrl = `${protocol}://${realHost}`;\n  if (typeof port === 'number') {\n    baseUrl += `:${port}`;\n  } else if (typeof port !== 'undefined') {\n    logError('Port type is of an invalid type');\n    throw new DataConnectError(\n      Code.INVALID_ARGUMENT,\n      'Incorrect type for port passed in!'\n    );\n  }\n  return `${baseUrl}/v1/projects/${project}/locations/${location}/services/${service}/connectors/${connector}`;\n}\nexport function addToken(url: string, apiKey?: string): string {\n  if (!apiKey) {\n    return url;\n  }\n  const newUrl = new URL(url);\n  newUrl.searchParams.append('key', apiKey);\n  return newUrl.toString();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { isCloudWorkstation } from '@firebase/util';\n\nimport {\n  Code,\n  DataConnectError,\n  DataConnectOperationError,\n  DataConnectOperationFailureResponse\n} from '../core/error';\nimport { SDK_VERSION } from '../core/version';\nimport { logError } from '../logger';\n\nimport { CallerSdkType, CallerSdkTypeEnum } from './transport';\n\nlet connectFetch: typeof fetch | null = globalThis.fetch;\nexport function initializeFetch(fetchImpl: typeof fetch): void {\n  connectFetch = fetchImpl;\n}\nfunction getGoogApiClientValue(\n  _isUsingGen: boolean,\n  _callerSdkType: CallerSdkType\n): string {\n  let str = 'gl-js/ fire/' + SDK_VERSION;\n  if (\n    _callerSdkType !== CallerSdkTypeEnum.Base &&\n    _callerSdkType !== CallerSdkTypeEnum.Generated\n  ) {\n    str += ' js/' + _callerSdkType.toLowerCase();\n  } else if (_isUsingGen || _callerSdkType === CallerSdkTypeEnum.Generated) {\n    str += ' js/gen';\n  }\n  return str;\n}\nexport interface DataConnectFetchBody<T> {\n  name: string;\n  operationName: string;\n  variables: T;\n}\nexport function dcFetch<T, U>(\n  url: string,\n  body: DataConnectFetchBody<U>,\n  { signal }: AbortController,\n  appId: string | null,\n  accessToken: string | null,\n  appCheckToken: string | null,\n  _isUsingGen: boolean,\n  _callerSdkType: CallerSdkType,\n  _isUsingEmulator: boolean\n): Promise<{ data: T; errors: Error[] }> {\n  if (!connectFetch) {\n    throw new DataConnectError(Code.OTHER, 'No Fetch Implementation detected!');\n  }\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json',\n    'X-Goog-Api-Client': getGoogApiClientValue(_isUsingGen, _callerSdkType)\n  };\n  if (accessToken) {\n    headers['X-Firebase-Auth-Token'] = accessToken;\n  }\n  if (appId) {\n    headers['x-firebase-gmpid'] = appId;\n  }\n  if (appCheckToken) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n  const bodyStr = JSON.stringify(body);\n  const fetchOptions: RequestInit = {\n    body: bodyStr,\n    method: 'POST',\n    headers,\n    signal\n  };\n  if (isCloudWorkstation(url) && _isUsingEmulator) {\n    fetchOptions.credentials = 'include';\n  }\n\n  return connectFetch(url, fetchOptions)\n    .catch(err => {\n      throw new DataConnectError(\n        Code.OTHER,\n        'Failed to fetch: ' + JSON.stringify(err)\n      );\n    })\n    .then(async response => {\n      let jsonResponse = null;\n      try {\n        jsonResponse = await response.json();\n      } catch (e) {\n        throw new DataConnectError(Code.OTHER, JSON.stringify(e));\n      }\n      const message = getMessage(jsonResponse);\n      if (response.status >= 400) {\n        logError(\n          'Error while performing request: ' + JSON.stringify(jsonResponse)\n        );\n        if (response.status === 401) {\n          throw new DataConnectError(Code.UNAUTHORIZED, message);\n        }\n        throw new DataConnectError(Code.OTHER, message);\n      }\n      return jsonResponse;\n    })\n    .then(res => {\n      if (res.errors && res.errors.length) {\n        const stringified = JSON.stringify(res.errors);\n        const response: DataConnectOperationFailureResponse = {\n          errors: res.errors,\n          data: res.data\n        };\n        throw new DataConnectOperationError(\n          'DataConnect error while performing request: ' + stringified,\n          response\n        );\n      }\n      return res;\n    });\n}\ninterface MessageObject {\n  message?: string;\n}\nfunction getMessage(obj: MessageObject): string {\n  if ('message' in obj) {\n    return obj.message;\n  }\n  return JSON.stringify(obj);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { DataConnectError, Code } from '../../core/error';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\nimport { logDebug } from '../../logger';\nimport { addToken, urlBuilder } from '../../util/url';\nimport { dcFetch } from '../fetch';\n\nimport { CallerSdkType, CallerSdkTypeEnum, DataConnectTransport } from '.';\n\nexport class RESTTransport implements DataConnectTransport {\n  private _host = '';\n  private _port: number | undefined;\n  private _location = 'l';\n  private _connectorName = '';\n  private _secure = true;\n  private _project = 'p';\n  private _serviceName: string;\n  private _accessToken: string | null = null;\n  private _appCheckToken: string | null = null;\n  private _lastToken: string | null = null;\n  private _isUsingEmulator = false;\n  constructor(\n    options: DataConnectOptions,\n    private apiKey?: string | undefined,\n    private appId?: string,\n    private authProvider?: AuthTokenProvider | undefined,\n    private appCheckProvider?: AppCheckTokenProvider | undefined,\n    transportOptions?: TransportOptions | undefined,\n    private _isUsingGen = false,\n    private _callerSdkType: CallerSdkType = CallerSdkTypeEnum.Base\n  ) {\n    if (transportOptions) {\n      if (typeof transportOptions.port === 'number') {\n        this._port = transportOptions.port;\n      }\n      if (typeof transportOptions.sslEnabled !== 'undefined') {\n        this._secure = transportOptions.sslEnabled;\n      }\n      this._host = transportOptions.host;\n    }\n    const { location, projectId: project, connector, service } = options;\n    if (location) {\n      this._location = location;\n    }\n    if (project) {\n      this._project = project;\n    }\n    this._serviceName = service;\n    if (!connector) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        'Connector Name required!'\n      );\n    }\n    this._connectorName = connector;\n    this.authProvider?.addTokenChangeListener(token => {\n      logDebug(`New Token Available: ${token}`);\n      this._accessToken = token;\n    });\n    this.appCheckProvider?.addTokenChangeListener(result => {\n      const { token } = result;\n      logDebug(`New App Check Token Available: ${token}`);\n      this._appCheckToken = token;\n    });\n  }\n  get endpointUrl(): string {\n    return urlBuilder(\n      {\n        connector: this._connectorName,\n        location: this._location,\n        projectId: this._project,\n        service: this._serviceName\n      },\n      { host: this._host, sslEnabled: this._secure, port: this._port }\n    );\n  }\n  useEmulator(host: string, port?: number, isSecure?: boolean): void {\n    this._host = host;\n    this._isUsingEmulator = true;\n    if (typeof port === 'number') {\n      this._port = port;\n    }\n    if (typeof isSecure !== 'undefined') {\n      this._secure = isSecure;\n    }\n  }\n  onTokenChanged(newToken: string | null): void {\n    this._accessToken = newToken;\n  }\n\n  async getWithAuth(forceToken = false): Promise<string> {\n    let starterPromise: Promise<string | null> = new Promise(resolve =>\n      resolve(this._accessToken)\n    );\n    if (this.appCheckProvider) {\n      this._appCheckToken = (await this.appCheckProvider.getToken())?.token;\n    }\n    if (this.authProvider) {\n      starterPromise = this.authProvider\n        .getToken(/*forceToken=*/ forceToken)\n        .then(data => {\n          if (!data) {\n            return null;\n          }\n          this._accessToken = data.accessToken;\n          return this._accessToken;\n        });\n    } else {\n      starterPromise = new Promise(resolve => resolve(''));\n    }\n    return starterPromise;\n  }\n\n  _setLastToken(lastToken: string | null): void {\n    this._lastToken = lastToken;\n  }\n\n  withRetry<T>(\n    promiseFactory: () => Promise<{ data: T; errors: Error[] }>,\n    retry = false\n  ): Promise<{ data: T; errors: Error[] }> {\n    let isNewToken = false;\n    return this.getWithAuth(retry)\n      .then(res => {\n        isNewToken = this._lastToken !== res;\n        this._lastToken = res;\n        return res;\n      })\n      .then(promiseFactory)\n      .catch(err => {\n        // Only retry if the result is unauthorized and the last token isn't the same as the new one.\n        if (\n          'code' in err &&\n          err.code === Code.UNAUTHORIZED &&\n          !retry &&\n          isNewToken\n        ) {\n          logDebug('Retrying due to unauthorized');\n          return this.withRetry(promiseFactory, true);\n        }\n        throw err;\n      });\n  }\n\n  // TODO(mtewani): Update U to include shape of body defined in line 13.\n  invokeQuery: <T, U>(\n    queryName: string,\n    body?: U\n  ) => Promise<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    queryName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n\n    // TODO(mtewani): Update to proper value\n    const withAuth = this.withRetry(() =>\n      dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeQuery`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: queryName,\n          variables: body\n        },\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen,\n        this._callerSdkType,\n        this._isUsingEmulator\n      )\n    );\n    return withAuth;\n  };\n  invokeMutation: <T, U>(\n    queryName: string,\n    body?: U\n  ) => Promise<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    mutationName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n    const taskResult = this.withRetry(() => {\n      return dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeMutation`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: mutationName,\n          variables: body\n        },\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen,\n        this._callerSdkType,\n        this._isUsingEmulator\n      );\n    });\n    return taskResult;\n  };\n\n  _setCallerSdkType(callerSdkType: CallerSdkType): void {\n    this._callerSdkType = callerSdkType;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectTransport } from '../network/transport';\n\nimport { DataConnect } from './DataConnect';\nimport {\n  DataConnectResult,\n  MUTATION_STR,\n  OperationRef,\n  SOURCE_SERVER\n} from './Reference';\n\nexport interface MutationRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof MUTATION_STR;\n}\n\n/**\n * Creates a `MutationRef`\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n */\nexport function mutationRef<Data>(\n  dcInstance: DataConnect,\n  mutationName: string\n): MutationRef<Data, undefined>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables: Variables\n): MutationRef<Data, Variables>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n * @returns `MutationRef`\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables?: Variables\n): MutationRef<Data, Variables> {\n  dcInstance.setInitialized();\n  const ref: MutationRef<Data, Variables> = {\n    dataConnect: dcInstance,\n    name: mutationName,\n    refType: MUTATION_STR,\n    variables: variables as Variables\n  };\n  return ref;\n}\n\n/**\n * @internal\n */\nexport class MutationManager {\n  private _inflight: Array<Promise<unknown>> = [];\n  constructor(private _transport: DataConnectTransport) {}\n  executeMutation<Data, Variables>(\n    mutationRef: MutationRef<Data, Variables>\n  ): MutationPromise<Data, Variables> {\n    const result = this._transport.invokeMutation<Data, Variables>(\n      mutationRef.name,\n      mutationRef.variables\n    );\n    const withRefPromise = result.then(res => {\n      const obj: MutationResult<Data, Variables> = {\n        ...res, // Double check that the result is result.data, not just result\n        source: SOURCE_SERVER,\n        ref: mutationRef,\n        fetchTime: Date.now().toLocaleString()\n      };\n      return obj;\n    });\n    this._inflight.push(result);\n    const removePromise = (): Array<Promise<unknown>> =>\n      (this._inflight = this._inflight.filter(promise => promise !== result));\n    result.then(removePromise, removePromise);\n    return withRefPromise;\n  }\n}\n\n/**\n * Mutation Result from `executeMutation`\n */\nexport interface MutationResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: MutationRef<Data, Variables>;\n}\n/**\n * Mutation return value from `executeMutation`\n */\nexport interface MutationPromise<Data, Variables>\n  extends Promise<MutationResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Mutation\n * @param mutationRef mutation to execute\n * @returns `MutationRef`\n */\nexport function executeMutation<Data, Variables>(\n  mutationRef: MutationRef<Data, Variables>\n): MutationPromise<Data, Variables> {\n  return mutationRef.dataConnect._mutationManager.executeMutation(mutationRef);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  _getProvider,\n  _removeServiceInstance,\n  getApp\n} from '@firebase/app';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\nimport {\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\n\nimport { AppCheckTokenProvider } from '../core/AppCheckTokenProvider';\nimport { Code, DataConnectError } from '../core/error';\nimport {\n  AuthTokenProvider,\n  FirebaseAuthProvider\n} from '../core/FirebaseAuthProvider';\nimport { QueryManager } from '../core/QueryManager';\nimport { logDebug, logError } from '../logger';\nimport {\n  CallerSdkType,\n  CallerSdkTypeEnum,\n  DataConnectTransport,\n  TransportClass\n} from '../network';\nimport { RESTTransport } from '../network/transport/rest';\n\nimport { MutationManager } from './Mutation';\n\n/**\n * Connector Config for calling Data Connect backend.\n */\nexport interface ConnectorConfig {\n  location: string;\n  connector: string;\n  service: string;\n}\n\n/**\n * Options to connect to emulator\n */\nexport interface TransportOptions {\n  host: string;\n  sslEnabled?: boolean;\n  port?: number;\n}\n\nconst FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR =\n  'FIREBASE_DATA_CONNECT_EMULATOR_HOST';\n\n/**\n *\n * @param fullHost\n * @returns TransportOptions\n * @internal\n */\nexport function parseOptions(fullHost: string): TransportOptions {\n  const [protocol, hostName] = fullHost.split('://');\n  const isSecure = protocol === 'https';\n  const [host, portAsString] = hostName.split(':');\n  const port = Number(portAsString);\n  return { host, port, sslEnabled: isSecure };\n}\n/**\n * DataConnectOptions including project id\n */\nexport interface DataConnectOptions extends ConnectorConfig {\n  projectId: string;\n}\n\n/**\n * Class representing Firebase Data Connect\n */\nexport class DataConnect {\n  _queryManager!: QueryManager;\n  _mutationManager!: MutationManager;\n  isEmulator = false;\n  _initialized = false;\n  private _transport!: DataConnectTransport;\n  private _transportClass: TransportClass | undefined;\n  private _transportOptions?: TransportOptions;\n  private _authTokenProvider?: AuthTokenProvider;\n  _isUsingGeneratedSdk: boolean = false;\n  _callerSdkType: CallerSdkType = CallerSdkTypeEnum.Base;\n  private _appCheckTokenProvider?: AppCheckTokenProvider;\n  // @internal\n  constructor(\n    public readonly app: FirebaseApp,\n    // TODO(mtewani): Replace with _dataConnectOptions in the future\n    private readonly dataConnectOptions: DataConnectOptions,\n    private readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    private readonly _appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (typeof process !== 'undefined' && process.env) {\n      const host = process.env[FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR];\n      if (host) {\n        logDebug('Found custom host. Using emulator');\n        this.isEmulator = true;\n        this._transportOptions = parseOptions(host);\n      }\n    }\n  }\n  // @internal\n  _useGeneratedSdk(): void {\n    if (!this._isUsingGeneratedSdk) {\n      this._isUsingGeneratedSdk = true;\n    }\n  }\n  _setCallerSdkType(callerSdkType: CallerSdkType): void {\n    this._callerSdkType = callerSdkType;\n    if (this._initialized) {\n      this._transport._setCallerSdkType(callerSdkType);\n    }\n  }\n  _delete(): Promise<void> {\n    _removeServiceInstance(\n      this.app,\n      'data-connect',\n      JSON.stringify(this.getSettings())\n    );\n    return Promise.resolve();\n  }\n\n  // @internal\n  getSettings(): ConnectorConfig {\n    const copy = JSON.parse(JSON.stringify(this.dataConnectOptions));\n    delete copy.projectId;\n    return copy;\n  }\n\n  // @internal\n  setInitialized(): void {\n    if (this._initialized) {\n      return;\n    }\n    if (this._transportClass === undefined) {\n      logDebug('transportClass not provided. Defaulting to RESTTransport.');\n      this._transportClass = RESTTransport;\n    }\n\n    if (this._authProvider) {\n      this._authTokenProvider = new FirebaseAuthProvider(\n        this.app.name,\n        this.app.options,\n        this._authProvider\n      );\n    }\n    if (this._appCheckProvider) {\n      this._appCheckTokenProvider = new AppCheckTokenProvider(\n        this.app,\n        this._appCheckProvider\n      );\n    }\n\n    this._initialized = true;\n    this._transport = new this._transportClass(\n      this.dataConnectOptions,\n      this.app.options.apiKey,\n      this.app.options.appId,\n      this._authTokenProvider,\n      this._appCheckTokenProvider,\n      undefined,\n      this._isUsingGeneratedSdk,\n      this._callerSdkType\n    );\n    if (this._transportOptions) {\n      this._transport.useEmulator(\n        this._transportOptions.host,\n        this._transportOptions.port,\n        this._transportOptions.sslEnabled\n      );\n    }\n    this._queryManager = new QueryManager(this._transport);\n    this._mutationManager = new MutationManager(this._transport);\n  }\n\n  // @internal\n  enableEmulator(transportOptions: TransportOptions): void {\n    if (\n      this._initialized &&\n      !areTransportOptionsEqual(this._transportOptions, transportOptions)\n    ) {\n      logError('enableEmulator called after initialization');\n      throw new DataConnectError(\n        Code.ALREADY_INITIALIZED,\n        'DataConnect instance already initialized!'\n      );\n    }\n    this._transportOptions = transportOptions;\n    this.isEmulator = true;\n  }\n}\n\n/**\n * @internal\n * @param transportOptions1\n * @param transportOptions2\n * @returns\n */\nexport function areTransportOptionsEqual(\n  transportOptions1: TransportOptions,\n  transportOptions2: TransportOptions\n): boolean {\n  return (\n    transportOptions1.host === transportOptions2.host &&\n    transportOptions1.port === transportOptions2.port &&\n    transportOptions1.sslEnabled === transportOptions2.sslEnabled\n  );\n}\n\n/**\n * Connect to the DataConnect Emulator\n * @param dc Data Connect instance\n * @param host host of emulator server\n * @param port port of emulator server\n * @param sslEnabled use https\n */\nexport function connectDataConnectEmulator(\n  dc: DataConnect,\n  host: string,\n  port?: number,\n  sslEnabled = false\n): void {\n  // Workaround to get cookies in Firebase Studio\n  if (isCloudWorkstation(host)) {\n    void pingServer(`https://${host}${port ? `:${port}` : ''}`);\n    updateEmulatorBanner('Data Connect', true);\n  }\n  dc.enableEmulator({ host, port, sslEnabled });\n}\n\n/**\n * Initialize DataConnect instance\n * @param options ConnectorConfig\n */\nexport function getDataConnect(options: ConnectorConfig): DataConnect;\n/**\n * Initialize DataConnect instance\n * @param app FirebaseApp to initialize to.\n * @param options ConnectorConfig\n */\nexport function getDataConnect(\n  app: FirebaseApp,\n  options: ConnectorConfig\n): DataConnect;\nexport function getDataConnect(\n  appOrOptions: FirebaseApp | ConnectorConfig,\n  optionalOptions?: ConnectorConfig\n): DataConnect {\n  let app: FirebaseApp;\n  let dcOptions: ConnectorConfig;\n  if ('location' in appOrOptions) {\n    dcOptions = appOrOptions;\n    app = getApp();\n  } else {\n    dcOptions = optionalOptions!;\n    app = appOrOptions;\n  }\n\n  if (!app || Object.keys(app).length === 0) {\n    app = getApp();\n  }\n  const provider = _getProvider(app, 'data-connect');\n  const identifier = JSON.stringify(dcOptions);\n  if (provider.isInitialized(identifier)) {\n    const dcInstance = provider.getImmediate({ identifier });\n    const options = provider.getOptions(identifier);\n    const optionsValid = Object.keys(options).length > 0;\n    if (optionsValid) {\n      logDebug('Re-using cached instance');\n      return dcInstance;\n    }\n  }\n  validateDCOptions(dcOptions);\n\n  logDebug('Creating new DataConnect instance');\n  // Initialize with options.\n  return provider.initialize({\n    instanceIdentifier: identifier,\n    options: dcOptions\n  });\n}\n\n/**\n *\n * @param dcOptions\n * @returns {void}\n * @internal\n */\nexport function validateDCOptions(dcOptions: ConnectorConfig): boolean {\n  const fields = ['connector', 'location', 'service'];\n  if (!dcOptions) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'DC Option Required');\n  }\n  fields.forEach(field => {\n    if (dcOptions[field] === null || dcOptions[field] === undefined) {\n      throw new DataConnectError(Code.INVALID_ARGUMENT, `${field} Required`);\n    }\n  });\n  return true;\n}\n\n/**\n * Delete DataConnect instance\n * @param dataConnect DataConnect instance\n * @returns\n */\nexport function terminate(dataConnect: DataConnect): Promise<void> {\n  return dataConnect._delete();\n  // TODO(mtewani): Stop pending tasks\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { Component, ComponentType } from '@firebase/component';\n\nimport { name, version } from '../package.json';\nimport { setSDKVersion } from '../src/core/version';\n\nimport { DataConnect, ConnectorConfig } from './api/DataConnect';\nimport { Code, DataConnectError } from './core/error';\n\nexport function registerDataConnect(variant?: string): void {\n  setSDKVersion(SDK_VERSION);\n  _registerComponent(\n    new Component(\n      'data-connect',\n      (container, { instanceIdentifier: settings, options }) => {\n        const app = container.getProvider('app').getImmediate()!;\n        const authProvider = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        let newOpts = options as ConnectorConfig;\n        if (settings) {\n          newOpts = JSON.parse(settings);\n        }\n        if (!app.options.projectId) {\n          throw new DataConnectError(\n            Code.INVALID_ARGUMENT,\n            'Project ID must be provided. Did you pass in a proper projectId to initializeApp?'\n          );\n        }\n        return new DataConnect(\n          app,\n          { ...newOpts, projectId: app.options.projectId! },\n          authProvider,\n          appCheckProvider\n        );\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectError } from '../core/error';\n\nimport { DataConnect, getDataConnect } from './DataConnect';\nimport {\n  OperationRef,\n  QUERY_STR,\n  DataConnectResult,\n  SerializedRef\n} from './Reference';\n\n/**\n * Signature for `OnResultSubscription` for `subscribe`\n */\nexport type OnResultSubscription<Data, Variables> = (\n  res: QueryResult<Data, Variables>\n) => void;\n/**\n * Signature for `OnErrorSubscription` for `subscribe`\n */\nexport type OnErrorSubscription = (err?: DataConnectError) => void;\n/**\n * Signature for unsubscribe from `subscribe`\n */\nexport type QueryUnsubscribe = () => void;\n/**\n * Representation of user provided subscription options.\n */\nexport interface DataConnectSubscription<Data, Variables> {\n  userCallback: OnResultSubscription<Data, Variables>;\n  errCallback?: (e?: DataConnectError) => void;\n  unsubscribe: () => void;\n}\n\n/**\n * QueryRef object\n */\nexport interface QueryRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof QUERY_STR;\n}\n/**\n * Result of `executeQuery`\n */\nexport interface QueryResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: QueryRef<Data, Variables>;\n  toJSON: () => SerializedRef<Data, Variables>;\n}\n/**\n * Promise returned from `executeQuery`\n */\nexport interface QueryPromise<Data, Variables>\n  extends Promise<QueryResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Query\n * @param queryRef query to execute.\n * @returns `QueryPromise`\n */\nexport function executeQuery<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>\n): QueryPromise<Data, Variables> {\n  return queryRef.dataConnect._queryManager.executeQuery(queryRef);\n}\n\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @returns `QueryRef`\n */\nexport function queryRef<Data>(\n  dcInstance: DataConnect,\n  queryName: string\n): QueryRef<Data, undefined>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables: Variables\n): QueryRef<Data, Variables>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @param initialCache initial cache to use for client hydration\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables?: Variables,\n  initialCache?: QueryResult<Data, Variables>\n): QueryRef<Data, Variables> {\n  dcInstance.setInitialized();\n  dcInstance._queryManager.track(queryName, variables, initialCache);\n  return {\n    dataConnect: dcInstance,\n    refType: QUERY_STR,\n    name: queryName,\n    variables\n  };\n}\n/**\n * Converts serialized ref to query ref\n * @param serializedRef ref to convert to `QueryRef`\n * @returns `QueryRef`\n */\nexport function toQueryRef<Data, Variables>(\n  serializedRef: SerializedRef<Data, Variables>\n): QueryRef<Data, Variables> {\n  const {\n    refInfo: { name, variables, connectorConfig }\n  } = serializedRef;\n  return queryRef(getDataConnect(connectorConfig), name, variables);\n}\n/**\n * `OnCompleteSubscription`\n */\nexport type OnCompleteSubscription = () => void;\n/**\n * Representation of full observer options in `subscribe`\n */\nexport interface SubscriptionOptions<Data, Variables> {\n  onNext?: OnResultSubscription<Data, Variables>;\n  onErr?: OnErrorSubscription;\n  onComplete?: OnCompleteSubscription;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ConnectorConfig,\n  DataConnect,\n  getDataConnect\n} from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\ninterface ParsedArgs<Variables> {\n  dc: DataConnect;\n  vars: Variables;\n}\n\n/**\n * The generated SDK will allow the user to pass in either the variable or the data connect instance with the variable,\n * and this function validates the variables and returns back the DataConnect instance and variables based on the arguments passed in.\n * @param connectorConfig\n * @param dcOrVars\n * @param vars\n * @param validateVars\n * @returns {DataConnect} and {Variables} instance\n * @internal\n */\nexport function validateArgs<Variables extends object>(\n  connectorConfig: ConnectorConfig,\n  dcOrVars?: DataConnect | Variables,\n  vars?: Variables,\n  validateVars?: boolean\n): ParsedArgs<Variables> {\n  let dcInstance: DataConnect;\n  let realVars: Variables;\n  if (dcOrVars && 'enableEmulator' in dcOrVars) {\n    dcInstance = dcOrVars as DataConnect;\n    realVars = vars;\n  } else {\n    dcInstance = getDataConnect(connectorConfig);\n    realVars = dcOrVars as Variables;\n  }\n  if (!dcInstance || (!realVars && validateVars)) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Variables required.');\n  }\n  return { dc: dcInstance, vars: realVars };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OnCompleteSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryRef,\n  QueryUnsubscribe,\n  SubscriptionOptions,\n  toQueryRef\n} from './api/query';\nimport { OpResult, SerializedRef } from './api/Reference';\nimport { DataConnectError, Code } from './core/error';\n\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observer observer object to use for subscribing.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observer: SubscriptionOptions<Data, Variables>\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param onNext Callback to call when result comes back.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  onNext: OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observerOrOnNext observer object or next function.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observerOrOnNext:\n    | SubscriptionOptions<Data, Variables>\n    | OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe {\n  let ref: QueryRef<Data, Variables>;\n  let initialCache: OpResult<Data> | undefined;\n  if ('refInfo' in queryRefOrSerializedResult) {\n    const serializedRef: SerializedRef<Data, Variables> =\n      queryRefOrSerializedResult;\n    const { data, source, fetchTime } = serializedRef;\n    initialCache = {\n      data,\n      source,\n      fetchTime\n    };\n    ref = toQueryRef(serializedRef);\n  } else {\n    ref = queryRefOrSerializedResult;\n  }\n  let onResult: OnResultSubscription<Data, Variables> | undefined = undefined;\n  if (typeof observerOrOnNext === 'function') {\n    onResult = observerOrOnNext;\n  } else {\n    onResult = observerOrOnNext.onNext;\n    onError = observerOrOnNext.onErr;\n    onComplete = observerOrOnNext.onComplete;\n  }\n  if (!onResult) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Must provide onNext');\n  }\n  return ref.dataConnect._queryManager.addSubscription(\n    ref,\n    onResult,\n    onError,\n    initialCache\n  );\n}\n", "/**\n * Firebase Data Connect\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DataConnect } from './api/DataConnect';\nimport { registerDataConnect } from './register';\n\nexport * from './api';\nexport * from './api.browser';\n\nregisterDataConnect();\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'data-connect': DataConnect;\n  }\n}\n"], "names": ["app", "_isFirebaseServerApp", "FirebaseError", "<PERSON><PERSON>", "isCloudWorkstation", "_removeServiceInstance", "pingServer", "updateEmulatorBanner", "getApp", "_get<PERSON><PERSON><PERSON>", "SDK_VERSION", "_registerComponent", "Component", "registerVersion"], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;AACO,IAAI,WAAW,GAAG,EAAE,CAAC;AAE5B;;;AAGG;AACG,SAAU,aAAa,CAAC,OAAe,EAAA;IAC3C,WAAW,GAAG,OAAO,CAAC;AACxB;;AC1BA;;;;;;;;;;;;;;;AAeG;AAWH;;;AAGG;MACU,qBAAqB,CAAA;IAGhC,WACE,CAAAA,KAAgB,EACR,gBAA0D,EAAA;QAA1D,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA0C;QAElE,IAAIC,wBAAoB,CAACD,KAAG,CAAC,IAAIA,KAAG,CAAC,QAAQ,CAAC,aAAa,EAAE;YAC3D,IAAI,CAAC,sBAAsB,GAAGA,KAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;SAC1D;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,KAAK,gBAAgB;AACnB,kBAAE,GAAG,EAAE;AACN,iBAAA,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAC5C,iBAAA,KAAK,EAAE,CAAC;SACZ;KACF;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC/B,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;SAChE;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO,IAAI,OAAO,CAAsB,CAAC,OAAO,EAAE,MAAM,KAAI;;;;;gBAK1D,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjB,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;qBACvC;yBAAM;wBACL,OAAO,CAAC,IAAI,CAAC,CAAC;qBACf;iBACF,EAAE,CAAC,CAAC,CAAC;AACR,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;KACjC;AAED,IAAA,sBAAsB,CAAC,QAA+B,EAAA;QACpD,KAAK,IAAI,CAAC,gBAAgB;AACxB,cAAE,GAAG,EAAE;AACN,aAAA,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC1D;AACF;;AC7ED;;;;;;;;;;;;;;;AAeG;AAeU,MAAA,IAAI,GAAG;AAClB,IAAA,KAAK,EAAE,OAA+B;AACtC,IAAA,mBAAmB,EAAE,qBAA6C;AAClE,IAAA,eAAe,EAAE,iBAAyC;AAC1D,IAAA,aAAa,EAAE,eAAuC;AACtD,IAAA,gBAAgB,EAAE,kBAA0C;AAC5D,IAAA,aAAa,EAAE,eAAuC;AACtD,IAAA,YAAY,EAAE,cAAsC;EACpD;AAEF;AACM,MAAO,gBAAiB,SAAQE,kBAAa,CAAA;IAIjD,WAAY,CAAA,IAAU,EAAE,OAAe,EAAA;AACrC,QAAA,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;QAHd,IAAI,CAAA,IAAA,GAAW,kBAAkB,CAAC;;;;QAQzC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;KACzD;;IAGD,QAAQ,GAAA;AACN,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,IAAI,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAM,GAAA,EAAA,IAAI,CAAC,OAAO,EAAE,CAAC;KAC3D;AACF,CAAA;AAED;AACM,MAAO,yBAA0B,SAAQ,gBAAgB,CAAA;;IAQ7D,WAAY,CAAA,OAAe,EAAE,QAA6C,EAAA;AACxE,QAAA,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;;QAP5B,IAAI,CAAA,IAAA,GAAW,2BAA2B,CAAC;AAQlD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC1B;AACF;;ACzED;;;;;;;;;;;;;;;AAeG;AAKH,MAAM,MAAM,GAAG,IAAIC,eAAM,CAAC,wBAAwB,CAAC,CAAC;AAC9C,SAAU,WAAW,CAAC,QAAwB,EAAA;AAClD,IAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC;AACK,SAAU,QAAQ,CAAC,GAAW,EAAA;IAClC,MAAM,CAAC,KAAK,CAAC,CAAA,aAAA,EAAgB,WAAW,CAAM,GAAA,EAAA,GAAG,CAAE,CAAA,CAAC,CAAC;AACvD,CAAC;AAEK,SAAU,QAAQ,CAAC,GAAW,EAAA;IAClC,MAAM,CAAC,KAAK,CAAC,CAAA,aAAA,EAAgB,WAAW,CAAM,GAAA,EAAA,GAAG,CAAE,CAAA,CAAC,CAAC;AACvD;;AC9BA;;;;;;;;;;;;;;;AAeG;AAmBH;MACa,oBAAoB,CAAA;AAE/B,IAAA,WAAA,CACU,QAAgB,EAChB,QAAyB,EACzB,aAAiD,EAAA;QAFjD,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QAChB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAoC;AAEzD,QAAA,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAE,CAAC;AAC7D,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,aAAa,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;SACnD;KACF;AACD,IAAA,QAAQ,CAAC,YAAqB,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;gBACrC,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,wBAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;qBACnD;yBAAM;wBACL,OAAO,CAAC,IAAI,CAAC,CAAC;qBACf;iBACF,EAAE,CAAC,CAAC,CAAC;AACR,aAAC,CAAC,CAAC;SACJ;AACD,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,KAAK,IAAG;YACrD,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,4BAA4B,EAAE;gBACxD,QAAQ,CACN,gEAAgE,CACjE,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC;aACb;iBAAM;AACL,gBAAA,QAAQ,CACN,oDAAoD;AAClD,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CACxB,CAAC;AACF,gBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC9B;AACH,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,sBAAsB,CAAC,QAA2B,EAAA;AAChD,QAAA,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC;KAC5C;AACD,IAAA,yBAAyB,CAAC,QAAwC,EAAA;AAChE,QAAA,IAAI,CAAC,aAAa;AACf,aAAA,GAAG,EAAE;aACL,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;aACpD,KAAK,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;KAChC;AACF;;ACnFD;;;;;;;;;;;;;;;AAeG;AAGI,MAAM,SAAS,GAAG,QAAQ;AAC1B,MAAM,YAAY,GAAG,WAAW;AAGhC,MAAM,aAAa,GAAG,SAAS;AAC/B,MAAM,YAAY,GAAG;;ACvB5B;;;;;;;;;;;;;;;AAeG;AAGI,IAAI,WAAqB,CAAC;AAC3B,SAAU,UAAU,CAAC,OAAiB,EAAA;IAC1C,WAAW,GAAG,OAAO,CAAC;AACxB,CAAC;AACD,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;ACtBlC;;;;;;;;;;;;;;;AAeG;SAEa,cAAc,CAC5B,GAAmB,EACnB,GAAW,EACX,GAAM,EAAA;IAEN,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACjB,QAAA,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KACnB;AACH;;ACzBA;;;;;;;;;;;;;;;AAeG;AAiCH,SAAS,gBAAgB,CACvB,QAAmC,EACnC,IAAU,EACV,MAAkB,EAAA;AAElB,IAAA,OAAO,SAAS,MAAM,GAAA;QACpB,OAAO;YACL,IAAI;AACJ,YAAA,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7B,gBAAA,eAAe,EAAE;oBACf,SAAS,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,SAAU;AACtD,oBAAA,GAAG,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE;AACtC,iBAAA;AACF,aAAA;AACD,YAAA,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,EAAE;YACtC,MAAM;SACP,CAAC;AACJ,KAAC,CAAC;AACJ,CAAC;MAEY,YAAY,CAAA;AAEvB,IAAA,WAAA,CAAoB,SAA+B,EAAA;QAA/B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;AACjD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;KAC3B;AACD,IAAA,KAAK,CACH,SAAiB,EACjB,SAAoB,EACpB,YAA6B,EAAA;AAE7B,QAAA,MAAM,GAAG,GAAyC;AAChD,YAAA,IAAI,EAAE,SAAS;YACf,SAAS;AACT,YAAA,OAAO,EAAE,SAAS;SACnB,CAAC;AACF,QAAA,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC7B,QAAA,MAAM,eAAe,GAAkC;YACrD,GAAG;AACH,YAAA,aAAa,EAAE,EAAE;YACjB,YAAY,EAAE,YAAY,IAAI,IAAI;AAClC,YAAA,SAAS,EAAE,IAAI;SAChB,CAAC;;QAEF,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAkC,CAAC;KAChE;AACD,IAAA,eAAe,CACb,QAAuC,EACvC,gBAAuD,EACvD,eAAqC,EACrC,YAA6B,EAAA;QAE7B,MAAM,GAAG,GAAG,WAAW,CAAC;YACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7B,YAAA,OAAO,EAAE,SAAS;AACnB,SAAA,CAAC,CAAC;QACH,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAGzC,CAAC;AACF,QAAA,MAAM,YAAY,GAAG;AACnB,YAAA,YAAY,EAAE,gBAAgB;AAC9B,YAAA,WAAW,EAAE,eAAe;SAC7B,CAAC;QACF,MAAM,WAAW,GAAG,MAAW;YAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;AAC7C,YAAA,YAAY,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAC5D,GAAG,IAAI,GAAG,KAAK,YAAY,CAC5B,CAAC;AACJ,SAAC,CAAC;QACF,IAAI,YAAY,IAAI,YAAY,CAAC,YAAY,KAAK,YAAY,EAAE;YAC9D,QAAQ,CAAC,uCAAuC,CAAC,CAAC;YAClD,IACE,CAAC,YAAY,CAAC,YAAY;iBACzB,YAAY,CAAC,YAAY;AACxB,oBAAA,YAAY,CACV,YAAY,CAAC,YAAY,CAAC,SAAS,EACnC,YAAY,CAAC,SAAS,CACvB,CAAC,EACJ;AACA,gBAAA,YAAY,CAAC,YAAY,GAAG,YAAY,CAAC;aAC1C;SACF;AACD,QAAA,IAAI,YAAY,CAAC,YAAY,KAAK,IAAI,EAAE;AACtC,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC;AAClD,YAAA,gBAAgB,CAAC;AACf,gBAAA,IAAI,EAAE,UAAU;AAChB,gBAAA,MAAM,EAAE,YAAY;AACpB,gBAAA,GAAG,EAAE,QAAqC;AAC1C,gBAAA,MAAM,EAAE,gBAAgB,CACtB,QAAqC,EACrC,YAAY,CAAC,YAAY,CAAC,IAAI,EAC9B,YAAY,CACb;AACD,gBAAA,SAAS,EAAE,YAAY,CAAC,YAAY,CAAC,SAAS;AAC/C,aAAA,CAAC,CAAC;YACH,IAAI,YAAY,CAAC,SAAS,KAAK,IAAI,IAAI,eAAe,EAAE;gBACtD,eAAe,CAAC,SAAS,CAAC,CAAC;aAC5B;SACF;AAED,QAAA,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC;AAC9B,YAAA,YAAY,EAAE,gBAAgB;AAC9B,YAAA,WAAW,EAAE,eAAe;YAC5B,WAAW;AACZ,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAC9B,YAAA,QAAQ,CACN,CACE,6BAAA,EAAA,QAAQ,CAAC,IACX,mBAAmB,IAAI,CAAC,SAAS,CAC/B,QAAQ,CAAC,SAAS,CACnB,CAAA,uBAAA,CAAyB,CAC3B,CAAC;YACF,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,QAAqC,CAAC,CAAC;;YAEzE,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAG,GAAG,CAAC,CAAC;SACpC;AACD,QAAA,OAAO,WAAW,CAAC;KACpB;AACD,IAAA,YAAY,CACV,QAAmC,EAAA;AAEnC,QAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,SAAS,EAAE;YAClC,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,gBAAgB,EACrB,CAA+C,6CAAA,CAAA,CAChD,CAAC;SACH;QACD,MAAM,GAAG,GAAG,WAAW,CAAC;YACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7B,YAAA,OAAO,EAAE,SAAS;AACnB,SAAA,CAAC,CAAC;QACH,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;AAC7C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CACvC,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,SAAS,CACnB,CAAC;QACF,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CACtB,GAAG,IAAG;YACJ,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;AACxC,YAAA,MAAM,MAAM,GAAiC;AAC3C,gBAAA,GAAG,GAAG;AACN,gBAAA,MAAM,EAAE,aAAa;AACrB,gBAAA,GAAG,EAAE,QAAQ;gBACb,MAAM,EAAE,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC;gBAC3D,SAAS;aACV,CAAC;AACF,YAAA,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,IAAG;AAChD,gBAAA,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC,aAAC,CAAC,CAAC;YACH,YAAY,CAAC,YAAY,GAAG;gBAC1B,IAAI,EAAE,GAAG,CAAC,IAAI;AACd,gBAAA,MAAM,EAAE,YAAY;gBACpB,SAAS;aACV,CAAC;AACF,YAAA,OAAO,MAAM,CAAC;SACf,EACD,GAAG,IAAG;AACJ,YAAA,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;AAC7B,YAAA,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,IAAG;AAChD,gBAAA,IAAI,YAAY,CAAC,WAAW,EAAE;AAC5B,oBAAA,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;iBAC/B;AACH,aAAC,CAAC,CAAC;AACH,YAAA,MAAM,GAAG,CAAC;AACZ,SAAC,CACF,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC;KACb;IACD,cAAc,CAAC,IAAY,EAAE,IAAY,EAAA;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACxC;AACF,CAAA;AACD,SAAS,YAAY,CAAC,IAAY,EAAE,IAAY,EAAA;AAC9C,IAAA,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAA,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,OAAO,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AAC3C;;ACvOA;;;;;;;;;;;;;;;AAeG;AAiBU,MAAA,iBAAiB,GAAG;IAC/B,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,WAAW;IACtB,iBAAiB,EAAE,mBAAmB;IACtC,cAAc,EAAE,gBAAgB;IAChC,mBAAmB,EAAE,qBAAqB;IAC1C,gBAAgB,EAAE,kBAAkB;;;ACtCtC;;;;;;;;;;;;;;;AAeG;AAMa,SAAA,UAAU,CACxB,aAAiC,EACjC,gBAAkC,EAAA;AAElC,IAAA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC;IAC3E,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,gBAAgB,CAAC;IACpD,MAAM,QAAQ,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM,CAAC;AAC/C,IAAA,MAAM,QAAQ,GAAG,IAAI,IAAI,oCAAoC,CAAC;AAC9D,IAAA,IAAI,OAAO,GAAG,CAAA,EAAG,QAAQ,CAAM,GAAA,EAAA,QAAQ,EAAE,CAAC;AAC1C,IAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,QAAA,OAAO,IAAI,CAAA,CAAA,EAAI,IAAI,CAAA,CAAE,CAAC;KACvB;AAAM,SAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QACtC,QAAQ,CAAC,iCAAiC,CAAC,CAAC;QAC5C,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,gBAAgB,EACrB,oCAAoC,CACrC,CAAC;KACH;IACD,OAAO,CAAA,EAAG,OAAO,CAAA,aAAA,EAAgB,OAAO,CAAA,WAAA,EAAc,QAAQ,CAAA,UAAA,EAAa,OAAO,CAAA,YAAA,EAAe,SAAS,CAAA,CAAE,CAAC;AAC/G,CAAC;AACe,SAAA,QAAQ,CAAC,GAAW,EAAE,MAAe,EAAA;IACnD,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,OAAO,GAAG,CAAC;KACZ;AACD,IAAA,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAA,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;AAC3B;;AChDA;;;;;;;;;;;;;;;AAeG;AAeH,IAAI,YAAY,GAAwB,UAAU,CAAC,KAAK,CAAC;AAIzD,SAAS,qBAAqB,CAC5B,WAAoB,EACpB,cAA6B,EAAA;AAE7B,IAAA,IAAI,GAAG,GAAG,cAAc,GAAG,WAAW,CAAC;AACvC,IAAA,IACE,cAAc,KAAK,iBAAiB,CAAC,IAAI;AACzC,QAAA,cAAc,KAAK,iBAAiB,CAAC,SAAS,EAC9C;AACA,QAAA,GAAG,IAAI,MAAM,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;KAC9C;SAAM,IAAI,WAAW,IAAI,cAAc,KAAK,iBAAiB,CAAC,SAAS,EAAE;QACxE,GAAG,IAAI,SAAS,CAAC;KAClB;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAMK,SAAU,OAAO,CACrB,GAAW,EACX,IAA6B,EAC7B,EAAE,MAAM,EAAmB,EAC3B,KAAoB,EACpB,WAA0B,EAC1B,aAA4B,EAC5B,WAAoB,EACpB,cAA6B,EAC7B,gBAAyB,EAAA;IAEzB,IAAI,CAAC,YAAY,EAAE;QACjB,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC,CAAC;KAC7E;AACD,IAAA,MAAM,OAAO,GAAgB;AAC3B,QAAA,cAAc,EAAE,kBAAkB;AAClC,QAAA,mBAAmB,EAAE,qBAAqB,CAAC,WAAW,EAAE,cAAc,CAAC;KACxE,CAAC;IACF,IAAI,WAAW,EAAE;AACf,QAAA,OAAO,CAAC,uBAAuB,CAAC,GAAG,WAAW,CAAC;KAChD;IACD,IAAI,KAAK,EAAE;AACT,QAAA,OAAO,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;KACrC;IACD,IAAI,aAAa,EAAE;AACjB,QAAA,OAAO,CAAC,qBAAqB,CAAC,GAAG,aAAa,CAAC;KAChD;IACD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,MAAM,YAAY,GAAgB;AAChC,QAAA,IAAI,EAAE,OAAO;AACb,QAAA,MAAM,EAAE,MAAM;QACd,OAAO;QACP,MAAM;KACP,CAAC;AACF,IAAA,IAAIC,uBAAkB,CAAC,GAAG,CAAC,IAAI,gBAAgB,EAAE;AAC/C,QAAA,YAAY,CAAC,WAAW,GAAG,SAAS,CAAC;KACtC;AAED,IAAA,OAAO,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC;SACnC,KAAK,CAAC,GAAG,IAAG;AACX,QAAA,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,KAAK,EACV,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAC1C,CAAC;AACJ,KAAC,CAAC;AACD,SAAA,IAAI,CAAC,OAAM,QAAQ,KAAG;QACrB,IAAI,YAAY,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI;AACF,YAAA,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;SACtC;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3D;AACD,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AACzC,QAAA,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE;YAC1B,QAAQ,CACN,kCAAkC,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAClE,CAAC;AACF,YAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;gBAC3B,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;aACxD;YACD,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SACjD;AACD,QAAA,OAAO,YAAY,CAAC;AACtB,KAAC,CAAC;SACD,IAAI,CAAC,GAAG,IAAG;QACV,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE;YACnC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC/C,YAAA,MAAM,QAAQ,GAAwC;gBACpD,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,IAAI,EAAE,GAAG,CAAC,IAAI;aACf,CAAC;YACF,MAAM,IAAI,yBAAyB,CACjC,8CAA8C,GAAG,WAAW,EAC5D,QAAQ,CACT,CAAC;SACH;AACD,QAAA,OAAO,GAAG,CAAC;AACb,KAAC,CAAC,CAAC;AACP,CAAC;AAID,SAAS,UAAU,CAAC,GAAkB,EAAA;AACpC,IAAA,IAAI,SAAS,IAAI,GAAG,EAAE;QACpB,OAAO,GAAG,CAAC,OAAO,CAAC;KACpB;AACD,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC7B;;AC7IA;;;;;;;;;;;;;;;AAeG;MAYU,aAAa,CAAA;AAYxB,IAAA,WAAA,CACE,OAA2B,EACnB,MAA2B,EAC3B,KAAc,EACd,YAA4C,EAC5C,gBAAoD,EAC5D,gBAA+C,EACvC,WAAc,GAAA,KAAK,EACnB,cAAgC,GAAA,iBAAiB,CAAC,IAAI,EAAA;QANtD,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqB;QAC3B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAS;QACd,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAgC;QAC5C,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAoC;QAEpD,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACnB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAwC;QAnBxD,IAAK,CAAA,KAAA,GAAG,EAAE,CAAC;QAEX,IAAS,CAAA,SAAA,GAAG,GAAG,CAAC;QAChB,IAAc,CAAA,cAAA,GAAG,EAAE,CAAC;QACpB,IAAO,CAAA,OAAA,GAAG,IAAI,CAAC;QACf,IAAQ,CAAA,QAAA,GAAG,GAAG,CAAC;QAEf,IAAY,CAAA,YAAA,GAAkB,IAAI,CAAC;QACnC,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;QACrC,IAAU,CAAA,UAAA,GAAkB,IAAI,CAAC;QACjC,IAAgB,CAAA,gBAAA,GAAG,KAAK,CAAC;;AA6HjC,QAAA,IAAA,CAAA,WAAW,GAGkC,CAC3C,SAAiB,EACjB,IAAO,KACL;AACF,YAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;;YAG9C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAC9B,OAAO,CACL,QAAQ,CAAC,CAAA,EAAG,IAAI,CAAC,WAAW,CAAA,aAAA,CAAe,EAAE,IAAI,CAAC,MAAM,CAAC,EACzD;AACE,gBAAA,IAAI,EAAE,CAAY,SAAA,EAAA,IAAI,CAAC,QAAQ,cAAc,IAAI,CAAC,SAAS,CAAA,UAAA,EAAa,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,cAAc,CAAE,CAAA;AAC7H,gBAAA,aAAa,EAAE,SAAS;AACxB,gBAAA,SAAS,EAAE,IAAI;aAChB,EACD,eAAe,EACf,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,gBAAgB,CACtB,CACF,CAAC;AACF,YAAA,OAAO,QAAQ,CAAC;AAClB,SAAC,CAAC;AACF,QAAA,IAAA,CAAA,cAAc,GAG+B,CAC3C,YAAoB,EACpB,IAAO,KACL;AACF,YAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAK;AACrC,gBAAA,OAAO,OAAO,CACZ,QAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAA,gBAAA,CAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,EAC5D;AACE,oBAAA,IAAI,EAAE,CAAY,SAAA,EAAA,IAAI,CAAC,QAAQ,cAAc,IAAI,CAAC,SAAS,CAAA,UAAA,EAAa,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,cAAc,CAAE,CAAA;AAC7H,oBAAA,aAAa,EAAE,YAAY;AAC3B,oBAAA,SAAS,EAAE,IAAI;iBAChB,EACD,eAAe,EACf,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,gBAAgB,CACtB,CAAC;AACJ,aAAC,CAAC,CAAC;AACH,YAAA,OAAO,UAAU,CAAC;AACpB,SAAC,CAAC;QAzKA,IAAI,gBAAgB,EAAE;AACpB,YAAA,IAAI,OAAO,gBAAgB,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC7C,gBAAA,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC;aACpC;AACD,YAAA,IAAI,OAAO,gBAAgB,CAAC,UAAU,KAAK,WAAW,EAAE;AACtD,gBAAA,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,UAAU,CAAC;aAC5C;AACD,YAAA,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC;SACpC;AACD,QAAA,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QACrE,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;SAC3B;QACD,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SACzB;AACD,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,gBAAgB,EACrB,0BAA0B,CAC3B,CAAC;SACH;AACD,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;AAChC,QAAA,IAAI,CAAC,YAAY,EAAE,sBAAsB,CAAC,KAAK,IAAG;AAChD,YAAA,QAAQ,CAAC,CAAA,qBAAA,EAAwB,KAAK,CAAA,CAAE,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC5B,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,MAAM,IAAG;AACrD,YAAA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;AACzB,YAAA,QAAQ,CAAC,CAAA,+BAAA,EAAkC,KAAK,CAAA,CAAE,CAAC,CAAC;AACpD,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC9B,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,UAAU,CACf;YACE,SAAS,EAAE,IAAI,CAAC,cAAc;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,SAAS,EAAE,IAAI,CAAC,QAAQ;YACxB,OAAO,EAAE,IAAI,CAAC,YAAY;SAC3B,EACD,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CACjE,CAAC;KACH;AACD,IAAA,WAAW,CAAC,IAAY,EAAE,IAAa,EAAE,QAAkB,EAAA;AACzD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACnB;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACnC,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;SACzB;KACF;AACD,IAAA,cAAc,CAAC,QAAuB,EAAA;AACpC,QAAA,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;KAC9B;AAED,IAAA,MAAM,WAAW,CAAC,UAAU,GAAG,KAAK,EAAA;AAClC,QAAA,IAAI,cAAc,GAA2B,IAAI,OAAO,CAAC,OAAO,IAC9D,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAC3B,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,KAAK,CAAC;SACvE;AACD,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,cAAc,GAAG,IAAI,CAAC,YAAY;AAC/B,iBAAA,QAAQ,iBAAiB,UAAU,CAAC;iBACpC,IAAI,CAAC,IAAI,IAAG;gBACX,IAAI,CAAC,IAAI,EAAE;AACT,oBAAA,OAAO,IAAI,CAAC;iBACb;AACD,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,YAAY,CAAC;AAC3B,aAAC,CAAC,CAAC;SACN;aAAM;AACL,YAAA,cAAc,GAAG,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;SACtD;AACD,QAAA,OAAO,cAAc,CAAC;KACvB;AAED,IAAA,aAAa,CAAC,SAAwB,EAAA;AACpC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7B;AAED,IAAA,SAAS,CACP,cAA2D,EAC3D,KAAK,GAAG,KAAK,EAAA;QAEb,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;aAC3B,IAAI,CAAC,GAAG,IAAG;AACV,YAAA,UAAU,GAAG,IAAI,CAAC,UAAU,KAAK,GAAG,CAAC;AACrC,YAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AACtB,YAAA,OAAO,GAAG,CAAC;AACb,SAAC,CAAC;aACD,IAAI,CAAC,cAAc,CAAC;aACpB,KAAK,CAAC,GAAG,IAAG;;YAEX,IACE,MAAM,IAAI,GAAG;AACb,gBAAA,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY;AAC9B,gBAAA,CAAC,KAAK;AACN,gBAAA,UAAU,EACV;gBACA,QAAQ,CAAC,8BAA8B,CAAC,CAAC;gBACzC,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;aAC7C;AACD,YAAA,MAAM,GAAG,CAAC;AACZ,SAAC,CAAC,CAAC;KACN;AA4DD,IAAA,iBAAiB,CAAC,aAA4B,EAAA;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;KACrC;AACF;;AC/ND;;;;;;;;;;;;;;;AAeG;AAqCH;;;;;;AAMG;SACa,WAAW,CACzB,UAAuB,EACvB,YAAoB,EACpB,SAAqB,EAAA;IAErB,UAAU,CAAC,cAAc,EAAE,CAAC;AAC5B,IAAA,MAAM,GAAG,GAAiC;AACxC,QAAA,WAAW,EAAE,UAAU;AACvB,QAAA,IAAI,EAAE,YAAY;AAClB,QAAA,OAAO,EAAE,YAAY;AACrB,QAAA,SAAS,EAAE,SAAsB;KAClC,CAAC;AACF,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;AAEG;MACU,eAAe,CAAA;AAE1B,IAAA,WAAA,CAAoB,UAAgC,EAAA;QAAhC,IAAU,CAAA,UAAA,GAAV,UAAU,CAAsB;QAD5C,IAAS,CAAA,SAAA,GAA4B,EAAE,CAAC;KACQ;AACxD,IAAA,eAAe,CACb,WAAyC,EAAA;AAEzC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAC3C,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,SAAS,CACtB,CAAC;QACF,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAG;AACvC,YAAA,MAAM,GAAG,GAAoC;gBAC3C,GAAG,GAAG;AACN,gBAAA,MAAM,EAAE,aAAa;AACrB,gBAAA,GAAG,EAAE,WAAW;AAChB,gBAAA,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,EAAE;aACvC,CAAC;AACF,YAAA,OAAO,GAAG,CAAC;AACb,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5B,MAAM,aAAa,GAAG,OACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;AAC1E,QAAA,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAC1C,QAAA,OAAO,cAAc,CAAC;KACvB;AACF,CAAA;AAiBD;;;;AAIG;AACG,SAAU,eAAe,CAC7B,WAAyC,EAAA;IAEzC,OAAO,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AAC/E;;AChIA;;;;;;;;;;;;;;;AAeG;AAqDH,MAAM,uCAAuC,GAC3C,qCAAqC,CAAC;AAExC;;;;;AAKG;AACG,SAAU,YAAY,CAAC,QAAgB,EAAA;AAC3C,IAAA,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnD,IAAA,MAAM,QAAQ,GAAG,QAAQ,KAAK,OAAO,CAAC;AACtC,IAAA,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjD,IAAA,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;IAClC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;AAC9C,CAAC;AAQD;;AAEG;MACU,WAAW,CAAA;;AAatB,IAAA,WAAA,CACkB,GAAgB;;IAEf,kBAAsC,EACtC,aAAiD,EACjD,iBAA0D,EAAA;QAJ3D,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAEf,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAoB;QACtC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAoC;QACjD,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAyC;QAf7E,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;QACnB,IAAY,CAAA,YAAA,GAAG,KAAK,CAAC;QAKrB,IAAoB,CAAA,oBAAA,GAAY,KAAK,CAAC;AACtC,QAAA,IAAA,CAAA,cAAc,GAAkB,iBAAiB,CAAC,IAAI,CAAC;QAUrD,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE;YACjD,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YAClE,IAAI,IAAI,EAAE;gBACR,QAAQ,CAAC,mCAAmC,CAAC,CAAC;AAC9C,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;aAC7C;SACF;KACF;;IAED,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAClC;KACF;AACD,IAAA,iBAAiB,CAAC,aAA4B,EAAA;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;AACpC,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;SAClD;KACF;IACD,OAAO,GAAA;AACL,QAAAC,0BAAsB,CACpB,IAAI,CAAC,GAAG,EACR,cAAc,EACd,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACnC,CAAC;AACF,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;;IAGD,WAAW,GAAA;AACT,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC;AACtB,QAAA,OAAO,IAAI,CAAC;KACb;;IAGD,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;YACtC,QAAQ,CAAC,2DAA2D,CAAC,CAAC;AACtE,YAAA,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC;SACtC;AAED,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,kBAAkB,GAAG,IAAI,oBAAoB,CAChD,IAAI,CAAC,GAAG,CAAC,IAAI,EACb,IAAI,CAAC,GAAG,CAAC,OAAO,EAChB,IAAI,CAAC,aAAa,CACnB,CAAC;SACH;AACD,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,qBAAqB,CACrD,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,iBAAiB,CACvB,CAAC;SACH;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,eAAe,CACxC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EACvB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EACtB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,sBAAsB,EAC3B,SAAS,EACT,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,cAAc,CACpB,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,UAAU,CAAC,WAAW,CACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAC3B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAClC,CAAC;SACH;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC9D;;AAGD,IAAA,cAAc,CAAC,gBAAkC,EAAA;QAC/C,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,EACnE;YACA,QAAQ,CAAC,4CAA4C,CAAC,CAAC;YACvD,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,mBAAmB,EACxB,2CAA2C,CAC5C,CAAC;SACH;AACD,QAAA,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;AAC1C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB;AACF,CAAA;AAED;;;;;AAKG;AACa,SAAA,wBAAwB,CACtC,iBAAmC,EACnC,iBAAmC,EAAA;AAEnC,IAAA,QACE,iBAAiB,CAAC,IAAI,KAAK,iBAAiB,CAAC,IAAI;AACjD,QAAA,iBAAiB,CAAC,IAAI,KAAK,iBAAiB,CAAC,IAAI;AACjD,QAAA,iBAAiB,CAAC,UAAU,KAAK,iBAAiB,CAAC,UAAU,EAC7D;AACJ,CAAC;AAED;;;;;;AAMG;AACG,SAAU,0BAA0B,CACxC,EAAe,EACf,IAAY,EACZ,IAAa,EACb,UAAU,GAAG,KAAK,EAAA;;AAGlB,IAAA,IAAID,uBAAkB,CAAC,IAAI,CAAC,EAAE;AAC5B,QAAA,KAAKE,eAAU,CAAC,CAAA,QAAA,EAAW,IAAI,CAAG,EAAA,IAAI,GAAG,CAAI,CAAA,EAAA,IAAI,EAAE,GAAG,EAAE,CAAA,CAAE,CAAC,CAAC;AAC5D,QAAAC,yBAAoB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;KAC5C;IACD,EAAE,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;AAChD,CAAC;AAgBe,SAAA,cAAc,CAC5B,YAA2C,EAC3C,eAAiC,EAAA;AAEjC,IAAA,IAAIP,KAAgB,CAAC;AACrB,IAAA,IAAI,SAA0B,CAAC;AAC/B,IAAA,IAAI,UAAU,IAAI,YAAY,EAAE;QAC9B,SAAS,GAAG,YAAY,CAAC;QACzBA,KAAG,GAAGQ,UAAM,EAAE,CAAC;KAChB;SAAM;QACL,SAAS,GAAG,eAAgB,CAAC;QAC7BR,KAAG,GAAG,YAAY,CAAC;KACpB;AAED,IAAA,IAAI,CAACA,KAAG,IAAI,MAAM,CAAC,IAAI,CAACA,KAAG,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QACzCA,KAAG,GAAGQ,UAAM,EAAE,CAAC;KAChB;IACD,MAAM,QAAQ,GAAGC,gBAAY,CAACT,KAAG,EAAE,cAAc,CAAC,CAAC;IACnD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC7C,IAAA,IAAI,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;QACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;QACzD,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AAChD,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QACrD,IAAI,YAAY,EAAE;YAChB,QAAQ,CAAC,0BAA0B,CAAC,CAAC;AACrC,YAAA,OAAO,UAAU,CAAC;SACnB;KACF;IACD,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAE7B,QAAQ,CAAC,mCAAmC,CAAC,CAAC;;IAE9C,OAAO,QAAQ,CAAC,UAAU,CAAC;AACzB,QAAA,kBAAkB,EAAE,UAAU;AAC9B,QAAA,OAAO,EAAE,SAAS;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;AAKG;AACG,SAAU,iBAAiB,CAAC,SAA0B,EAAA;IAC1D,MAAM,MAAM,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACpD,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;KACzE;AACD,IAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;AACrB,QAAA,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;YAC/D,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAG,EAAA,KAAK,CAAW,SAAA,CAAA,CAAC,CAAC;SACxE;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;AAIG;AACG,SAAU,SAAS,CAAC,WAAwB,EAAA;AAChD,IAAA,OAAO,WAAW,CAAC,OAAO,EAAE,CAAC;;AAE/B;;AC3UA;;;;;;;;;;;;;;;AAeG;AAeG,SAAU,mBAAmB,CAAC,OAAgB,EAAA;IAClD,aAAa,CAACU,eAAW,CAAC,CAAC;AAC3B,IAAAC,sBAAkB,CAChB,IAAIC,mBAAS,CACX,cAAc,EACd,CAAC,SAAS,EAAE,EAAE,kBAAkB,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAI;QACvD,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAG,CAAC;QACzD,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAC5D,MAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QACrE,IAAI,OAAO,GAAG,OAA0B,CAAC;QACzC,IAAI,QAAQ,EAAE;AACZ,YAAA,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;SAChC;AACD,QAAA,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE;YAC1B,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,gBAAgB,EACrB,mFAAmF,CACpF,CAAC;SACH;QACD,OAAO,IAAI,WAAW,CACpB,GAAG,EACH,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,SAAU,EAAE,EACjD,YAAY,EACZ,gBAAgB,CACjB,CAAC;AACJ,KAAC,sCAEF,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;AACF,IAAAC,mBAAe,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;;AAExC,IAAAA,mBAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;AACrD;;AC9DA;;;;;;;;;;;;;;;AAeG;AA0DH;;;;AAIG;AACG,SAAU,YAAY,CAC1B,QAAmC,EAAA;IAEnC,OAAO,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AACnE,CAAC;AAwBD;;;;;;;AAOG;AACG,SAAU,QAAQ,CACtB,UAAuB,EACvB,SAAiB,EACjB,SAAqB,EACrB,YAA2C,EAAA;IAE3C,UAAU,CAAC,cAAc,EAAE,CAAC;IAC5B,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IACnE,OAAO;AACL,QAAA,WAAW,EAAE,UAAU;AACvB,QAAA,OAAO,EAAE,SAAS;AAClB,QAAA,IAAI,EAAE,SAAS;QACf,SAAS;KACV,CAAC;AACJ,CAAC;AACD;;;;AAIG;AACG,SAAU,UAAU,CACxB,aAA6C,EAAA;AAE7C,IAAA,MAAM,EACJ,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,EAC9C,GAAG,aAAa,CAAC;IAClB,OAAO,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACpE;;AC7IA;;;;;;;;;;;;;;;AAeG;AAaH;;;;;;;;;AASG;AACG,SAAU,YAAY,CAC1B,eAAgC,EAChC,QAAkC,EAClC,IAAgB,EAChB,YAAsB,EAAA;AAEtB,IAAA,IAAI,UAAuB,CAAC;AAC5B,IAAA,IAAI,QAAmB,CAAC;AACxB,IAAA,IAAI,QAAQ,IAAI,gBAAgB,IAAI,QAAQ,EAAE;QAC5C,UAAU,GAAG,QAAuB,CAAC;QACrC,QAAQ,GAAG,IAAI,CAAC;KACjB;SAAM;AACL,QAAA,UAAU,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;QAC7C,QAAQ,GAAG,QAAqB,CAAC;KAClC;IACD,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,IAAI,YAAY,CAAC,EAAE;QAC9C,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;KAC1E;IACD,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5C;;ACzDA;;;;;;;;;;;;;;;AAeG;AA0CH;;;;;;;AAOG;AACG,SAAU,SAAS,CACvB,0BAEkC,EAClC,gBAEyC,EACzC,OAA6B,EAC7B,UAAmC,EAAA;AAEnC,IAAA,IAAI,GAA8B,CAAC;AACnC,IAAA,IAAI,YAAwC,CAAC;AAC7C,IAAA,IAAI,SAAS,IAAI,0BAA0B,EAAE;QAC3C,MAAM,aAAa,GACjB,0BAA0B,CAAC;QAC7B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,aAAa,CAAC;AAClD,QAAA,YAAY,GAAG;YACb,IAAI;YACJ,MAAM;YACN,SAAS;SACV,CAAC;AACF,QAAA,GAAG,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;KACjC;SAAM;QACL,GAAG,GAAG,0BAA0B,CAAC;KAClC;IACD,IAAI,QAAQ,GAAsD,SAAS,CAAC;AAC5E,IAAA,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;QAC1C,QAAQ,GAAG,gBAAgB,CAAC;KAC7B;SAAM;AACL,QAAA,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACnC,QAAA,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACjC,QAAa,gBAAgB,CAAC,UAAU,CAAC;KAC1C;IACD,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;KAC1E;AACD,IAAA,OAAO,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,eAAe,CAClD,GAAG,EACH,QAAQ,EACR,OAAO,EACP,YAAY,CACb,CAAC;AACJ;;AC3GA;;;;AAIG;AAwBH,mBAAmB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;"}