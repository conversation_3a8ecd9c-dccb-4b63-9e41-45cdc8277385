{"name": "@firebase/installations", "version": "0.6.19", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "module": "dist/esm/index.esm.js", "browser": "dist/esm/index.esm.js", "exports": {".": {"types": "./dist/installations-public.d.ts", "require": "./dist/index.cjs.js", "default": "./dist/esm/index.esm.js"}, "./package.json": "./package.json"}, "typings": "./dist/installations-public.d.ts", "license": "Apache-2.0", "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c && yarn api-report", "build:deps": "lerna run --scope @firebase/installations --include-dependencies build", "build:release": "yarn build && yarn typings:public", "dev": "rollup -c -w", "test": "yarn type-check && yarn test:karma && yarn lint", "test:ci": "node ../../scripts/run_tests_in_ci.js", "test:karma": "karma start", "test:debug": "karma start --browsers=Chrome --auto-watch", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "type-check": "tsc -p . --noEmit", "serve": "yarn serve:build && yarn serve:host", "serve:build": "rollup -c test-app/rollup.config.js", "serve:host": "http-server -c-1 test-app", "api-report": "api-extractor run --local --verbose", "doc": "api-documenter markdown --input temp --output docs", "build:doc": "yarn build && yarn doc", "typings:public": "node ../../scripts/build/use_typings.js ./dist/installations-public.d.ts", "typings:internal": "node ../../scripts/build/use_typings.js ./dist/src/index.d.ts"}, "repository": {"directory": "packages/installations", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "devDependencies": {"@firebase/app": "0.14.0", "rollup": "2.79.2", "@rollup/plugin-commonjs": "21.1.0", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "16.0.0", "rollup-plugin-typescript2": "0.36.0", "rollup-plugin-uglify": "6.0.4", "typescript": "5.5.4"}, "peerDependencies": {"@firebase/app": "0.x"}, "dependencies": {"@firebase/util": "1.13.0", "@firebase/component": "0.7.0", "idb": "7.1.1", "tslib": "^2.1.0"}}